<!--pages/couponcenter/getCoupon/getCoupon.wxml-->
<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<!--  <view class="img" wx:if="{{type}}">
    <image lazy-load wx:if="{{type=='ok'}}" style="width: 260rpx;max-height:300rpx;" mode="widthFix" src="https://m.sanfu.com/wechat/image/coupon/winner.png"></image>
    <image lazy-load wx:if="{{type=='err'}}" style="width: 260rpx;max-height:300rpx;" mode="widthFix" src="https://m.sanfu.com/wechat/image/coupon/loser.png"></image>
    <image lazy-load wx:if="{{type=='exp'}}" style="width: 260rpx;max-height:300rpx;" mode="widthFix" src="https://m.sanfu.com/wechat/image/coupon/cryer.png"></image>
  </view> -->
<view wx:if="{{showStatus==3}}" class="location-box">
  <text>需要定位获取区域活动</text>
  <view hover-class="hover" hover-start-time="20" hover-stay-time="100" bindtap="getdshoid">开启定位</view>
</view>
<view class="container" wx:else>
  <image wx:if="{{showStatus<=1}}" src="{{utils.jpg2jpeg(bgUrl)}}" style="width: 100%;" mode="widthFix"></image>
  <image wx:else src="https://m.sanfu.com/wechat/image/coupon/loser.png" style="width:400rpx;margin: 100rpx auto 30rpx;" mode="widthFix"></image>
  <view wx:if="{{showStatus==1}}" style="background:#fff;text-align: center">
    <view style="font-size: 28rpx;padding:30rpx;color: #8f8f8f;margin-top: 0.5em;">{{des}}</view>
    <view class="btn" bindtap="sendGift">
      一键领取
    </view>
  </view>
  <view wx:if="{{showStatus==0}}" style="font-size: 1.2em;color: #67bb71;font-weight: 700;margin:48rpx auto 12rpx">成功领取礼包！</view>
  <view wx:if="{{showStatus==2}}" style="font-size: 1em;color: #7d7d7d;font-weight: 700;margin:48rpx auto 12rpx;padding: 0 30rpx;">{{statusStr||'该礼包已失效！'}}</view>
  <!-- <view style="padding: 1em;text-align: center;width: 100%;margin: 1em auto;">该礼包空空如也！</view> -->

  <view wx:if="{{showStatus<=1}}" class="coupon" wx:for="{{couponList}}" wx:key="couponTypeID">
    <view class="content">
      <view class="top">
        <text>{{item.name}}</text>
        <text wx:if="{{(item.useMethod||item.useType>=0)&&!limitShop}}" style="color:#FF8632;">
          <block wx:if="{{item.useMethod}}">使用渠道：{{item.useMethod}}</block>
          <block wx:if="{{item.useType==0}}">使用渠道：仅限门店</block>
          <block wx:if="{{item.useType==1}}">使用渠道：仅限官网</block>
          <block wx:if="{{item.useType==2}}">使用渠道：官网&门店</block>
        </text>
      </view>
      <view select-name="conponmoney" style="color:#FF8632;font-weight:700;">
        <block wx:if="{{item.couponType==2}}">
          <text style="font-size:2em;vertical-align: sub;font-weight:900;">礼品券</text>
        </block>
        <block wx:else>
          <text style="font-size:0.7em;">￥</text>
          <text style="font-size:2em;vertical-align: sub;font-weight:900;">{{item.money}}</text>
          <text style="font-size:0.7em;font-weight:400;"> 满{{item.leastMoney}}元使用</text>
        </block>
      </view>
      <view wx:if="{{item.fixedTrem==1&&item.validEnd}}" style="font-size:22rpx;color:#afafaf;">有效期至：{{item.validEnd&&utils.substring2(item.validEnd,0,10)}}
      </view>
      <view wx:elif="{{item.nextDay>0&&item.validDay}}" style="margin-top: -0.4em;line-height: 1;font-size:22rpx;color:#afafaf;">有效期：领取{{item.nextDay}}天后生效，有效期{{item.validDay}}天
      </view>
      <view wx:elif="{{item.validDay}}" style="margin-top: -0.4em;line-height: 1;font-size:22rpx;color:#afafaf;">有效期：领取后{{item.validDay}}天内有效
      </view>
    </view>

    <view wx:if="{{item.memo}}" bindtap="toShowMemo" data-memo="{{item.memo}}" style="padding:0.5em;padding-bottom:1.5em;position: absolute;right: 1em;top: 1em;">
      <text style="float:right;font-size:0.8em;font-weight:400;display:inline-block;color:#fff;background:#FF8632;padding:0.3em 0.6em;margin-top:1.1em;margin-right:0.5em;border-radius:0.6em;">使用说明></text>
    </view>
  </view>

  <view wx:if="{{showStatus<=1}}" class="coupon" wx:for="{{postCardList}}" wx:key="couponTypeID">
    <view class="content">
      <view class="top">
        <text>{{item.freeName}}</text>
        <text style="color:#FF8632;">使用该卡全场免邮费</text>
      </view>
      <view style="color:#FF8632;height: 84rpx;line-height:84rpx;font-size:1.6em;font-weight: 900;">包邮卡</view>
      <view style="margin-top: -0.35em;line-height: 1;font-size:22rpx;color:#afafaf;">有效期：从领取起{{item.validDay}}天有效</view>
    </view>
  </view>
  <view style="height:2em"></view>
</view>
<view wx:if="{{showMemo&&memo}}" style="flex-direction:column;display:flex;z-index:999;color:#fff;position:fixed;width:100%;padding:2em;font-size:0.7em;height:100%;background:rgba(0, 0, 0, 0.85);top:0;">
  <view style="flex:1;width:100%;overflow-y:scroll;color:#fff;font-size: 1.3em;">{{memo}}</view>
  <view bindtap="toShowMemo" style="width:50%;margin:1em auto;color:#fff;border:1px solid #fff; border-radius:0.3em;font-size:1.35em;text-align:center;padding:0.5em;">知道了</view>
</view>


<auth id="sanfu-auth" showCancel="{{false}}"></auth>
<!-- <view style="width: 600rpx;word-break: break-all;">{{opt}}</view> -->
<sf-loading wx:if="{{showLoading}}" full></sf-loading>
