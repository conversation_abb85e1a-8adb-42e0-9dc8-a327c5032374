const app = getApp()
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    shopInfo: {
      type: null,
      value: ''
    },
    pid: {
      type: null,
      value: ''
    },
    menuButton: {
      type: null,
      value: ''
    },
    hidePop: {
      type: Boolean,
      value: false
    },
  },
  data: {
    currentIndex: -1,
    list: {}
  },
  attached() {
    this.load()
  },
  detached() {
    this.isload = false
    app.$bus.off('initSubPage')
    app.$bus.off('setSubPageIndex')
    app.$bus.off('setSubTop')
    app.$bus.off('clearSubPage')
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {
      if (this.isload) return
      this.load()
    },
    hide: function() {
      this.isload = false
      app.$bus.off('initSubPage')
      app.$bus.off('setSubPageIndex')
      app.$bus.off('setSubTop')
      app.$bus.off('clearSubPage')
    },
    resize: function() {}
  },
  methods: {
    load() {
      this.isload = true
      app.$bus.on('initSubPage', (list, index) => {
        console.log('this.second',this.second)
        let target = `list[${index}]`
        this.data.list[index] = list
        if (!this.second) {
          this.second = 1
          const firstScreenList = list.filter((item, index) => index <= 5)
          const restList = list.filter((item, index) => index > 5)
          this.setData({
            [target]: firstScreenList,
            currentIndex: index
          })

          // 2. 延迟补齐剩余区块
          wx.nextTick(() => {
            setTimeout(() => {
              this.setData({
                [target]: firstScreenList.concat(restList)
              })
            }, 100) // 100ms 可
          })
        } else {
          wx.nextTick(() => {
            this.setData({
              [target]: list,
              currentIndex: index
            })
          }) // 100ms 可
        }
        console.log('list',this.data.list);
        this.checkData(index, list)
        const barH = app.$bus.emit('getColumnPageH') || 0
        this.barH = barH
      })
      app.$bus.on('setSubPageIndex', (data, top) => {
        console.log('setSubPageIndex', data)
        this.setData({
          currentIndex: data.key,
          isTop: top
        })
        if (data.linkType != 2 && top)
          try {
            const pages = getCurrentPages()
            const page = pages[pages.length - 1]
            page.swipeChange('#202020')
          } catch (e) {
            //TODO handle the exception
          }
        if (!this.data.list[data.key])
          this.getDecorationInfo(data)
        if (this.subTop) {
          this.triggerEvent('scrollTo', {
            selector: '#subPage >>> #top',
            duration: 0,
            offsetTop: -1 * app.util.rpx2px(this.barH) + 1
          })
        }
      })
      app.$bus.on('setSubTop', e => {
        this.subTop = e
      })
      app.$bus.on('clearSubPage', () => {
        this.data.list = {}
        console.log('list',this.data.list);
      })
    },
    async getDecorationInfo(data) {
      console.log('getDecorationInfogetDecorationInfo')
      let target = `list[${data.key}]`
      let url = 'ms-sanfu-wap-goods/activePage'
      let res
      let getData = {
        configId: data.link,
        shoId: app.local.get('dsho_id') || 'GZQ'
      }
      if (this.preview) {
        url = 'ms-sanfu-wap-goods/draftPreview'
        getData.homepageDecCfgId = data.link
        res = await app.reqPost(url, getData)
      } else {
        res = await app.reqGet(url, getData)
      }

      if (res.success && res.data.unitList && Array.isArray(res.data.unitList) && res.data.unitList.length) {
        let unitList = [...res.data.unitList]
        delete res.data.unitList
        // 去navbar
        unitList.splice(0, 1)
        unitList.map(item => {
          delete item.unitSeqNo
          item.unitStyle.unitName = item.unitName
          delete item.unitName
          if (item.unitIsLimit === 1) item.hide = true
          delete item.unitIsLimit
          if (item.unitKey === 'columnPagination') {
            item.delete = 1
          }
        })
        this.data.list[data.key] = unitList
        this.checkHide(1, data.key)
        // 使用独立方法处理 unitList
        unitList = formatUnitList(unitList)
        // unitList 已经处理好

        const firstScreenList = unitList.filter((item, index) => index <= 5)
        const restList = unitList.filter((item, index) => index > 5)

        this.setData({
          [target]: firstScreenList
        })

        // 2. 延迟补齐剩余区块
        wx.nextTick(() => {
          setTimeout(() => {
            this.setData({
              [target]: firstScreenList.concat(restList)
            })
          }, 100) // 100ms 可
        })
        this.checkData(data.key, unitList)
      } else {}
    },
    async checkData(index, data) {
      try {
        // 人群包获取
        let packageIds = []
        let a = JSON.stringify(this.data.list[index])
        if (a.indexOf('"isGroup2":') > -1) {
          this.hasGroup2 = true
        }
        // 人群包获取
        a = a.split(`"packageIds":`)
        a.forEach(str => {
          if (str[0] == '"') {
            str = str.substring(1)
            str = str.split('"')[0]
            if (str) packageIds.push(str)
          }
        })
        packageIds = [...new Set(packageIds.join().split(','))].join()
        this.packageIds = packageIds
        // this.get
      } catch (e) {
        //TODO handle the exception
      }

      await Promise.all([this.getUserInfo(), this.getQYShop(), this.getPackageIds()])

      this.checkHide(0, index)
    },
    async getUserInfo(e) {
      if (this.has_getUserInfo) return
      this.has_getUserInfo = 1
      await app.waitSid()
      const res = await app.reqGet('ms-sanfu-wechat-customer/customer/superQrCode/getCustomerInfo', {
        sid: wx.getStorageSync('sid')
      })
      if (res.success) {
        this.data.userInfo = {
          ...this.data.userInfo,
          ...res.data
        }
      } else {
        app.util.reqFail(res)
      }
    },
    async getPackageIds() {
      if (!this.packageIds) return
      await app.waitSid()
      // 过滤掉shopInfo.packageIds中已有的
      const existingPackageIds = this.data.shopInfo.packageIds || []
      const currentPackageIds = this.packageIds.split(',').filter(id => id.trim())
      const filteredPackageIds = currentPackageIds.filter(id => !existingPackageIds.includes(id))

      if (filteredPackageIds.length === 0) return

      this.packageIds = filteredPackageIds.join(',')
      const res = await app.reqGet('ms-sanfu-wap-common/common/getMeetPackage', {
        sid: wx.getStorageSync('sid'),
        seqNos: this.packageIds
      })
      // this.data.shopInfo.packageIds = []
      if (res.success && res.data) {
        if (!this.data.shopInfo.packageIds) this.data.shopInfo.packageIds = []
        this.data.shopInfo.packageIds = [...this.data.shopInfo.packageIds, res.data.split(',')]
      }
    },
    async getQYShop() {
      if (this.has_getQYShop) return
      this.has_getQYShop = 1
      this.data.shopInfo.isGroup = app.sf.getUserProperty('qy_shop_id') ? 1 : 0
      if (!this.hasGroup2) return
      const res = await app.getQYShop()
      this.data.shopInfo.isGroup2 = res ? 1 : 0
    },

    checkHide(noSet, index) {
      const target = `list[${index}]`
      const list = this.data.list[index]
      let targetArr = []
      const shopInfo = this.data.shopInfo
      const now = Date.now() + wx.getStorageSync('differenceTime')
      shopInfo.now = now
      list.map((item, index) => {
        const unitLimit = item.unitLimit
        if (item.hide) {
          // 校验大学生
          if ((unitLimit.isS == 1 && this.data.userInfo.cusTypeNo == 2) || (unitLimit.isS == 2 && this.data.userInfo.cusTypeNo != 2)) {
            delete unitLimit.isS
          }
          // 会员卡
          if (unitLimit.mL && unitLimit.mL.indexOf(this.data.userInfo.cardLevelId) > -1) {
            delete unitLimit.mL
          }
          // 主体控制
          if (unitLimit.orgId && shopInfo.orgId && unitLimit.orgId == shopInfo.orgId) {
            delete unitLimit.orgId
          }
          // 展示店
          if (unitLimit.shops && shopInfo.dsho_id && unitLimit.shops.indexOf(shopInfo.dsho_id) > -1) {
            delete unitLimit.shops
          }

          // 人群包
          if (unitLimit.packageIds && shopInfo.packageIds && shopInfo.packageIds.some(e => unitLimit.packageIds.indexOf(e) > -1)) {
            delete unitLimit.packageIds
          }

          // 企微会员isGroup 1 已加入 2未加入
          if ((unitLimit.isGroup == 1 && shopInfo.isGroup) || (unitLimit.isGroup == 2 && !shopInfo.isGroup)) {
            delete unitLimit.isGroup
          }
          // 时间
          if (unitLimit.eTime && unitLimit.eTime > now) {
            delete unitLimit.eTime
          }
          if (unitLimit.bTime && unitLimit.bTime < now) {
            delete unitLimit.bTime
          }
          // 渠道
          if (unitLimit.channel && this.channel && unitLimit.channel.indexOf(this.channel) > -1) {
            delete unitLimit.channel
          }

          // 综合
          if (item.hide && !unitLimit.mL && !unitLimit.isS && !unitLimit.orgId && !unitLimit.shops && !unitLimit.packageIds && !unitLimit.isGroup && !unitLimit.eTime && !unitLimit.bTime && !unitLimit.channel) {
            delete item.hide
            targetArr.push(`${target}[${index}].hide`)
          }
          // 后续处理 非二次的直接删除
          if (item.hide && (unitLimit.orgId || unitLimit.shops || unitLimit.eTime || unitLimit.bTime || unitLimit.channel)) {
            item.delete = 1
          }
        }
      })
      // 恢复显示
      if (targetArr.length && !noSet) {
        let data = {}
        targetArr.forEach(e => {
          data[e] = false
        })
        this.setData(data)
      }
      if (!noSet) {
        this.setData({
          userInfo: this.data.userInfo,
          shopInfo: this.data.shopInfo
        })
      }
    },
    scrollTo(e) {
      let data = { ...e.detail }
      this.triggerEvent('scrollTo', {
        ...data,
        selector: '#subPage>>>' + data.selector,
        offsetTop: -1 * app.util.rpx2px(this.barH)
      })
    }
  }
})

// 新增：格式化 unitList 的方法，处理 goods 和 multGoods
function formatUnitList(unitList) {
  return unitList
    .map(item => {
      // 深拷贝，避免污染原数据
      const newItem = { ...item, unitContent: { ...item.unitContent }, unitStyle: { ...item.unitStyle } }

      // 促销链接清理
      newItem.unitContent = processLinks(item.unitContent, item.unitContent.existPromoIdList || [])
      delete newItem.unitContent.existPromoIdList
      delete newItem.unitContent.promoIdList

      // 处理 goods 组件
      if (newItem.unitKey === 'goods') {
        newItem.unitContent.list = sortGoods(newItem.unitContent.goods[0], newItem.unitContent.list)
        delete newItem.unitContent.goods
        if (!Array.isArray(newItem.unitContent.list) || !newItem.unitContent.list.length) {
          newItem.delete = 1
        }
      }
      // 处理 multGoods 组件
      else if (newItem.unitKey === 'multGoods' && newItem.unitContent && Array.isArray(newItem.unitContent.goods)) {
        newItem.unitContent.goods = newItem.unitContent.goods
          .map(goodsItem => {
            let goodsList
            if (newItem.unitContent[goodsItem.key]) {
              goodsList = sortGoods(goodsItem, newItem.unitContent[goodsItem.key])
            } else {
              goodsList = []
            }
            // 塞入 goodsList 后，删除原有的 key 商品数组，避免冗余
            delete newItem.unitContent[goodsItem.key]
            delete goodsItem.classId_bh
            delete goodsItem.classId_fs
            delete goodsItem.promoId_bh
            delete goodsItem.promoId_fs
            delete goodsItem.goodsids
            delete goodsItem.goodsListType
            return {
              ...goodsItem,
              goodsList
            }
          })
          .filter(item => item.goodsList && item.goodsList.length > 0)
      } else if (newItem.unitKey === 'multGoods' && newItem.unitContent && Array.isArray(newItem.unitContent.goods1)) {
        const orgId = app.local.get('orgId')
        // 独立获取列表
        newItem.unitContent.goods = newItem.unitContent.goods1.map(e => {
          if (orgId == 102) {
            if (e.goodsListType === '1') e.classId = e.classId_bh
            else e.promoId = e.promoId_bh
          } else {
            if (e.goodsListType === '1') e.classId = e.classId_fs
            else e.promoId = e.promoId_fs
          }
          delete e.classId_bh
          delete e.classId_fs
          delete e.promoId_bh
          delete e.promoId_fs
          delete e.goodsids
          delete e.goodsListType
          return {
            ...e
          }
        })
        delete newItem.unitContent.goods1
      }
      // 处理 cube 组件
      else if (newItem.unitKey === 'cube' && newItem.unitContent && Array.isArray(newItem.unitContent.goods)) {
        delete newItem.unitContent.goods
        newItem.unitContent.list.forEach(item => {
          if ((item.type === 'dynamicGoods' || item.type === 'goods') && item.data.content?.goods && item.data.content?.goods[0].key) {
            let key = item.data.content?.goods[0].key
            const goodsList = newItem.unitContent[key] || []
            item.data.content.list = sortGoods(item.data.content?.goods[0], goodsList)
            delete item.data.content.goods
            delete newItem.unitContent[key]
          }
        })
      }
      // 存在商品相关组件  若无商品  将删除组件
      if (Array.isArray(newItem.unitContent.goods) && !newItem.unitContent.goods.length) {
        newItem.delete = 1
      }
      return newItem
    })
    .filter(item => !item.delete)
}

/**
 * 递归处理所有层级的 link 字段，校验 promo_dtl_id_1 参数
 * @param {Object|Array} data 需要处理的数据对象或数组
 * @param {Array} existPromoIdList 允许存在的 id 列表
 * @returns 处理后的数据
 */
function processLinks(data, existPromoIdList) {
  try {
    if (Array.isArray(data)) {
      // 递归处理数组
      return data.map(item => processLinks(item, existPromoIdList))
    } else if (typeof data === 'object' && data !== null) {
      const newObj = {}
      for (const key in data) {
        if ((key === 'link' || key === 'jumpUrl') && typeof data[key] === 'string' && data[key].includes('promo_dtl_id_1=')) {
          // 提取 id 列表
          const match = data[key].match(/promo_dtl_id_1=([\d,]+)/)
          if (match) {
            const ids = match[1].split(',')
            // console.log('start',ids);
            const validIds = ids.filter(id => existPromoIdList.includes(id))
            // console.log('validIds',validIds);
            if (validIds.length === 0) {
              // 都不存在，直接不赋值到 newObj，相当于删除
              newObj[key] = data[key].replace(/promo_dtl_id_1=[\d,]+/, `promo_dtl_id_1=1&deletePromo=1`)
              continue
            } else if (validIds.length >= 1) {
              // 只保留一个 id
              newObj[key] = data[key].replace(/promo_dtl_id_1=[\d,]+/, `promo_dtl_id_1=${validIds[0]}`)
              continue
            }
            // 有多个存在，保留原 link
            newObj[key] = data[key]
            continue
          }
        }
        // 递归处理
        newObj[key] = processLinks(data[key], existPromoIdList)
      }
      return newObj
    }
    return data
  } catch (e) {
    console.error('processLinks error:', e)
    return data
  }
}

function sortGoods(goodsConfig, list) {
  // 根据 goods 配置的货号顺序调整 list 中商品的顺序
  if (goodsConfig.goodsids && Array.isArray(list)) {
    const goodsidsOrder = goodsConfig.goodsids.split(',')
    const sortedList = []
    const processedGoodsSn = new Set() // 用于跟踪已处理的货号

    // 按照 goodsids 的顺序重新排列 list，跳过重复的货号
    goodsidsOrder.forEach(goodsSn => {
      if (!processedGoodsSn.has(goodsSn)) {
        const foundItem = list.find(item => item.goodsSn === goodsSn)
        if (foundItem) {
          sortedList.push(foundItem)
          processedGoodsSn.add(goodsSn)
        }
      }
    })

    // 如果有商品在 list 中但不在 goodsids 中，追加到末尾
    list.forEach(item => {
      if (!processedGoodsSn.has(item.goodsSn)) {
        sortedList.push(item)
      }
    })

    return sortedList
  }
  return list || []
}
