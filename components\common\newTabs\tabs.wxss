.con{
  width: 100%;
  position: sticky;
  /* 修复导航tab空隙 */
  /* padding: 0 30rpx; */
  padding: 1px 30rpx 0;
  box-sizing: border-box;
  z-index: 15;
}

.tabnav {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
}

.nav-active {
  font-weight: 700;
}

.tab-item {
  position: relative;
  width: auto;
  display: inline-block;
  padding-right: 30rpx;
  transition: all .15s;
  transform: scale(1);
  height: 32rpx;
  line-height: 32rpx;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}
.bgCircle{
  position: absolute;
  top: -9px;
  left: 0;
  width: 48rpx;
  height: 48rpx;
  border-radius: 100rpx;
  background: linear-gradient(135deg, rgba(230, 0, 18, 0.5), rgba(232, 68, 81, 0.2));
  transition: all .15s;
}

.navItem .navSelectTxt{
  z-index: 3;
  position: relative;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 1);
}
.navItem:last-of-type{
  margin-right: unset;
}

.tab-item-Ordinary{
  position: relative;
  height: 40rpx;
  display: flex;
  font-size: 28rpx;
  color: #333;
  justify-content: space-between;
  align-content: space-between;
  flex-direction: column;
  align-items: center;
  margin-right: 80rpx;
}
.tab-item-Ordinary:last-of-type{
  margin-right: unset;
}
.navSelectOrdinary{
  font-weight: bold;
  color: #E60012;
}
.navSelectOrdinary .navSelectTxt,.tab-item-Ordinary .navSelectTxt{
  height: 28rpx;
  line-height: 28rpx;
}
.bgLine{
  background: transparent;
  width: 40rpx;
  height: 4rpx;
  border-radius: 30px;
}
.tab-item .navSelectTxt{
  font-size: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  display: inline-block;
}
.navSelect{
  font-weight: 600;
  font-size: 40rpx;
  transform: scale(1);
  color: #000000;
  height: 40rpx;
  line-height: 40rpx;
}
.navSelect .navSelectTxt{
  font-size: 40rpx;
  height: 42rpx;
  line-height: 42rpx;
}