Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 提示文字
    text: {
      type: String,
      value: '点击「添加小程序」，微信下拉访问更便捷'
    },
    // 多少秒后关闭
    duration: {
      type: Number,
      value: 3
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    SHOW_TOP: false,
    SHOW_MODAL: false,

    tri_right: '120rpx',
    tri_top: '120rpx'
  },

  ready: function() {

    // 判断是否已经显示过
    let num = wx.getLaunchOptionsSync().scene //场景指为1089 或 1104 也跳过

    let cache = wx.getStorageSync('is_fav_miniapp');
    if (cache || num == 1089 || num == 1104) {
      wx.setStorageSync("is_fav_miniapp", new Date)
      return;
    }

    // 没显示过，则3秒后进行展示
    setTimeout(() => {
      //三角位置
      if (wx.canIUse('getMenuButtonBoundingClientRect') && wx.canIUse('getSystemInfoSync')) {
        try {
          let sys = wx.getSystemInfoSync();
          let wxbtn = wx.getMenuButtonBoundingClientRect();
          let right = 130
          right = sys.screenWidth - wxbtn.right + wxbtn.width * 3 / 4 - 16 * sys.screenWidth / 750
          this.setData({
            tri_right: right + 'px',
            tri_top: wxbtn.bottom + 'px'
          })
        } catch (e) {
          this.setData({
            tri_right: '65px;'
          })

        }

      } else {
        console.log('false')
        this.setData({
          tri_right: '65px;'
        })
      }
      this.setData({
        SHOW_TOP: true
      });
      // 关闭时间
      setTimeout(() => {
        this.setData({
          SHOW_TOP: false
        })
      }, this.data.duration * 1000);
    }, 3000)
  },

  /**
   * 组件的方法列表
   */
  methods: {
    preventD: function() {
      return
    },
    // 显示全屏添加说明
    showModal: function() {
      this.setData({
        SHOW_TOP: false,
        SHOW_MODAL: true
      });
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          hideTabBar: true
        })
      }
    },
    close_mask: function() {
      this.setData({
        SHOW_MODAL: false
      });
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          hideTabBar: false
        })
      }
    },
  }
})