.show_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: grey;
  padding: 20rpx 0;
  font-size: 28rpx;
  width: 100%;
  min-height: 90rpx;
  margin-bottom: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

.show_end text {
  color: grey;
}

.default {
  width: 100%;
  display: flex;
  display: -webkit-box;
  display: -webkit-flex;
  flex-direction: column;
  align-items: center;
  -webkit-align-items: center;
  padding-top: 100rpx;
}

.default image {
  width: 360rpx;
  height: 290rpx;
  margin-bottom: 80rpx;
}

.default text {
  font-size: 32rpx;
  color: rgb(111, 111, 111);
}

@keyframes Loading {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loadmore-icon {
  display: inline-block;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 4rpx solid gray;
  border-bottom-color: transparent;
  vertical-align: middle;
  margin-right: 10rpx;
  -webkit-animation: Loading 0.6s linear infinite;
  animation: Loading 0.6s linear infinite;
}

.hover {
  filter: contrast(97%);
  border-radius: 2rpx;
}

.hover2 {
  filter: contrast(88%);
  border-radius: 2rpx;
}

.hover3 {
  filter: contrast(80%);
}


.location-box {
  height: 20vh;
  margin-top: 30vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.location-box text {
  font-size: 50rpx;
  color: #404144;
  font-weight: 700;
  text-align: center;
  padding: 0 50rpx;
}

.location-box view {
  font-size: 36rpx;
  background: linear-gradient(to left, #E60012, #FF7956);
  color: #fff;
  padding: 30rpx 100rpx;
  border-radius: 90rpx;
  margin-top: 10vh;
}

.observer {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: -1;
}
