@import "/font/icon_f3.wxss";

.popup {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  font-size: 26rpx;
  padding-bottom: 16rpx;
}

.popup-top {
  width: 100%;
  font-family: "微软雅黑";
  padding: 30rpx 30rpx 20rpx;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
}

.popup-title {
  width: 100%;
  font-size: 40rpx;
  font-family: "微软雅黑";
  color: #303030;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title-fb {
  font-size: 28rpx;
  align-self: top;
  margin-left: 16rpx;
  color: #434343;
  flex: 1;
}

.fb-tip {
  color: #ec3a3a;
  font-size: 24rpx;
}

.serviceDescription3 {
  background: #F6F6F6;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  font-size: 26rpx;
}

.coupon {
  min-height: 170rpx;
  width: 90%;
  margin: 24rpx auto;
  margin-top: 0;
  border-radius: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  background-color: #fff;
  letter-spacing: .6px;
  overflow: hidden;
  position: relative;
}

.coupon .left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-right: 0px solid #F94980;
  border-radius: 8px 0 0 8px;
  color: #fff;
  min-width: 180rpx;
  padding: 0 10rpx;
  background: linear-gradient(to left, #E60012, #FF7956);
}

.coupon .right {
  padding: 5px 8px;
  border-radius: 0 8px 8px 0;
  flex: 1;
  overflow: hidden;
  /*超出部分隐藏*/
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  white-space: nowrap;
  /*规定段落中的文本不进行换行 */
  text-align: left;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.coupon .left .rule {
  /* @include sc(20rpx, #fff); */
  font-size: 11px;
  margin-top: 4rpx;
}

.coupon .left .money-content {
  font-size: 12px;
}

.coupon .left .money {
  font-weight: bold;
  font-size: 30px;
  /* @include sc(80rpx, #fff); */
  letter-spacing: 1px;
}

.coupon .right .title {
  /* @include sc(.20rpx,#555); */
  font-weight: bold;
  font-size: 28rpx;
  letter-spacing: .9px;
  margin: 0 0 4rpx 0;
  color: #555;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}

.coupon .right .use_way {
  /* @include sc(.50rpx, #888); */
  letter-spacing: .9px;
  font-size: 22rpx;
  /* margin: 10rpx 0; */
  color: #666;
}

.coupon .right .valid {
  /* @include sc(20rpx, #aaa); */
  margin: 8rpx 0;
  color: #aaa;
  font-size: 22rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.coupon .right .valid .to-use {
  color: #e60012;
  border: 1px solid;
  border-radius: 10px;
  padding: 6rpx 20rpx;
  font-size: 26rpx;
  margin-left: auto;
  /* margin-top: -40rpx; */
}

.dash-top {
  border-top: 1px dashed #eee;
  padding-top: 5px;
  margin-bottom: 2px !important;
}

.lift-day {
  position: absolute;
  top: 7px;
  right: -30px;
  font-size: 10px;
  padding: 0 30px;
  text-align: center;
  background: #FFA14B;
  color: white;
  transform: rotateZ(45deg);
}

.confirm {
  width: 690rpx;
  margin: 0 auto;
  text-align: center;
  font-size: 30rpx;
  color: #fff;
  margin-top: 12rpx;
  background: linear-gradient(to left, #E60012, #FF7956);
  padding: 20rpx 0;
  border-radius: 60rpx;
}

.serDes {
  width: 100%;
  padding: 20rpx;
  font-size: 39rpx;
  font-family: "微软雅黑";
  color: #474747;
  background: #f1f1f1;
}

.description {
  flex: 1;
  position: relative;
  background: #F6F6F6;
}

.vip {
  font-size: 27rpx;
  color: #474747;
  margin-top: 20rpx;
  overflow-y: scroll;
}

.vipDiscount {
  text-decoration: underline;
  font-size: 20rpx;
  color: rgb(164, 164, 164);
  padding-left: 40rpx;
}

.otherDes {
  font-size: 20rpx;
  color: rgb(164, 164, 164);
  padding-left: 40rpx;
}





.yd-cell-left {
  width: 90%;
  color: #565656;
}

.yd-checklist {
  background: #f4f4f4;
}

.yd-tab-nav .yd-tab-active:before {
  background-color: #E60012;
  left: 70%;
  width: 20%;
  margin-left: -30%;
}

.yd-tab-nav .yd-tab-active {
  color: #E60012 !important;
}

.yd-checklist:after {
  content: "";
  position: absolute;
  z-index: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  border-bottom: 0px solid #d9d9d9;
  -webkit-transform: scaleY(.5);
  transform: scaleY(.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}

.yd-checklist-item-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 2px;
  margin-left: -12px;
}

.yd-checklist-item:not(:last-child):after {
  height: 0;
  border-bottom: 0px solid #d9d9d9;
}

.yd-checklist-content {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  position: relative;
  color: #333;
  padding-right: 2px;
}

.yd-checklist-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  z-index: 1;
  margin-left: 12px;
  margin-bottom: 20rpx;
}

.yd-confirm-ft>a.primary {
  color: #E60012 !important;
}

.yd-btn {
  padding: 0 30rpx;
  border-radius: 15px;
}

.act-item {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 20rpx;
  font-size: 28rpx;
}

.act-item-0 {
  display: flex;
  font-size: 36rpx;
  color: #c9c9c9;
  align-items: center;
}

.act-item-1 {
  flex: 1;
  font-size: 28rpx;
  color: #555555;
  display: flex;
  align-items: center;
}

.act-item-2 {
  font-size: 26rpx;
  color: #525252;
  display: flex;
  align-items: center;
}

.act-item-3 {
  display: flex;
  font-size: 36rpx;
  color: #c9c9c9;
  align-items: center;
}

.tab {
  width: 100%;
  height: 80rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #fff;
  border-bottom: 2rpx solid #f3f3f3;
}

.tab-item {
  height: 100%;
  padding: 0 12rpx;
  font-size: 28rpx;
  color: #585858;
  border-bottom: 6rpx solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab .active {
  color: #E60012;
  border-bottom-color: #E60012;
}


.to-use-fb {
  color: #f38143;
  border: 1px solid #ffccaf;
  border-radius: 30px;
  padding: 6rpx 20rpx;
  font-size: 26rpx;
  margin-left: auto;
  margin-top: -40rpx;
}

.coupon-fb::after {
  content: '兑';
  background: #f38143;
  color: #fafafa;
  position: absolute;
  right: 0;
  transform: rotate(45deg);
  padding: 0rpx 40rpx;
  font-size: 24rpx;
  top: 6rpx;
  right: -30rpx;
}

.coupon .right .valid .arrow {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 12px;
}

.dash-top {
  border-top: 1px dashed #eee;
  padding-top: 5px;
  margin-bottom: 2px !important;
}

.lift-day {
  position: absolute;
  top: 7px;
  right: -30px;
  font-size: 10px;
  padding: 0 30px;
  text-align: center;
  background: #FFA14B;
  color: white;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
