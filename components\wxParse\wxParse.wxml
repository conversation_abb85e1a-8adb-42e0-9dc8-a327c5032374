<!--**
 * author: <PERSON> (微信小程序开发工程师)
 * organization: WeAppDev(微信小程序开发论坛)(http://weappdev.com)
 *               垂直微信小程序开发交流社区
 * 
 * github地址: https://github.com/icindy/wxParse
 * 
 * for: 微信小程序富文本解析
 * detail : http://weappdev.com/t/wxparse-alpha0-1-html-markdown/184
 */-->

<!--基础元素-->
<template name="wxParseVideo">
	<!--增加video标签支持，并循环添加-->
	<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
		<video class="{{item.classStr}} wxParse-{{item.tag}}-video" src="{{item.attr.src}}"></video>
	</view>
</template>
<wxs src="../../utils/utils.wxs" module="utils"></wxs>
<template name="wxParseImg">
	<img  class="{{item.classStr}} wxParse-{{item.tag}}" data-from="{{item.from}}" data-src="{{item.attr.src}}" data-idx="{{item.imgIndex}}" data-open="{{item.open}}" src="{{utils.jpg2jpeg(item.attr.src)}}"  bindload="wxParseImgLoad" bindtap="wxParseImgTap" mode="widthFix" style="width:100%;height: {{item.attr.height}}rpx;display: flex;margin-bottom: -1rpx;" data-src="{{item.attr.src}}" show-menu-by-longpress lazy-load/>
</template>

<template name="WxEmojiView">
	<view class="viewc WxEmojiView wxParse-inline" style="{{item.styleStr}}">
		<block wx:for="{{item.textArray}}" wx:key="index">
			<block class="{{item.text == '\\n' ? 'wxParse-hide':''}}" wx:if="{{item.node == 'text'}}">{{item.text}}</block>
			<block wx:elif="{{item.node == 'element'}}">
				<image class="wxEmoji" src="{{item.baseSrc}}{{item.text}}" />
			</block>
		</block>
	</view>
</template>

<template name="WxParseBr">
	<text>\n</text>
</template>
<!--入口模版-->

<template name="wxParse">
	<block wx:for="{{wxParseData}}" wx:key="index">
		<template is="wxParse0" data="{{item}}" />
	</block>
</template>


<!--循环模版-->
<template name="wxParse0">
	<!--<template is="wxParse1" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse1" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse1" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse1" data="{{item}}" />
				</block>
			</view>
		</block>
		<block wx:elif="{{item.tag == 'table'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse1" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" catchtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse1" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse1" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse1" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>



<!--循环模版-->
<template name="wxParse1">
	<!--<template is="wxParse2" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse2" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse2" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse2" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse2" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse2" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse2" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>


<!--循环模版-->
<template name="wxParse2">
	<!--<template is="wxParse3" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse3" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse3" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse3" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse3" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse3" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse3" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>

<!--循环模版-->
<template name="wxParse3">
	<!--<template is="wxParse4" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse4" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse4" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse4" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse4" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse4" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse4" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>

<!--循环模版-->
<template name="wxParse4">
	<!--<template is="wxParse5" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse5" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse5" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse5" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse5" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse5" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse5" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>

<!--循环模版-->
<template name="wxParse5">
	<!--<template is="wxParse6" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse6" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse6" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse6" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse6" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse6" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse6" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>

<!--循环模版-->
<template name="wxParse6">
	<!--<template is="wxParse7" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse7" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse7" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse7" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse7" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse7" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse7" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>
<!--循环模版-->
<template name="wxParse7">
	<!--<template is="wxParse8" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse8" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse8" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse8" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse8" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse8" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse8" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>

<!--循环模版-->
<template name="wxParse8">
	<!--<template is="wxParse9" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse9" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse9" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse9" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse9" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse9" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse9" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>

<!--循环模版-->
<template name="wxParse9">
	<!--<template is="wxParse10" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse10" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse10" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse10" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse10" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse10" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse10" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>

<!--循环模版-->
<template name="wxParse10">
	<!--<template is="wxParse11" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse11" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse11" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse11" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse11" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse11" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse11" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>

<!--循环模版-->
<template name="wxParse11">
	<!--<template is="wxParse12" data="{{item}}" />-->
	<!--判断是否是标签节点-->
	<block wx:if="{{item.node == 'element'}}">
		<block wx:if="{{item.tag == 'button'}}">
			<button type="default" size="mini">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse12" data="{{item}}" />
				</block>
			</button>
		</block>
		<!--li类型-->
		<block wx:elif="{{item.tag == 'li'}}">
			<view class="viewc {{item.classStr}} wxParse-li" style="{{item.styleStr}}">
				<view class="viewc {{item.classStr}} wxParse-li-inner">
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<view class="viewc {{item.classStr}} wxParse-li-circle"></view>
					</view>
					<view class="viewc {{item.classStr}} wxParse-li-text">
						<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
							<template is="wxParse12" data="{{item}}" />
						</block>
					</view>
				</view>
			</view>
		</block>

		<!--video类型-->
		<block wx:elif="{{item.tag == 'video'}}">
			<template is="wxParseVideo" data="{{item}}" />
		</block>

		<!--img类型-->
		<block wx:elif="{{item.tag == 'img'}}">
			<template is="wxParseImg" data="{{item}}" />
		</block>

		<!--a类型-->
		<block wx:elif="{{item.tag == 'a'}}">
			<view bindtap="wxParseTagATap" class="viewc wxParse-inline {{item.classStr}} wxParse-{{item.tag}}" data-src="{{item.attr.href}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse12" data="{{item}}" />
				</block>
			</view>
		</block>

		<block wx:elif="{{item.tag == 'br'}}">
			<template is="WxParseBr"></template>
		</block>
		<!--热区标签-->
		<block wx:elif="{{item.tag == 'area'&&item.attr.href}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}} areaareaarea" style="{{item.styleStr}}" bindtap="openH5" data-url="{{item.attr.href}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse12" data="{{item}}" />
				</block>
			</view>
		</block>
		<!--其他块级标签-->
		<block wx:elif="{{item.tagType == 'block'}}">
			<view class="viewc {{item.classStr}} wxParse-{{item.tag}}" style="{{item.styleStr}}">
				<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
					<template is="wxParse12" data="{{item}}" />
				</block>
			</view>
		</block>

		<!--内联标签-->
		<view wx:else class="viewc {{item.classStr}} wxParse-{{item.tag}} wxParse-{{item.tagType}}" style="{{item.styleStr}}">
			<block wx:for="{{item.nodes}}" wx:for-item="item" wx:key="index">
				<template is="wxParse12" data="{{item}}" />
			</block>
		</view>

	</block>

	<!--判断是否是文本节点-->
	<block wx:elif="{{item.node == 'text'}}">
		<!--如果是，直接进行-->
		<template is="WxEmojiView" data="{{item}}" />
	</block>

</template>
