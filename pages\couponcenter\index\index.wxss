/* pages/couponcenter/index/index.wxss */

page {
  min-height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  background: #f6f6f6;
}

.coupon-type-wrap {
  height: 92rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}

.coupon-type-wrap .coupontype {
  min-width: 100rpx;
  height: 46rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F6F6F6;
  color: #999;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: 2rpx solid #F6F6F6;
  margin-right: 20rpx;
}

.coupon-type-wrap .coupontype.active {
  color: #E60012;
  border-color: #E60012;
  background: #FEEEF4;
}

.coupon-type-wrap .history {
  font-size: 28rpx;
  color: #666666;
  margin-left: auto;
  align-self: stretch;
  display: flex;
  align-items: center;
}

.swiper-container {
  position: relative;
  flex: 1;
}

.swiper-box {
  width: 750rpx;
  height: 3600rpx;
}

.none-coupon {
  font-size: 30rpx;
  color: #969696;
  padding-top: 40%;
  text-align: center;
}

.tip-count {
  position: absolute;
  height: 15px;
  width: 15px;
  background: rgba(246, 57, 121, 0.9);
  color: #fff;
  top: 5px;
  font-size: 10px;
  right: -10px;
  line-height: 15px;
  text-align: center;
  border-radius: 10px;
  z-index: 0;
}


.my_coupon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  line-height: 50px;
  text-align: center;
  /* border: 1px solid; */
  font-size: 11px;
  color: white;
  background: rgba(0, 0, 0, .5);
  position: fixed;
  bottom: 120px;
  right: 2%;
  z-index: 600;
}

.swiper-tab {
  width: 100%;
  text-align: center;
  line-height: 80rpx;
  font-weight: 700;
  display: flex;
  justify-content: center;
}

.tabs-list {
  font-size: 30rpx;
  display: inline-block;
  width: 50%;
  height: 100%;
  text-align: center;
  position: relative;
  line-height: 100rpx;
  background: #fd6c90;
  color: #fff;
}

.tabs-list-active {
  background: #fafafa;
  font-weight: 700;
  color: #ea3675;
  font-size: 32rpx;
}

.swiper-tab-list {
  font-size: 30rpx;
  display: inline-block;
  width: 20%;
  color: #444;
  margin-right: 4%;
  margin-left: 4%;
  position: relative;
}

.on {
  color: #F63979;
  /* border-bottom: 5rpx solid #F63979; */
}

.on::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 36%;
  border: 2px solid;
  border-radius: 5px;
  width: 20%;
  background: #F63979;
}





.gift-alert {
  width: 670rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 10vh;
}

.key {
  flex: 1;
  line-height: 53rpx;
  text-align: center;
  border: 1rpx solid #cccccc;
  border-radius: 39rpx;
  margin-top: 34rpx;
  margin: 34rpx 20rpx 0 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.key_title {
  line-height: 53rpx;
  border: 1rpx solid #CCB;
  /* display: block; */
  text-align: center;
  border-radius: 32rpx;
  margin-top: 34rpx;
  width: 82px;
  margin-right: 20rpx;
}

.footer-wrap {
  display: flex;
  height: 90rpx;
  width: 100%;
  background: #fff;
  border-top: 2rpx solid #f1f1f1;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100;
  box-sizing: content-box;
  padding-bottom: env(safe-area-inset-bottom);
}

.footer-wrap>view {
  height: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #404040;
  position: relative;
  font-size: 28rpx;
  color: #333;
}

.footer-wrap>view:first-of-type::after {
  content: ' ';
  position: absolute;
  width: 1px;
  height: 30rpx;
  background: #dfdfdf;
  right: -0.5px;
  top: 50%;
  margin-top: -10rpx;
}

.to-use {
  color: #E60012;
  background: #FEEEF4;
  border-radius: 24rpx;
  border: 2rpx solid #E60012;
  padding: 6rpx 24rpx;
  margin-left: auto;
}
