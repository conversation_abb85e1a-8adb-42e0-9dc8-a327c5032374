/* pages/distributershop/groupshare/groupshare.wxss */
@import "/font/icon_f7.wxss";

image,
view,
label {
  padding: 0;
  margin: 0;
  overflow: visible;
  box-sizing: border-box;
  font-size: inherit;
  position: relative;
}

page {
  background: #f5f6f7;
  box-sizing: border-box;
  min-height: 100%;
  padding: 0;
  padding-bottom: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.share-bottom {
  z-index: 21;
  position: fixed;
  bottom: 0;
  left: 0;
  height: 100rpx;
  width: 100%;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  background: #e60012;
  color: #fff;
}

.share-title {
  padding: 0 30rpx;
  padding-top: 30rpx;
  display: flex;
  width: 100%;
}

.share-title>text,
.share-title>view {
  font-size: 36rpx;
  color: #333;
  display: flex;
  align-items: center;
  font-weight: 700;
}

.share-title .edit {
  margin-left: auto;
  color: #e60012aa;
  font-size: 30rpx;
}

.upload {
  background: #fff;
  margin-top: 18rpx;
}

.reFont {
  color: #808080;
  padding: 16rpx 30rpx;
  font-size: 28rpx;
}

.upload-imgs {
  padding: 16rpx 0 16rpx 30rpx;
  display: flex;
  flex-wrap: wrap;
}


.upImg {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  overflow: visible;
  margin-bottom: 20rpx;
  border-radius: 4rpx;
}

.upImg-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  margin-right: 20rpx;
  overflow: visible;
  border: 2rpx dashed #ccc;
  color: #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 70rpx;
  border-radius: 12rpx;

}

.delete {
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  font-size: 36rpx;
  color: #fff;
  opacity: 0.65;
  background: #a0a0a0;
  border-radius: 0 12rpx 0 12rpx;
}

/* 上传 */
.upImgLoad {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 999;
  left: 0px;
  bottom: 0;
  background: rgba(0, 0, 0, .3);
}

.imgOut1 {
  margin-top: 450rpx;
  text-align: center;
  color: #f7f7f7;
  font-size: 28rpx;
}

.upLoadImg {
  width: 400rpx;
  height: 36rpx;
  border: solid 2px #ccc;
  display: inline-block;
}

.select {
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 19;
  background: rgba(151, 130, 197, 0.65);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4rpx;
  color: #fff;
  font-size: 70rpx;
}