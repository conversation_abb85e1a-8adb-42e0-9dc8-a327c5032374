<!--pages/couponcenter/index/index.wxml-->
<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<tabs list="{{['三福卡券','商家联盟卡券']}}" index="{{tabstyle}}" bindchange="tabNav" lineStyle="height: 6rpx;border-radius: 6px;" activeStyle="font-weight:700;color:#E60012;" tabStyle="font-size:32rpx;padding:0 30rpx;color:#666;" style2="justify-content:space-evenly;height:96rpx;border-bottom:2rpx solid #EFEFEF;{{hideTab?'display:none;':''}}" sticky styles="z-index:999;top:0;background:#fff;" isFlex>
  <view wx:if="{{tabstyle==0}}" class="coupon-type-wrap">
    <view class="coupontype {{couponTypeIndex==index?'active':''}}" wx:for="{{couponTypeList}}" wx:key="index" bindtap="checkCoupon" data-i="{{index}}">{{item}}</view>
    <view class="history" bindtap='toHistory'>历史记录</view>
  </view>
</tabs>
<block wx:if="{{tabstyle==0}}">
  <view class='none-coupon' wx:if="{{couponList.length === 0}}">暂无可使用优惠券~~</view>
  <block wx:for="{{couponList}}" wx:key="index">
    <coupon wx:if="{{couponTypeIndex==0||utils.include(item.useMethodNew,couponTypeList[couponTypeIndex])}}" item="{{item}}" type="{{hideTab?1:''}}" showCode> </coupon>
  </block>
</block>
<view wx:else class='coupon-list1' style="height:100%">
  <view class='none-coupon' style="text-align: center;" wx:if="{{externallist.length === 0}}">暂无可使用的联盟商家卡券哦~~
  </view>
  <block wx:for="{{externallist}}" wx:key="index">
    <coupon bindtap='toDetail' data-index="{{index}}" hideBtn>
      <image slot="left" src="{{item.showPicUrl}}" style="width: 200rpx; height: 200rpx;position:absolute;left: 0;right: 0;" mode="aspectFill" />
      <view slot="title">
        {{item.couponTypeName}}
      </view>
      <view slot="useway">
        兑换时间: {{item.whenTime}}

      </view>
      <view slot="other" style="display: flex;align-items: center;flex: 1;">
        <text> {{item.yyExchangeType}}</text>
        <view class='to-use' data-yyExchangeType="{{item.yyExchangeType}}" data-couponcode="{{item.couponCode}}" data-secretkey="{{item.secretKey}}" catchtap="toCoupon">
          {{item.yyExchangeType === 'H5兑换' ? '点击查看' : '提取券码'}}
        </view>
      </view>
    </coupon>
  </block>
</view>
<view style="height: 50rpx;"></view>
<searchBar bind:change="getUnUseCoupons" wx:if="{{tabstyle === 0}}"></searchBar>
<block wx:if="{{!hideTab}}">
  <view style="width: 1px;height: 94rpx;margin-top: env(safe-area-inset-bottom);"></view>
  <view class="footer-wrap">
    <view bindtap='toCouponList'>
      领券中心
    </view>
    <view bindtap='toFubi'>
      福币兑换
    </view>
  </view>
</block>
<uni-popup show="{{extractShow}}" type="center" bindclose="onChangeStatus" zIndex="999">
  <view class="gift-alert">
    <view style="display:flex; font-size:26rpx" wx:if="{{info.couponCode}}">
      <view class='key' style="padding: 0 12rpx;">{{info.couponCode}}</view>
      <view class="key_title" bindtap="copyContent" data-val="{{info.couponCode}}">复制编码</view>
    </view>
    <view style="display:flex; font-size:26rpx" wx:if="{{info.secretKey}}">
      <view class='key' style="padding: 0 12rpx;">{{info.secretKey}}</view>
      <view class="key_title" bindtap="copyContent" data-val="{{info.secretKey}}">复制密钥</view>
    </view>
    <view style="text-align: center; margin-top: 30rpx;padding-bottom: 24rpx;font-size:28rpx" bindtap="onChangeStatus">关闭</view>
  </view>
</uni-popup>


<sf-loading full wx:if="{{loading}}"></sf-loading>

<auth id="sanfu-auth"></auth>
