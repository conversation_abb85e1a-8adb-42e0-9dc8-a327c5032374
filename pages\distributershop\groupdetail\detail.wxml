<!--pages/distributershop/groupdetail/detail.wxml-->
<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<view wx:if="{{pageStatus==2}}" class="location-box">
  <text>需要定位以获取商品价格</text>
  <view hover-class="hover" hover-start-time="20" hover-stay-time="100" bindtap="checkLocation">开启定位</view>
</view>
<block wx:else>
  <view class="detail-page">
    <view class="title">{{detail.recommendTitle}}</view>
    <view class="subtitle">{{detail.subtitle}}</view>
    <view class="autor">
      <text style="margin-right:8rpx;">{{detail.nickName || '三福君'}} </text>
      <text> {{detail.publishTime||''}}</text>
    </view>
    <block wx:for="{{detail.tempList}}" wx:key="index">
      <text wx:if="{{item.tempType=='text'}}" user-selectt class="article">{{item.data}}</text>
      <template wx:if="{{item.tempType=='1'}}" data="{{item}}" id="{{index}}" is="imgType1" />
      <template wx:if="{{item.tempType=='2'}}" data="{{item}}" id="{{index}}" is="imgType2" />
      <template wx:if="{{item.tempType=='3'}}" data="{{item}}" id="{{index}}" is="imgType3" />
      <template wx:if="{{item.tempType=='4'}}" data="{{item}}" id="{{index}}" is="imgType4" />
    </block>
  </view>

  <view class="bottom" wx:if="{{type!='preview'}}">
    <view class="viewer-count" bindtap="dianzan" style="margin-left:10px;">
      <span class="f7 {{detail.liked === 0 ? 'iconDG-dianzankong checked-bg' : 'iconDG-dianzanshi'}}"></span>
      <text>{{detail.likeCnt || 0}}</text>
    </view>
    <!--  <view class="viewer-count" bindtap="collect">
    <span class="f7 {{detail.collected === 0 ? 'iconDG-shoucangkong checked-bg' : 'iconDG-shoucangshi'}}"></span>
    <text>{{detail.collectCnt || 0}}</text>
  </view> -->
    <view wx:if="{{showShare}}" class="viewer-count" bindtap="showShare">
      <span data-shareItem="{{item}}" class="f7 iconDG-fenxiang"></span>
      <text>{{detail.shareCnt || 0}}</text>
    </view>
    <view class="goods-body">
      <text class="goods-btn" bindtap="changeGoodsFlag" data-come="main">商品 {{detail.goodsAmount}}</text>
    </view>
  </view>
</block>
<uni-popup show="{{ goodsflag }}" closeName="组合商品-商品弹框" zIndex="900" type="bottom" bind:close="closeGoodsFlag">
  <view style="height:40px;width:50%;"> </view>
  <view class="group-top">
    <text bindtap="goCart">购物车</text>
    <text>{{detail.goodsAmount}}件商品</text>
    <text class="uni-icon uni-icon-closeempty" bindtap="closeGoodsFlag"></text>
  </view>
  <view class="caogao-pop">
    <scroll-view scroll-y style="width: 100%;height: 100%;position: absolute;left: 0;top:0;bottom: 0;right: 0;">
      <block wx:for="{{goodslist}}" wx:key="index">
        <template data="{{item}}" id="{{index}}" is="group-goods" />
      </block>
    </scroll-view>
  </view>
</uni-popup>

<template name='group-goods'>
  <view class="line1" bindtap="goGoodsDetail" data-item="{{item}}">
    <image style="width:130px;height:130px;border-radius:5px 0 0 5px;" mode="aspectFill" src="{{utils.jpg2jpeg(item.limg)}}" />
    <!-- <view class="image" style='background:url({{item.simg || "https://img.sanfu.com/img/index_boy/shangpinwutu.png"}}) center -5px / 100% no-repeat'> -->
    <!-- </view> -->
    <view class="item-detail">
      <view class="title">{{item.goodsName}}</view>
      <view class="good-sn">
        货号：{{item.gooId}}
      </view>
      <view class="viewer">
        <view class="price">￥
          <text>{{item.salePrice}}</text>
        </view>
        <view class="viewer-cart" catchtap="openSku" data-item="{{item}}">
          <span class="f7 icongouwuche" style="font-size:28px;"></span>
        </view>
      </view>
    </view>
  </view>
</template>
<template name='imgType1'>
  <view class="imgType1" wx:if="{{item.imgList.length>0}}" bindtap="goGoodsDetail" data-item="{{item.imgList[0]}}">
    <image bindlongpress="showimg" data-r="{{item.imgList[0].originalUrl}}" mode="widthFix" src="{{utils.jpg2jpeg(item.imgList[0].originalUrl)}}">
      <view class="imgnav f7 iconxiaochengxu" wx:if="{{item.imgList[0].gooId}}"></view>
    </image>
  </view>
</template>
<template name='imgType2'>
  <view class="imgType2">
    <image wx:for="{{item.imgList}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2" mode="widthFix" src="{{utils.jpg2jpeg(item2.originalUrl)}}" bindtap="goGoodsDetail" data-item="{{item2}}">
      <view class="imgnav f7 iconxiaochengxu" wx:if="{{item2.gooId}}"></view>
    </image>
  </view>
</template>
<template name='imgType3'>
  <view class="imgType3">
    <image class="part1" mode="aspectFit" src="{{utils.jpg2jpeg(item.imgList[0].originalUrl)}}" bindtap="goGoodsDetail" data-item="{{item.imgList[0]}}">
      <view class="imgnav f7 iconxiaochengxu" wx:if="{{item2.gooId}}"></view>a
    </image>
    <view class="part2">
      <block wx:for="{{item.imgList}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2">
        <image wx:if="{{index2!=0}}" mode="aspectFit" src="{{utils.jpg2jpeg(item2.originalUrl)}}" bindtap="goGoodsDetail" data-item="{{item2}}">
          <view class="imgnav f7 iconxiaochengxu" wx:if="{{item2.gooId}}"></view>
        </image>
      </block>
    </view>
  </view>
</template>
<template name='imgType4'>
  <view class="imgType4">
    <image wx:for="{{item.imgList}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2" mode="aspectFit" src="{{utils.jpg2jpeg(item2.originalUrl)}}" bindtap="goGoodsDetail" data-item="{{item2}}">
      <view class="imgnav f7 iconxiaochengxu" wx:if="{{item2.gooId}}"></view>
    </image>
  </view>
</template>

<checkSize new="{{false}}"></checkSize>


<share id="sanfu-share" />
<sf-loading wx:if="{{showLoading}}" full />
