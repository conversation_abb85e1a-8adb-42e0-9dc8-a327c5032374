.rate {
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-user-select: none;
  user-select: none
}

.rate-item {
  position: relative;
  padding: 0 2px;
}

.icon {
  display: block;
  height: 1.1em;
  font-size: 30rpx;
}

.icon.half {
  position: absolute;
  top: 0;
  width: .5em;
  overflow: hidden;
  left: 2px;
}

.rate.type2 {
  width: 100%;
  display: flex;
}

.rate.type2 .rate-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #aaa;
  border-right: none;
  line-height: 1;
  padding: 6rpx 0;
  height: auto;
}

.rate.type2 .rate-item:first-of-type {
  border-radius: 8rpx 0 0 8rpx;
}

.rate.type2 .rate-item:last-of-type {
  border-right: 2rpx solid #aaa;
  border-radius: 0 8rpx 8rpx 0;
}
