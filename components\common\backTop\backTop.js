const app = getApp()
import IntersectionObserver from '../../../utils/intersection-observer.js';
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
  },
  /**
   * backTop 点击回到顶部组件（包含导购入口展示)
   * @navStyle {type} 类型[page|view]
   * @placeholderStyle {String} 填充样式
   * @titleStyle {String} 填充样式

   */
  properties: {
    type: {
      type: String,
      value: 'page'
    },
    showDis: {
      type: Boolean,
      value: false
    },
    top: {
      type: Number,
      value: 140
    },
    shoID: {
      type: String,
      value: ''
    },
    // 强制展示企微码
    invite: {
      type: Boolean,
      value: false
    },
    zIndex: {
      type: Number,
      value: 99
    },
  },
  data: {
    lastshoid: '',
    showTopBtn: false, // 回到顶部按钮
    showDisBtn: false, // 导购入口按钮
    showCodeBtn: false, //企业微信二维码按钮
    config_id: '',
    showSPHBtn: 0 //视频号按钮  0  1预告   2直播中
  },
  attached() {
    if (this.properties.showDis)
      this.checkStaff()
  },
  watch: {
    'shoID': function(newVal) {
      if (this.data.lastshoid != newVal) {
        this.setData({
          showCodeBtn: false
        })
        this.data.lastshoid = newVal
      }
    },

  },
  ready() {
    //曝光处理
    this.ob = new IntersectionObserver({
      selector: `.tigger`,
      observeAll: true,
      context: this,
      delay: 30,
      threshold: 0.000000000000000000000000001,
      initialRatio: 0,
      viewport: {
        bottom: 0,
        top: 0
      },
      onNoV: () => {
        this.setData({
          showTopBtn: true
        })
      },
      onEach: () => {
        this.setData({
          showTopBtn: false
        })
      }
    })
    this.ob.connect()
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: async function() {
      // setTimeout(() => {
      //   this.getSPH(1)
      // }, 500)
      this.ob && this.ob.reconnect && this.ob.reconnect()
      const limitShop = await app.util.checkLimitShop()
      this.setData({
        limitShop
      })
    },
    hide: function() {
      this.setData({
        showSPHBtn: false
      })
      this.ob && this.ob.disconnect && this.ob.disconnect()
    },
    resize: function() {},
  },
  methods: {
    startmessage(e) {
      console.log(e)
    },
    completemessage(e) {
      console.log(e)
    },
    toTop() {
      if (this.properties.type == 'page') {
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 0
        })
      } else {
        this.triggerEvent('toTop')
      }
    },
    async checkStaff() {
      await app.waitSid()
      if (!wx.getStorageSync('sid')) return
      if (wx.getStorageSync('isStaff')) {
        this.getStaffShop()
      } else if (typeof wx.getStorageSync('isStaff') != 'boolean') {
        setTimeout(async () => {
          let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/staff/verify', {
            sid: wx.getStorageSync('sid')
          }, res => {})
          if (res.success) {
            app.local.set('isStaff', true)
            this.getStaffShop()
          }
        }, 5000)
      }
    },
    async getStaffShop() {
      this.setData({
        showDisBtn: true
      })
      if (wx.getStorageSync('discard')) {
        return
      }
      let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/agentShop', {
        sid: wx.getStorageSync('sid')
      })
      if (res.success) {
        app.local.set('sho_id', res.data || '')
        app.local.set('disshop', res.data || '')
      } else app.local.set('sho_id', '')
      app.local.set('discard', wx.getStorageSync('cardid'))
    },
    // 视频号
    getChannelsLiveNoticeInfo() {
      return new Promise(res => {
        if (wx.canIUse('getChannelsLiveNoticeInfo'))
          wx.getChannelsLiveNoticeInfo({
            finderUserName: 'sphGmbF4i0VFiEL',
            success: res1 => {
              res(res1)
            },
            fail: res1 => {
              res('')
            }
          })
        else res('')
      })
    },
    getChannelsLiveInfo() {
      return new Promise(res => {
        if (wx.canIUse('getChannelsLiveInfo'))
          wx.getChannelsLiveInfo({
            finderUserName: 'sphGmbF4i0VFiEL',
            success: res1 => {
              res(res1)
            },
            fail: res1 => {
              res('')
            }
          })
        else res('')
      })
    },
    async getSPH(e) {
      //直播信息
      let liveInfo = await this.getChannelsLiveInfo()
      if (liveInfo && liveInfo.status == 2) {
        if (e === 1) {
          !app.local.get('closeSph') && this.setData({
            showSPHBtn: 2
          })
        } else {
          wx.openChannelsLive({
            finderUserName: 'sphGmbF4i0VFiEL',
            feedId: liveInfo.feedId,
            nonceId: liveInfo.nonceId
          })
        }
      } else {
        //预约信息
        let noticeInfo = await this.getChannelsLiveNoticeInfo()
        // 开播提醒
        if (noticeInfo && noticeInfo.status == 0) {
          if (e === 1) {
            !app.local.get('closeSph') && this.setData({
              showSPHBtn: 1
            })
          } else {
            wx.reserveChannelsLive({
              noticeId: noticeInfo.noticeId,
              success: res => {},
              fail: res => {}
            })
          }
        } else {
          this.setData({
            showSPHBtn: false
          })
          app.local.set('closeSph', 0, 12 * 60 * 60)
        }
      }
    },
    closeDis() {
      this.setData({
        showDisBtn: false
      })
    },
    closeDis2() {
      this.setData({
        config_id: ''
      })
    },
    closeDis3() {
      app.local.set('closeSph', 1, 12 * 60 * 60)
      this.setData({
        showSPHBtn: false
      })
    },
    toDis() {
      wx.navigateTo({
        url: '/pages/distributershop/index/index'
      })
    },
    celltoast() {
      wx.showToast({
        title: '已发送邀请通知',
        icon: 'success',
        duration: 2000
      })
    }
  }

});
