<wxs src="../../../../utils/utils.wxs" module="utils"></wxs>
<navbar id="navbar" isBack showMenuBtn navStyle="z-index:998;" title="活动推荐">
</navbar>
<view class="promo-container">
  <view class="promo-item" hover-class="hover" hover-start-time="20" hover-stay-time="100" wx:for="{{list}}" wx:key="index" bindtap="toList" data-id="{{item.promo_dtl_id}}" data-title="{{item.promo_name}}" wx:if="{{item.imgs.length>0}}">
    <image mode="aspectFill" class="promo-img" lazy-load  src="{{utils.jpg2jpeg(item.imgs[0])}}" />
    <view class="promo-name">{{item.promo_name}}</view>
    <view class="promo-bottom">
      <view>{{item.startTime}}-{{item.endTime}}</view>
      <view wx:if="{{!discard}}" style="padding: 20rpx;margin: -10rpx;" class="iconDG-fenxiang f7" catchtap="showShare" data-index="{{index}}"></view>
    </view>
  </view>
</view>

<view wx:if="{{has_more != 4}}" class="show_end" bindtap="getList">
  <view wx:if="{{has_more == 1}}" class="loadmore-icon"></view>
  <text>{{ loadingText[has_more] }}</text>
</view>

<share id="sanfu-share" />

<!-- 分享朋友圈二维码 -->
<canvas type="2d" id="shareCanvas" class="shareCanvas" style="width:{{canvasWidth}}px;height:{{canvasHeight}}px;position:fixed;top:-{{canvasHeight||9999}}px;right:-{{canvasHeight}}rpx;margin-top:-1000px;margin-right:-1000px;"></canvas>
<view hidden="{{showqrcode}}" class="qrcode">
  <image lazy-load mode="aspectFit" src="{{tmppath}}" style="height:{{canvasHeight}}px;width:630rpx;max-height:85%;border-radius: 20rpx;"></image>
  <view class="dis-top">
    <view style="background:white;width:40%;flex:none;" bindtap="closeQR">关闭</view>
    <view style="background:white;width:40%;flex:none;" bindtap="saveImage">保存</view>
  </view>
</view>

<sf-loading wx:if="{{showLoading}}" full />
