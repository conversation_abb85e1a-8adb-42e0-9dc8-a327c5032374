/* pages/distributershop/active/active.wxss */
@import "/font/icon_f7.wxss";
@import '../qrcode.wxss';
.single-page{background:#F6F6F6;}
.bg{height:340rpx;width:100%;box-shadow: 0 0 2rpx #eee;position: relative;}
.group-goods{margin-top: 24rpx;}
.autor{
  align-items: center;
  display: flex;
  color: #a9a9a9;font-size: 24rpx;
  background:white;
  padding: 10rpx;
}
.viewer{display: flex;background: white;font-size: 24rpx;padding: 10rpx 20rpx;justify-content: space-between;box-shadow: 0 4rpx 4rpx #eee;}  
.autor view{
  padding-right: 10rpx;
  font-size: 24rpx;
}
.title{
  align-items: center;
  display: flex;
  color: #222;
  font-size: 36rpx;
  background:white;
  padding: 10rpx;
  padding-bottom: 0;
}

.subtitle{
  align-items: center;
  display: flex;
  color: #555;
  font-size: 30rpx;
  background:white;
  padding: 0 10rpx;
}

.article-title{
  position: absolute;
  bottom: 0;left: 0;
  width: 100%;
  background: linear-gradient(360deg, rgba(0,0,0, .6),rgba(0, 0, 0, 0));
  margin: 0 auto;
  padding: 120rpx 20rpx 40rpx 20rpx;
  text-align: center;
  color: white;
  font-size: 26rpx;}
.viewer{display: flex;font-size: 24rpx;justify-content: flex-end;padding: 0;padding-top: 10rpx;}
.viewer-count{display: flex;align-items: center;position: relative;margin-right: 50rpx;color: #C7C6C6;padding: 16rpx;}
.viewer-count text{color: #555;font-size: 16rpx;position: absolute;height: 20rpx;line-height: 20rpx;right: 0%;top: 0;}