<!-- index.wxml -->
<wxs src="../../utils/utils.wxs" module="tools"></wxs>
<view class="top-bg">
  <navbar title="我的" isBack="" navStyle="overflow:hidden;" titleStyle="z-index:2;color:#202020;">
    <view style="position: absolute;top: 0;left: 0;height: 540rpx;width: 100%;background: linear-gradient(139deg, #FAEEF0 0%, #EFE9EE 24%, #E3EEFF 100%);z-index: -1;"></view>
  </navbar>
  <view class="topinfo-wrap" bindtap="navigator" data-url="/pages/user/userInfo/userInfo">
    <image wx:if="{{topInfo.headImgUrl||!has_login}}" class="head-img" src="{{has_login? topInfo.headImgUrl:'/static/logo.jpg'}}" style="{{has_login?'':'border-radius:0' }}"></image>
    <open-data wx:else class="head-img" type="userAvatarUrl"></open-data>
    <view class="info-box">
      <!-- <open-data wx:if="{{has_login&&!topInfo.nickName}}" class="nickname" type="userNickName"></open-data> -->
      <text class="nickname">{{topInfo.nickName?topInfo.nickName:'用户'+topInfo&&topInfo.cusId||''}}</text>
      <view style="font-size:24rpx;color:#666;margin-top:24rpx;display:flex;">点击查看资料<text style="font-size: 24rpx;margin-left: 2rpx;" class="uni-icon uni-icon-arrowright;"></text>
      </view>
    </view>
    <!--url pcsign -->
    <view wx:if="{{has_login}}" class="btn-sign" catchtap="navigator" data-url="/sign">
      <text>{{topInfo.has_sign?'已':''}}签到</text>
    </view>
  </view>
  <view class="coupons-wrap" wx:if="{{has_login}}" style="{{fbhistoryPoint>0?'padding-bottom: 20rpx;':''}}">
    <view bindtap="navigator" data-url="/pages/fubishop/index/index">
      <text class="value">{{topInfo.fubi}}</text>
      <view>福币</view>
      <text wx:if="{{fbhistoryPoint>0}}" style="color: #E60012;margin-top: 12rpx;font-size: 20rpx;">{{fbhistoryPoint}}个至年末过期</text>
    </view>
    <view bindtap="navigator" data-url="/pages/couponcenter/index/index">
      <text class="value">{{topInfo.countCoupon}}</text>
      <text>优惠券</text>
    </view>
    <view>
      <text class="value" bindtap="navigator" data-url="user/customer/fubiDetail">{{topInfo.totalMoney}}</text>
      <text>年消费</text>
    </view>
  </view>
  <view class="card" bindtap="opencard" style="background-image: url({{cardInfo[0].imgUrl}});">
    <view class="card-left">
      <view class="card-name-wrap"><text class="card-name">{{cardInfo[0].cardName}}</text><text class="cardid">{{topInfo.cusId}}</text></view>
      <text class="cardtime" wx:if="{{topInfo.disendDate}}">等级有效期至{{topInfo.disendDate}} </text>
    </view>
    <view class="card-right">
      <text class="discounts" style="background: {{cardInfo[0].bgCorlor}};">{{cardInfo[0].discounts}}</text>
    </view>
    <text class="uni-icon uni-icon-arrowright" style="margin-left: -4rpx;"></text>
  </view>
</view>
<!--  <view class="gradeDescription">
    <view>如何升级/降级保级?</view>
  </view> -->
<view wx:if="{{adList&&adList.length>0}}" class="func-part ad">
  <block wx:if="{{adList.length==1}}">
    <image bindtap="toad" data-url="{{adList[0].url}}" mode="widthFix" src="{{tools.jpg2jpeg(adList[0].img)}}" style="border-radius: 16rpx;"></image>
  </block>
  <block wx:else>
    <image bindtap="toad" data-url="{{adList[0].url}}" mode="widthFix" src="{{tools.jpg2jpeg(adList[0].img)}}" style="border-radius: 16rpx 0 0 16rpx;"></image>
    <image bindtap="toad" data-url="{{adList[1].url}}" mode="widthFix" src="{{tools.jpg2jpeg(adList[1].img)}}" style="border-radius: 0 16rpx 16rpx 0;"></image>
  </block>
</view>

<view class="func-part">
  <view class="common-title">
    <view class="title">我的订单</view>
    <!--   <view class="more">全部订单
        <text class="uni-icon uni-icon-arrowright" style="font-size: 24rpx;margin-top: 2rpx;"></text>
      </view> -->
  </view>
  <view wx:if="{{!limitShop}}" class="order-btn-box">
    <view wx:for="{{checkList}}" wx:key="id" class="{{index==orderCheckIndex?'active':''}}" bindtap="change_mall_type" data-index="{{index}}">
      <view wx:if="{{item.num&&index!=orderCheckIndex}}">{{item.num}}</view>
      <text>{{item.button}}</text>
    </view>
  </view>
  <view class="layout-func-row">
    <view wx:for="{{orderList[checkList[orderCheckIndex].type]}}" wx:key="id" class="layout-func-row-item cell-4" bindtap="to_order" data-url="{{item.tourl}}" data-title="{{item.title}}">
      <view class="mall-icon  f7 {{item.iconName}}">
        <view class="tip" wx:if="{{item.info}}">{{item.info}}</view>
      </view>
      <text class="item-func-title">{{item.title}}</text>
    </view>
  </view>
  <swiper class="order-info" wx:if="{{orderInfo&&orderInfo.length>0&&orderCheckIndex==0}}" autoplay duration="200" interval="3000">
    <swiper-item wx:for="{{orderInfo}}" wx:key="index">
      <view class="order-item" bindtap="toOrderDetail" data-i="{{index}}">
        <image wx:if="{{item.img}}" lazy-load mode="aspectFill" style="width: 80rpx;height: 80rpx;border-radius: 8rpx;margin-right: 10rpx;flex-shrink: 0;" src="{{tools.jpg2jpeg(item.img)}}"></image>
        <view wx:if="{{item.sendtType==1}}">
          <view>门店自提 - {{item.shopName}}</view>
          <view wx:if="{{item.countDown>0}}">剩余提货时间:
            <countDown time="{{item.countDown*1000}}" type="2" styles="display: inline;" />
          </view>
        </view>
        <view wx:else style="flex:1;overflow: hidden;">
          <view style="display: flex;justify-content: space-between;">
            <text>{{item.logisticStatus}}{{item.sendtType==5?' - '+item.shopName:''}}</text>
            <text style="color: #999;">{{tools.split(item.time,' ')[0]}}</text>
          </view>
          <view style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
            {{item.address}}
          </view>
        </view>
        <view catchtap="toTakingCode" data-i="{{index}}" wx:if="{{item.sendtType==1}}" class="take-code">提货码</view>
      </view>
    </swiper-item>
  </swiper>
</view>

<view class="vip-interest-box func-part">
  <view class="common-title">
    <view class="title" bindtap="{{!limitShop?'navigator':''}}" data-url="/pages/user/userRight/userRight">会员权益 <text wx:if="{{!limitShop}}" class="uni-icon uni-icon-help" style="color: #909399;font-weight: 400;"></text></view>
    <view wx:if="{{!limitShop}}" class="more" bindtap="navigator" data-url="h5user/showRights">
      <text class="moreTitle">会员制度</text>
      <text class="uni-icon uni-icon-arrowright" style="font-size: 28rpx;"></text>
    </view>
  </view>
  <view class="layout-func-row">
    <view wx:for="{{vip_qy}}" wx:key="id" class="layout-func-row-item cell-4" bindtap="navigator" data-module="会员权益" data-url="{{item.pageUrl}}" data-open="{{item.info==-1?true:false}}" data-title="{{item.title}}">
      <image wx:if="{{item.img}}" class="mall-icon " src="{{item.img}}">
      </image>
      <sficon wx:else name="{{item.iconName}}" size="28">
      </sficon>
      <text class="item-func-title">{{item.title}}</text>
    </view>
  </view>
</view>
<view class="func-part">
  <view class="common-title">
    <view class="title">我的服务</view>
  </view>
  <view class="layout-func-row">
    <view wx:for="{{serviceList}}" wx:key="id" class="layout-func-row-item cell-4" bindtap="navigator" data-module="我的服务" data-url="{{item.tourl}}" data-title="{{item.title}}">
      <view class="{{item.iconName}}">
        <view wx:if="{{item.info}}">{{item.info}}</view>
      </view>
      <text class="item-func-title">{{item.title}}</text>
    </view>
  </view>
</view>


<!--  <view class="func-part">
    <view class="common-title" style="padding-bottom:0;">
      <view class="title">我的任务</view>
    </view>
    <view class="taskLine" bindtap="navigator" data-url="h5wechat/jsp/user/askFriends.jsp?cus_id={{topInfo.cusId}}&2">
      <view class="">
        <view class="iconBorder" style="background:url('https://img.sanfu.com/sf_access/uploads/psejq66DxFYSVd6vVUBdewIiJdWWXsUs.png')"></view>
        <view class="inline-block">
          <view class="taskTitle">邀请好友</view>
          <view class="taskTip">邀请好友赢福币</view>
        </view>
      </view>
      <view class="taskBtn">去邀请</view>
    </view>
    <view class="taskLine" bindtap="navigator" data-url="h5wechat/user/showUseredit.htm">
      <view class="">
        <view class="iconBorder" style="background:url('https://img.sanfu.com/sf_access/uploads/Ex66TJLdWdM2fDacXop1tMhGPIOvXU46.png')"></view>
        <view class="inline-block">
          <view class="taskTitle">完善资料</view>
          <view class="taskTip">资料完善送福币</view>
        </view>
      </view>
      <view class="taskBtn">去完善</view>
    </view>
  </view> -->
<auth id="sanfu-auth" />
<sf-loading id="sf-loading" wx:if="{{showLoading}}" full />
<uni-popup zIndex="100" closeName="我的-生日礼弹框" type="center" show="{{birthImg}}" bindclose="closeBirth">
  <image src="{{birthImg}}" mode="widthFix" style="width: 550rpx;border-radius: 20rpx;position: relative;overflow: visible;" bindtap="toBirth">
    <text class="uni-icon uni-icon-close" style="position: absolute;color:#fff;font-size: 60rpx;z-index: 999;top: -20rpx;right: -20rpx;" catchtap="closeBirth"></text>
  </image>
</uni-popup>
