const app = getApp()
import Auth from '../../auth/index'
const p1 = {
  /**
   * 页面的初始数据
   */
  data: {
    viewCartList: '', // 购物车展示列表（shop-商品）
    cartInfo: {
      totalNum: 0,
      totalGoodsAmount: 0,
      totalOffAmount: 0,
      totalShippingFee: 0,
      totalPayAmount: 0,
      isCheckAll: false,
      customerDiscount: {},
      totalCouponFee: 0,
      couponLength: 0,
      totalGoodsFee: 0,
      totalDiscountMemberPrice: 0,
      totalDiscountPromoPrice: 0
    }, // 购物车其他信息
    editDel: 0,
    couponShow: 0,
    showSkeleton: true,
    shippingRule: false, // 邮费规则弹窗
    FeeToast: false, // 偏远地区邮费提示
    pageStatus: 1, // 0 正常 1loading 2未定位
    showDiscount: false,
    isBack: true, //是否显示顶部的返回按钮
    toggleIcon: false //控制失效列表下的收起图标的点击旋转动画
  },
  onLoad: function (options) {
    this.cartList = []
    this.isTabbarPage()
  },
  onShow: async function () {
    await app.waitSid()
  
    // //自定义底栏设置index
    // if (typeof this.getTabBar === 'function' && this.getTabBar()) {
    //   this.getTabBar().setData({
    //     selected: 3
    //   })
    // }
    if (app.local.get('dsho_id')) {
      this.setData({
        pageStatus: 1
      })
      this.getCartList()
    } else {
      if (!this.second) {
        this.checkLocation()
      }
      this.second = true
    }
    setTimeout(() => {
      this.setSkeleton(false)
    }, 2000)
    const limitShop = await app.util.checkLimitShop()
    this.setData({
      limitShop
    })
  },
  onReady() {
    if (!wx.getStorageSync('noShowFeeToast')) {
      wx.nextTick(() => {
        this.setData({
          FeeToast: true
        })
      })
    }
  },
  onHide() {
  },
  observer(e) {
    // 猜你喜欢曝光
    let item = e.currentTarget.dataset.item
    if (!item) return
    let prod_crdnt = {
      column: Number(e.detail / 2),
      row: Number(e.detail % 2)
    }
    app.report.exposeGoods({
      gooid: item.goodsSn,
      goodsName: item.goodsName,
      price_original: (item.qiangPrice || item.groupBuyPrice) ? item.salePrice || item.scPrice : item.scPrice,
      price_current: item.qiangPrice || item.groupBuyPrice || item.memberPrice || item.salePrice,
    })
  },
  checkLocation() {
    app.getLocation('', () => {
      if (app.local.get('dsho_id')) {
        this.setData({
          pageStatus: 1
        })
        this.getCartList()
      } else {
        this.setData({
          pageStatus: 2
        })
      }
    })
  },
  isTabbarPage() {
    let pages = getCurrentPages()
    if (pages.length > 0) {
      let prevPage = pages[pages.length - 1]
      if (prevPage.route === 'pages/cart/cart') {
        this.setData({
          isBack: false
        })
      }
    }
  },
  // 跳转到详情
  toDisplay(e) {
    let i = e.currentTarget.dataset.i
    let goods = this.cartList.itemList[i]
    // 未上架跳转
    if ((goods.goodsType == 7 || (goods.isValid == 0 && goods.fromCusId)) && !this.data.limitShop) {
      app.toH5(`scanpage?sku=${goods.gooId}&shop=${goods.shoId}&isComeScan=false&pageCard=${goods.fromCusId}&isShare=true`)
    } else app.toH5(`goods/goodsDisplay?goods_sn=${goods.gooId}`)
  },
  getCartList() {
    this.setLoading(1)
    let oldDshoid = wx.getStorageSync('oldDshoid')
    let needUpdateCartQiang = 1
    if (oldDshoid == app.local.get('dsho_id')) {
      needUpdateCartQiang = 0
    }
    wx.setStorageSync('oldDshoid', app.local.get('dsho_id'))

    app.reqGet('ms-sanfu-wap-cart/listNetSaleCart', {
      sid: app.local.get('sid'),
      shoId: app.local.get('dsho_id'),
      needUpdateCartQiang: needUpdateCartQiang
    },
      res => {
        // 获取购物车列表
        this.setLoading(0)
        this.setSkeleton(false)
        if (res.success) {
          this.dealData0(res)
          this.dealData()
        } else {
          this.setLoading(0)
          app.util.reqFail(res)
        }
      }
    )
  },
  closeTip() {
    this.setData({
      ['cartInfo.customerDiscount']: {}
    })
  },
  edit() {
    this.setData({
      editDel: !this.data.editDel
    })
  },
  maiZeng(e) {
    let item = e.currentTarget.dataset.item
    let shop = e.currentTarget.dataset.shop
    if (shop) return
    let url = item.isNext2 ? `/goods/pieceGoodsList?promoDtlId=${item.promoDtlId}&hasGift=${item.promoLabel == '买赠' ? true : false}` : `/goods/goodsList?promo_dtl_id_1=${item.promoDtlId}&hasGift=${item.promoLabel == '买赠' ? true : false}`
    app.toH5(url)
  },
  baoyou(e) {
    let m = e.currentTarget.dataset.name
    this.openH5(e)
  },

  shengji(e) {
    let my = e.currentTarget.dataset.name
    app.toH5(`goods/goodsList?caoudanFlag=1&disLogprice=${my}&isBestCd=2&is_super=1`)
  },
  manjian(e) {
    this.openH5(e)
    let m = e.currentTarget.dataset.name
  },
  shopCouponCallbackUse(e) {
    if (!e.detail) return
    this.setLoading(1)
    let couponId
    let checked
    couponId = e.detail.code
    checked = e.detail.checked
    if (checked) {
      checked = 0
    } else {
      checked = 1
    }
    // "buyType": 0,
    // "checked": 0,
    // "couponCode": "",
    // "freeCode": "",
    // "goodsId": "",
    // "groupBuyId": "",
    // "provinceId": 0,
    // "qiangId": "",
    // "shoId": "",
    // "sid": ""
    app.reqPost('ms-sanfu-wap-cart/checkUseCouponCount', {
      sid: app.local.get('sid'),
      shoId: wx.getStorageSync('dsho_id'),
      couponCode: couponId,
      checked: checked,
      type: 1
    },
      res => {
        this.setLoading(0)
        if (res.success) {
          this.dealData0(res)
          this.dealData()
        } else {
          app.util.reqFail(res)
        }
      }
    )
  },
  shopCouponCallbackGet(e) {
    let couponId = e.detail
    this.setLoading(1)
    // "groupBuyId": "",
    // "newUseCoupon": 0,
    // "provinceId": 0,
    // "qiangId": "",
    app.reqPost('ms-sanfu-wap-cart/sendCouponCount', {
      buyType: 0,
      shoId: wx.getStorageSync('dsho_id'),
      sid: app.local.get('sid'),
      typeId: couponId
    },
      res => {
        this.setLoading(0)
        if (res.success) {
          if (res.data.completeMsg) {
            wx.showModal({
              title: '温馨提示',
              content: '请完善会员信息，以便参与更多活动',
              success: res => {
                if (res.confirm) {
                  app.sf.track('click_complete_member_info')
                  app.toH5('wechat/user/showUseredit.htm?type=1')
                }
              }
            })
            return
          }
          this.dealData0(res)
          wx.showToast({
            icon: 'none',
            title: '领取成功'
          })
          this.dealData()
          app.subscribeMsg(2009, res.data.couponCode)
        } else {
          app.util.reqFail(res)
        }
      }
    )
  },
  openCoupon(e) {
    const item = this.cartList
    this.setData({
      couponShow: 1,
      availableCouponList: item.availableCouponList,
      shopCouponList: item.shopCouponList
    })
  },
  checkType7() {
    // 校验未上架
    let checkShop = 0
    for (let i = 0; i < this.data.shopList.length; i++) {
      let shop = this.data.shopList[i]
      if (shop.shoId) {
        let hasStatus = false
        for (let j = 0; j < shop.list.length; j++) {
          let promo = shop.list[j]
          for (let k = 0; k < promo.itemList.length; k++) {
            let item = promo.itemList[k]
            if (!item.isInvalid && item.cartStatus) {
              hasStatus = true
              checkShop++
              break
            }
          }
          if (hasStatus) break
        }
        if (checkShop > 1) {
          return true
        }
      }
    }
    return false
  },
  toCount() {
    if (this.cartList.length == 0) return
    if (this.data.cartInfo.totalPayAmount == 0) {
      wx.showToast({
        icon: 'none',
        title: '请勾选需要购买的商品'
      })
      return
    }
    if (this.checkType7()) {
      wx.showModal({
        title: '温馨提示',
        content: '门店特供商品不支持同一订单同时存在两家门店，请分别下单',
        showCancel: false
      })
      return
    }
    let bindcard = wx.getStorageSync('bindcard')
    if (bindcard == 4) {
      Auth(4, '', '', this.toCount)
      return
    }
    this.setLoading(1)
    this.trackCount()
    app.reqGet('ms-sanfu-wap-cart/countNetSaleCart', {
      sid: wx.getStorageSync('sid'),
      buyType: 0,
      shoId: wx.getStorageSync('dsho_id')
    },
      res => {
        this.setLoading(0)
        if (res.success) {
          app.local.set('countData', res, 60)
          app.toH5(`count`)
        } else {
          app.util.reqFail(res)
        }
      }
    )
  },
  trackCount() {
    //下单记录
    let goolist = []
    for (let i in this.cartList.itemList) {
      let item = this.cartList.itemList[i]
      if (item.cartStatus && item.isInvalid == 0) {
        app.report.submitOrder({
          gooid: item.gooId,
          goodsName: item.goodsName,
          barcode: item.goodsId,
          sku_name: item.colorName + ' ' + item.sizeName,
          price_current: item.salePrice,
          price_original: item.salePrice,
          shoid: '',
          sku_count: item.amount
        })
      }
    }
  },
  // 1.货号2.SKU3.商品名称4.商品价格
  checkOneGoods(e) {
    const i = e.currentTarget.dataset.i
    let item = this.cartList.itemList[i]
    let cartStatus = item.cartStatus
    let tmpCartStatus = 0
    if (cartStatus == 1) {
      tmpCartStatus = 0
    } else {
      tmpCartStatus = 1
    }
    this.setLoading(1)
    app.reqPost('ms-sanfu-wap-cart/checkNetSaleCart', {
      sid: app.local.get('sid'),
      goodsId: item.goodsId,
      cartStatus: tmpCartStatus,
      shoId: wx.getStorageSync('dsho_id')
    },
      res => {
        // 获取购物车列表
        this.setLoading(0)
        if (res.success) {
          this.dealData0(res)
          this.dealData()
        } else {
          app.util.reqFail(res)
        }
      }
    )
  },
  checkAllShop() {
    if (this.cartList.length == 0) return
    let cartStatus = this.data.cartInfo.isCheckAll
    var tmpCartStatus = 0
    if (cartStatus) {
      tmpCartStatus = 0
    } else {
      tmpCartStatus = 1
    }
    this.setLoading(1)
    app.reqPost('ms-sanfu-wap-cart/checkAllNetSaleCart', {
      sid: app.local.get('sid'),
      shoId: wx.getStorageSync('dsho_id'),
      cartStatus: tmpCartStatus
    },
      res => {
        // 获取购物车列表
        this.setLoading(0)
        if (res.success) {
          this.dealData0(res)
          this.dealData()
        } else {
          app.util.reqFail(res)
        }
      }
    )
  },
  clearGoods() {
    wx.showModal({
      title: '提示',
      content: '确认清除所有失效商品？',
      success: res1 => {
        this.setData({
          editDel: 0
        })
        if (res1.confirm) {
          this.setLoading(1)
          app.reqPost('ms-sanfu-wap-cart/deleteInvalidNetSaleCart', {
            sid: app.local.get('sid'),
            shoId: wx.getStorageSync('dsho_id')
          },
            res => {
              this.setLoading(0)
              if (res.success) {
                wx.showToast({
                  title: '清空成功',
                  icon: 'none'
                })
                this.dealData0(res)
                this.dealData()
              } else {
                app.util.reqFail(res)
              }
            }
          )
        }
      }
    })
  },
  delGoods() {
    wx.showModal({
      title: '提示',
      content: '确认删除已勾选商品？',
      success: res1 => {
        this.setData({
          editDel: 0
        })
        if (res1.confirm) {
          console.log(this.cartList.itemList)
          this.setLoading(1)
          this.deleteOtherGoods(this.cartList.itemList.map(e => e.goodsId).join())
          const track_list = this.cartList.itemList.filter(e => e.cartStatus == 1).map(e => {
            return {
              barcode: e.goodsId,
              goodsName: e.goodsName,
              price: e.qiangPrice || e.salePrice,
              action_num: e.amount,
              sku_name: e.colorName + ' ' + e.sizeName,
              action_type: 1,
              gooid: e.goodsId,
              price_original: e.qiangPrice || e.salePrice,
              price_current: e.qiangPrice || e.salePrice,
              srtype: 'remove_from_cart'
            }
          })
          app.reqPost('ms-sanfu-wap-cart/deleteAllNetSaleCart', {
            sid: app.local.get('sid'),
            shoId: wx.getStorageSync('dsho_id')
          },
            res => {
              // 获取购物车列表
              this.setLoading(0)
              if (res.success) {
                track_list.forEach(e => {
                  app.report.purchaseGoods(e)
                })
                this.dealData0(res)
                this.dealData()
              } else {
                app.util.reqFail(res)
              }
            }
          )
        }
      }
    })
  },
  tapDeleteOneGoods(e) {
    let i = e.currentTarget.dataset.i
    this.deleteOneGoods(i)
  },
  deleteOneGoods(i) {
    const item = this.cartList.itemList[i]
    wx.showModal({
      title: '提示',
      content: '确认删除该商品？',
      success: res1 => {
        // this.setData({
        //   editDel: 0
        // })
        if (res1.confirm) {
          this.setLoading(1)
          this.deleteOtherGoods(item.goodsId)
          app.reqPost('ms-sanfu-wap-cart/deleteNetSaleCart', {
            sid: app.local.get('sid'),
            goodsidStr: item.goodsId,
            shoId: wx.getStorageSync('dsho_id')
          },
            res => {
              // 获取购物车列表
              this.setLoading(0)
              if (res.success) {
                this.dealData0(res)
                this.dealData()
              } else {
                app.util.reqFail(res)
              }
            }
          )
          app.report.purchaseGoods({
            barcode: item.goodsId,
            action_num: item.amount,
            goodsName: item.goodsName,
            price_original: item.qiangPrice || item.salePrice,
            price_current: item.qiangPrice || item.salePrice,
            gooid: item.goodsId,
            sku_name: item.colorName + ' ' + item.sizeName,
            action_type: 1,
            srtype: 'remove_from_cart'
          })
        }
      }
    })
  },
  checkOpenSku(e) {
    let i = e.currentTarget.dataset.i
    let goods = this.cartList.itemList[i]
    // 未上架跳转 需优化
    if ((goods.goodsType == 7 || (goods.isValid == 0 && goods.fromCusId)) && !this.data.limitShop) {
      app.toH5(`scanpage?sku=${goods.gooId}&shop=${goods.shoId}&isComeScan=false&pageCard=${goods.fromCusId}&isShare=true`)
      return
    }
    this.tk_goods = goods
    this.tk_shoid = goods.goodsType == 7 || (goods.isValid == 0 && goods.fromCusId) ? goods.shoId : ''
    app.$bus.emit('setSkuGoods', {
      goods: goods,
      isBack: true,
      select: {
        barcode: goods.goodsId,
        colorName: goods.colorName,
        sizeName: goods.sizeName,
        amount: goods.amount,
        shoId: this.tk_shoid,
        imgUrl: goods.sImg
      }
    })
  },
  callback(e) {
    if (!e.detail) {
      wx.showToast({
        icon: 'none',
        title: '选择出错，请重试'
      })
      return
    }
    let detail = e.detail
    // console.log(shoId, oldBarcode, newBarcode, amount)
    var gooID = detail.newBarcode
    if (gooID.length > 6) {
      gooID = gooID.slice(0, 6)
    }
    this.addCartApi(gooID, detail.newBarcode, detail.amount, detail.name)
  },
  addCartApi(gooID, newBarcode, amount, name) {
    this.setLoading(1)
    const oldBarcode = this.tk_goods.goodsId
    app.reqPost('ms-sanfu-wap-cart/updateNetSaleCart', {
      sid: app.local.get('sid'),
      goodsId: oldBarcode,
      newGoodsid: newBarcode,
      amount: amount,
      gooId: gooID,
      shoId: this.tk_shoid || wx.getStorageSync('dsho_id'),
      updateType: this.tk_goods.amount < amount ? 0 : 1,
      locationShoId: wx.getStorageSync('dsho_id')
    },
      res => {
        // 获取购物车列表
        this.setLoading(0)
        if (res.success) {
          console.log(this.tk_goods)
          // 加购 减购埋点
          if (oldBarcode != newBarcode) {
            app.report.purchaseGoods({
              barcode: oldBarcode,
              action_num: this.tk_goods.amount,
              goodsName: this.tk_goods.goodsName,
              price_original: this.tk_goods.qiangPrice || this.tk_goods.salePrice,
              price_current: this.tk_goods.qiangPrice || this.tk_goods.salePrice,
              gooid: gooID,
              sku_name: this.tk_goods.colorName + ' ' + this.tk_goods.sizeName,
              action_type: 1,
              srtype: 'remove_from_cart'
            })
            app.report.purchaseGoods({
              barcode: newBarcode,
              action_num: amount,
              goodsName: this.tk_goods.goodsName,
              price_original: this.tk_goods.qiangPrice || this.tk_goods.salePrice,
              price_current: this.tk_goods.qiangPrice || this.tk_goods.salePrice,
              gooid: gooID,
              sku_name: name,
              action_type: 0,
              srtype: 'append_to_cart'
            })
          } else if (this.tk_goods.amount < amount) {
            app.report.purchaseGoods({
              barcode: newBarcode,
              action_num: amount - this.tk_goods.amount,
              goodsName: this.tk_goods.goodsName,
              price_original: this.tk_goods.qiangPrice || this.tk_goods.salePrice,
              price_current: this.tk_goods.qiangPrice || this.tk_goods.salePrice,
              gooid: gooID,
              sku_name: name,
              action_type: 0,
              srtype: 'append_to_cart'
            })
          } else {
            console.log()
            app.report.purchaseGoods({
              shoid: '',
              barcode: newBarcode,
              action_num: this.tk_goods.amount - amount,
              goodsName: this.tk_goods.goodsName,
              price_original: this.tk_goods.qiangPrice || this.tk_goods.salePrice,
              price_current: this.tk_goods.qiangPrice || this.tk_goods.salePrice,
              gooid: gooID,
              sku_name: name,
              action_type: 1,
              srtype: 'remove_from_cart'
            })
          }
          this.dealData0(res)
          this.dealData()
        } else {
          app.util.reqFail(res)
        }
      }
    )
  },
  checkNum(e) {
    let i = e.currentTarget.dataset.i
    let goods = this.cartList.itemList[i]
    let amount = goods.amount
    let type = e.currentTarget.dataset.type
    // 未上架跳转 需优化
    if ((goods.goodsType == 7 || (goods.isValid == 0 && goods.fromCusId)) && !this.data.limitShop) {
      app.toH5(`scanpage?sku=${goods.gooId}&shop=${goods.shoId}&isComeScan=false&pageCard=${goods.fromCusId}&isShare=true`)
      return
    }
    if (type == 1) {
      amount++
      app.report.purchaseGoods({
        shoid: '',
        barcode: goods.goodsId,
        action_num: 1,
        goodsName: goods.goodsName,
        price_original: goods.qiangPrice || goods.salePrice,
        price_current: goods.qiangPrice || goods.salePrice,
        gooid: goods.goodsId,
        sku_name: goods.colorName + ' ' + goods.sizeName,
        action_type: 0,
        srtype: 'append_to_cart'
      })
    } else if (type == 0) {
      amount--
      app.report.purchaseGoods({
        shoid: '',
        barcode: goods.goodsId,
        action_num: 1,
        goodsName: goods.goodsName,
        price_original: goods.qiangPrice || goods.salePrice,
        price_current: goods.qiangPrice || goods.salePrice,
        gooid: goods.goodsId,
        sku_name: goods.colorName + ' ' + goods.sizeName,
        action_type: 1,
        srtype: 'remove_from_cart'
      })
    }
    if (amount == 0) {
      this.deleteOneGoods(i) // index
    } else {
      this.setLoading(1)
      app.reqPost('ms-sanfu-wap-cart/updateNetSaleCart', {
        sid: app.local.get('sid'),
        goodsId: goods.goodsId,
        newGoodsid: goods.goodsId,
        amount: amount,
        shoId: goods.goodsType == 7 || (goods.isValid == 0 && goods.fromCusId) ? goods.shoId : wx.getStorageSync('dsho_id'),
        gooId: goods.gooId,
        updateType: type == 1 ? 0 : 1,
        locationShoId: wx.getStorageSync('dsho_id')
      },
        res => {
          // 获取购物车列表
          this.setLoading(0)
          if (res.success) {
            this.dealData0(res)
            this.dealData()
          } else {
            app.util.reqFail(res)
          }
        }
      )
    }
  },
  dealData0(res) {
    // disFreeShippingfee:
    // payFee: 最终支付金额
    // provinceFee:
    // shippingFee:
    // totalCouponFee: 优惠券总额
    // totalDiscountFee: 促销总优惠+会员总优惠+优惠券总额
    // totalDiscountMemberPrice:  会员总优惠
    // totalDiscountPromoPrice: 促销总优惠
    // totalGoodsFee: 商品原总价
    this.cartList = (res.data.netSaleCartList && res.data.netSaleCartList[0]) || {
      itemList: []
    }
    this.cartList.nextPromo = res.data.nextPromo
    res.data.totalDiscountFee = res.data.totalDiscountFee - res.data.totalCouponFee + res.data.totalRealCouponFee
    this.data.cartInfo = {
      totalNum: res.data.totalNum,
      totalGoodsAmount: res.data.payFee,
      totalOffAmount: res.data.totalDiscountFee,
      totalShippingFee: res.data.totalShippingFee,
      totalPayAmount: res.data.totalPayAmount,
      isCheckAll: res.data.isCheckAll,
      customerDiscount: res.data.customerDiscount,
      totalCouponFee: res.data.totalCouponFee,
      couponLength: res.data.checkedCouponList.length,
      totalGoodsFee: res.data.totalGoodsFee,
      totalDiscountMemberPrice: res.data.totalDiscountMemberPrice,
      totalDiscountPromoPrice: res.data.totalDiscountPromoPrice,
      totalRealCouponFee: res.data.totalRealCouponFee
    }
  },
  async dealData() {
    this.goodsList = [] // 购物车推荐列表
    let currTime = Date.now() + (await app.util.getTimeDiff())
    // 失效商品
    let isInvalidList = []
    // 包邮展示标识
    this.cartList.showFreeShipping = false

    this.cartList.promoList = []
    // // 无促销列表
    // let noPromo = {
    //   itemList: []
    // }
    // 失效列表
    let isInvalidItem = {
      itemList: []
    }
    const allgoodsId = []
    this.cartList.itemList.forEach((e, eIndex) => {
      allgoodsId.push(e.goodsId)
      e.index = eIndex
      //包邮展示判断
      if (e.cartStatus) this.cartList.showFreeShipping = true

      // 失效商品判断
      if (e.isInvalid === 1) {
        e.isQiang = false
        isInvalidList.push(e)
      } else {
        this.goodsList.push({
          goodsSn: e.gooId,
          isSelected: e.cartStatus
        })
      }
      // 预售处理
      if (e.preSalePlanInfo) {
        let preSaleTime = new Date(e.preSalePlanInfo.estimateConTime)
        e.preSaleTime = `预计${preSaleTime.getMonth() + 1}月${preSaleTime.getDate()}日前发货`
      }
      delete e.preSalePlanInfo
      // 秒杀商品判断+倒计时
      if (e.isQiang) {
        console.log(e)
        this.cartList.isQiang = true
        e.goodsPromoList = []
        e.add_price = e.salePrice
        e.salePrice = e.qiangPrice
        e.qiangEndTime = e.qiangEndTime.replace(/\.([\d\D]*)/g, '')
        e.qiangEndTime = e.qiangEndTime.replace(/-/g, '/')
        e.countTime = new Date(e.qiangEndTime).getTime()
        if (e.countTime == 'Invalid Date' || !e.countTime) {
          e.qiangEndTime = e.qiangEndTime.replace(/-/g, '/')
          e.countTime = new Date(e.qiangEndTime).getTime()
        }
        e.countTime = e.countTime - currTime
      }
      // 价格保留小数
      // e.salePrice = app.util.toDecimal2(e.salePrice)
      // e.realPrice = app.util.toDecimal2(e.realPrice)
    })
    // 促销处理
    const allShop = await app.getAllShop()
    const dshoId = wx.getStorageSync('dsho_id') // 区域店
    this.cartList.itemList.forEach(e => {
      if (e.isInvalid) return // 失效跳过
      // 阈值促销过滤（加进数组）
      let farr = this.cartList.nextPromo.filter(el => el.goodsId === e.goodsId)
      if (farr.length > 0) {
        for (let j in farr) {
          farr[j].isNext = true
          farr[j].isNext2 = true
          if (farr[j].status == 3) {
            farr[j].isNext = false
            farr[j].isNext2 = false
          }
          farr[j].promoLevel = 10
          if (farr[j].shoId && dshoId != farr[j].shoId) {
            farr[j].shoId = e.shoId
            farr[j].promoDtlId += '-' + farr[j].shoId
          }
          e.goodsPromoList.push({
            ...farr[j]
          })
        }
      }
      // if(e.cartStatus==0)
      if (e.goodsPromoList.length > 0 && !e.isQiang) {
        // 过滤秒杀
        // 促销处理（总）
        // 按等级排序
        let goodsPromo2 = e.goodsPromoList.sort((a, b) => b.promoLevel - a.promoLevel)
        // 取最大
        let goodsPromo = goodsPromo2[0]
        // 未上架店判断
        if ((goodsPromo.shoId && dshoId != goodsPromo.shoId) || e.goodsType == 7 || (e.isValid == 0 && e.fromCusId)) {
          goodsPromo.shoId = e.shoId
          goodsPromo.promoDtlId += '-' + goodsPromo.shoId
          goodsPromo.type7 = true
        }
        if (!e.cartStatus) goodsPromo.isNext = true
        delete e.goodsPromoList
        let index = this.cartList.promoList.findIndex(el => el.promoDtlId === goodsPromo.promoDtlId)
        if (index == -1) {
          goodsPromo.itemList = []
          // isNext处理 含未勾选情况
          goodsPromo.isNext = goodsPromo.isNext || false
          goodsPromo.itemList.push(e)
          this.cartList.promoList.push(goodsPromo)
        } else {
          if (!goodsPromo.isNext) this.cartList.promoList[index].isNext = false
          if (goodsPromo.selfPromoName) this.cartList.promoList[index].selfPromoName = goodsPromo.selfPromoName
          this.cartList.promoList[index].itemList.push(e)
        }
      } else {
        // 未上架
        let type7
        if (e.goodsType == 7 || (e.isValid == 0 && e.fromCusId)) {
          type7 = true
        }
        this.cartList.promoList.push({
          itemList: [e],
          type7,
          shoId: e.shoId
        })
        console.log(e.shoId);
        // noPromo.itemList.push(e)
      }
      // console.log({
      //   ...e
      // });
    })
    // 重新封装未上架（店）
    const shopList = []
    const noshopList = []
    this.cartList.promoList.forEach(item => {
      if (item.type7) {
        let index = shopList.findIndex(e => e.shoId === item.shoId)
        if (index > -1) {
          shopList[index].list.push(item)
        } else {
          const shop = allShop.find(e => e.shoId == item.shoId) || ''
          shopList.push({
            shoName: shop.shoName || item.shoId,
            shoId: item.shoId,
            list: [item]
          })
        }
      } else {
        noshopList.push(item)
      }
    })
    shopList.push({
      list: noshopList
    })
    // this.cartList.promoList.push(noPromo)
    // this.data.viewCartList = this.cartList
    this.data.viewCartList = JSON.parse(JSON.stringify(this.cartList))
    delete this.data.viewCartList.availableCouponList
    delete this.data.viewCartList.checkedBarcodeList
    delete this.data.viewCartList.expressWayList
    delete this.data.viewCartList.invoice
    delete this.data.viewCartList.isCheckAllCoupon
    delete this.data.viewCartList.isShowImg
    delete this.data.viewCartList.itemList
    delete this.data.viewCartList.latitude
    delete this.data.viewCartList.longitude
    delete this.data.viewCartList.notAvailableCouponList
    delete this.data.viewCartList.shopCouponList
    delete this.data.viewCartList.isShowCouponList
    delete this.data.viewCartList.jsdFreeShippingName
    delete this.data.viewCartList.freeCode
    delete this.data.viewCartList.sendType
    delete this.data.viewCartList.shoId
    delete this.data.viewCartList.shoName
    delete this.data.viewCartList.shoName
    delete this.data.viewCartList.promoList
    // this.data.viewCartList.promoList.forEach(j => {
    //   j.itemList.forEach(k => {
    //     delete k.goodsNextPromo
    //     delete k.cartType
    //     delete k.categories
    //     delete k.chanId
    //     delete k.channelId
    //     delete k.cid
    //     delete k.clientTypeId
    //     delete k.concPrice
    //     delete k.createTime
    //     delete k.cusId
    //     delete k.fromCusId
    //     delete k.groupBuyId
    //     delete k.groupId
    //     delete k.imgPath
    //     delete k.liveRoomId
    //     delete k.liveUserId
    //     delete k.memberPercent
    //     delete k.newGoodsid
    //     delete k.off
    //     delete k.promPrice
    //     delete k.promotionIds
    //     delete k.qiangEndTime
    //     delete k.qiangId
    //     delete k.qiangStarttime
    //     delete k.wxShareId
    //   })
    // })
    console.log(this.data.viewCartList)
    // console.log(this.data.viewCartList);
    // console.log(this.cartList);
    this.getOtherList(allgoodsId.join())
    this.setData({
      shopList,
      cartInfo: this.data.cartInfo,
      viewCartList: this.data.viewCartList,
      isInvalidList: isInvalidList,
      disableDelete: true
    })
    wx.nextTick(() => {
      this.setData({
        disableDelete: false
      })
    })
    this.getRecGoods()
  },
  closeCouponShow(e) {
    //关闭优惠券弹窗
    if (!e.detail || !e.detail.show)
      this.setData({
        couponShow: 0
      })
  },

  openH5(e) {
    app.toH5(e.currentTarget.dataset.url)
  },
  setLoading(bool) {
    if (this.data.pageStatus == bool) return
    wx.nextTick(() => {
      this.setData({
        pageStatus: bool
      })
    })
  },
  setSkeleton(bool) {
    if (this.data.showSkeleton == bool) return
    this.setData({
      showSkeleton: bool
    })
  },
  getPromo(e) {
    app.reqGet('ms-sanfu-wap-goods-index/listIndexShopPromo', {
      num: 2,
      sid: wx.getStorageSync('sid'),
      shoId: wx.getStorageSync('dsho_id')
    }, res => {
      if (res.success) {
        let list = res.data || []
        if (list.length % 2 != 0) list.pop()
        this.setData({
          guessPromo: list,
          guessGoods: this.data.guessGoods
        })
      }
    })
  },
  getRecGoods() {
    app.reqPost('ms-sanfu-wap-goods/listBuyRecommendGoods', {
      goodsList: this.goodsList || [],
      sid: wx.getStorageSync('sid'),
      shoId: wx.getStorageSync('dsho_id')
    }, res => {
      if (res.success) {
        let list = res.data || []
        if (list.length % 2 != 0) list.pop()
        this.data.guessGoods = list
        this.getPromo()
      }
    })
  },
  // 跳转到详情
  toGuessGoods(e) {
    let id = e.currentTarget.dataset.id
    let goods = e.currentTarget.dataset.item
    app.toH5(`goods/goodsDisplay?goods_sn=${id}`)
  },
  toPromo(e) {
    let id = e.currentTarget.dataset.id
    let title = e.currentTarget.dataset.title || ''
    app.toH5(`goods/goodsList?promo_dtl_id_1=${id}&navTitleName=${title}`)
  },
  finishedCountDown(e) {
    this.getCartList()
  },
  showShippingRule(e) {
    let price = e.currentTarget.dataset.price
    this.setData({
      shippingRule: e.type == 'close' ? 0 : !this.data.shippingRule
    })
  },
  closeFeeToast() {
    wx.setStorageSync('noShowFeeToast', true)
    this.setData({
      FeeToast: false
    })
  },
  toPriceLog(e) {
    // 点击查看特殊运费
    let price = this.data.viewCartList.shippingfeeAmount
    wx.navigateTo({
      url: '/pages/goods/specialPriceLog/specialPriceLog?price=' + price
    })
  },
  toShowDiscount(e) {
    let type = e.currentTarget.dataset.type
    if (type) {
      this.setData({
        showDiscount: true,
        showBottom: true,
        isRotate: 1
      })
    } else {
      this.setData({
        showDiscount: false,
        isRotate: 0
      })
      wx.nextTick(() => {
        setTimeout(() => {
          if (!this.data.showDiscount)
            this.setData({
              showBottom: false
            })
        }, 300)
      })
    }
  },
  toShare() {
    wx.navigateTo({
      url: '/pages/goods/cartShare/cartShare'
    })
  },
  toggleFun() {
    this.setData({
      toggleIcon: !this.data.toggleIcon
    })
  },
  getOtherList(ids) {
    return
    setTimeout(() => {
      wx.nextTick(() => {
        this.oCart = this.selectComponent('#moreCart')
        console.log('getOtherListgetOtherListgetOtherList', this.oCart);
        if (!this.oCart) return
        this.oCart.start(ids)
      })
    }, 200)
  },
  deleteOtherGoods(ids) {
    if (!this.oCart) return
    this.oCart.deleteOtherGoods(ids)
  },
  saveDisableList(e) {
    // 暂时不用
    // this.disableList = e.detail
    // this.disableList.forEach(e => {
    //   e.isInvalid = true
    // })
    // this.setData({
    //   isInvalidList: [...this.data.isInvalidList, ...this.disableList]
    // })
    // console.log(e.detail);
  }
}
module.exports = p1
