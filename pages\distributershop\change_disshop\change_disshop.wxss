view{
  box-sizing: border-box;
}
.container{
  padding:0 30rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.item{
  height: 80rpx;
  padding: 0 10rpx;
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #ececec;
}


.item .title{
  font-size: 32rpx;
  color: #202020;
  font-weight: 700;
  min-width: 200rpx;
}

.item .other{
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  color: #333;
  font-size: 30rpx;
}

.item .other input{
  width: 100%;
}

.btn{
  margin-top: 80rpx;
  margin-bottom: 40rpx;
  min-width: 180rpx;
  padding: 16rpx 36rpx;
  border: 2rpx solid #8f6aff;
  color: #8f6aff;
  border-radius: 80rpx;
  font-size: 36rpx; 
  /* font-weight: 700; */
  align-self: center;
}

.btn-hover{
  background: #ececec;
}