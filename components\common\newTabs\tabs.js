const app = getApp()
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
    addGlobalClass: true,
    multipleSlots: true,
    virtualHost: true
  },
  /**
   * @index
   * @list {Array}展示列表
   * @key {String} 列表索引
   * @lineWidth {Number} 标签宽
   * @lineHeight {Number} 标签高
   * @title {String} 标题内容
   * @showTitle {Boolean} showTitle = [false|true] 显示标题 
   * @statusBarHeight {String} statusBarHeight 猜你喜欢的tab粘连到顶部header的距离
   * @themeType {Number} 主题类型：1猜你喜欢 0附近门店详情或其他
   */
  properties: {
    index: {
      type: Number,
      value: 0
    },
    list: {
      type: Array,
      value: []
    },
    key: {
      type: String,
      value: ''
    },
    lineWidth: {
      type: Number,
      value: -1
    },
    lineHeight: {
      type: Number,
      value: -1
    },
    color: {
      type: String,
      value: '#f00'
    },
    tabStyle: {
      type: String,
      value: ''
    },
    activeStyle: {
      type: String,
      value: ''
    },
    bg: {
      type: String,
      value: ''
    },
    duration: {
      type: Number,
      value: 0.2
    },
    sticky: {
      type: Boolean,
      value: false
    },
    styles: {
      type: String,
      value: ''
    },
    themeType:{
      type: Number
    }
  },
  data: {
    active: -1,
    scrollLeft: 0,
    lineStyle: ''
  },
  attached() {},
  detached() {},
  watch: {
    'list': function() {
      this.setActive(this.properties.index || 0)
    },
    'index': function(newVal) {
      this.setActive(this.properties.index)
    }
  },
  methods: {
    onChange(e) {
      let active = e.currentTarget.dataset.index
      this.triggerEvent('change', active);
      this.setActive(active)
    },
    setActive(active) {
      if (this.data.list.length == 0) return
      if (active > this.data.list.length - 1) active = this.data.list.length - 1
      if (this.data.key ? !this.data.list[active][this.data.key] : !this.data.list[active]) active = -1
      if (active == this.data.active) return
      this.setData({
        active
      })
      if (active >= 0) {
        setTimeout(() => {
          this.setLine()
        }, 100)
        this.scrollIntoView()
      } else {
        this.setData({
          lineStyle: 'display:none'
        })
      }
    },
    async setLine(skipTransition) {
      const {
        color,
        active,
        duration,
        lineWidth,
        lineHeight
      } = this.data;
      let rects = await this.getRect('.tab', true) || {}
      const rect = rects[active];
      const width = lineWidth !== -1 ? lineWidth : rect.width / 3;
      const height = lineHeight !== -1 ? `height: ${lineHeight}px;` : '';
      let left = rects.slice(0, active).reduce((prev, curr) => prev + curr.width, 0);
      left += (rect.width - width) / 2;
      const transition = skipTransition ? '' : `transition-duration: ${duration}s; -webkit-transition-duration: ${duration}s;`;
      this.setData({
        lineStyle: `
            ${height};
            width: ${width}px;
            -webkit-transform: translateX(${left}px);
            transform: translateX(${left}px);
            ${transition}
          `
      });
    },
    // scroll active tab into view
    async scrollIntoView() {
      const {
        active
      } = this.data;
      let tabRects = await this.getRect('.tab', true) || {}
      let navRect = await this.getRect('.tabnav') || {}

      const tabRect = tabRects[active];
      const offsetLeft = tabRects
        .slice(0, active)
        .reduce((prev, curr) => prev + curr.width, 0);

      this.setData({
        scrollLeft: offsetLeft - (navRect.width - tabRect.width) / 2
      });

    },
    getRect(selector, all) {
      return new Promise(resolve => {
        wx.createSelectorQuery()
          .in(this)[all ? 'selectAll' : 'select'](selector)
          .boundingClientRect(rect => {
            if (all && Array.isArray(rect) && rect.length) {
              resolve(rect);
            }
            if (!all && rect) {
              resolve(rect);
            }
          })
          .exec();
      });
    }
  }
});
