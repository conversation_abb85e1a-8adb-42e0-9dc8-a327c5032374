@import "/font/icon_f7.wxss";
.promo-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 20rpx;
}

.promo-item {
  background: #fff;
  border-radius: 12rpx;
  width: 100%;
  height: 220rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-top: 30rpx;
  margin-bottom: 12rpx;
  box-shadow: 2rpx 2rpx 10rpx #dfdfdf;
  position: relative;
  padding: 18rpx;
  padding-left: 220rpx;
}



.promo-item .promo-img {

  position: absolute;
  width: 180rpx;
  height: 180rpx;
  left: 20rpx;
  top: 50%;
  margin-top: -90rpx;
  border-radius: 12rpx;
  border: 2rpx solid #ececec;
  box-shadow: 0rpx 0rpx 10rpx #dfdfdf;
  box-sizing: border-box;

}

.promo-name {
  color: #3b3b3b;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  text-overflow: ellipsis;
  font-size: 30rpx;
  line-height: 1.35;
}

.promo-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #888;
  font-size: 24rpx;
}

.promo-bottom-btn {
  display: flex;
  border-radius: 24rpx;
  border: 2rpx solid #fd6c90;
  color: #fd6c90;
  padding: 4rpx 12rpx;
  align-items: center;
  justify-content: center;
  min-width: 170rpx;
}

.hover {
  background: #f3f3f3;
}

/* 二维码相关 */

.qrcode {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.shareCanvas {
  background: none;
}

.dis-top {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
}

.dis-top view {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  margin: 0 !important;
  padding: 10px 0;
  color: #555; 
  border-radius: 50px;
  box-shadow: 1px 1px 3px #999;
  flex:1;
}

.dis-top button {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  font-size: 13px;
  margin-left: 10px;
  padding: 12px 0;
  color: #555; 
  border-radius: 50px;
  box-shadow: 1px 1px 3px #999;
  line-height: 1 !important;
  background: #fff;
}
