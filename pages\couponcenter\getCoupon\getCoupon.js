// pages/couponcenter/getCoupon/getCoupon.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    status: '',
    couponId: '',
    type: '',
    msg: '',
    loaded: false,
    loading: false,
    opt: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.data.status = options.status || ''
    app.apploading(app)
    let opt = wx.getEnterOptionsSync()
    this.setData({
      opt: JSON.stringify(opt)
    })
    console.log(opt);
  },
  onShow: function() {
    if (wx.getStorageSync('sid')) this.init()
  },
  init() {
    if (this.data.loaded) return
    this.setData({
      loaded: true
    })
    this.getCouponId()
  },
  getCouponId() {
    this.setData({
      loading: true
    })
    app.reqGet('/ms-sanfu-wechat-coupon/coupon/linkSendCouponAndReturnActiveStatus', {
      sid: wx.getStorageSync('sid'),
      id: this.data.status
    }, res => {
      this.setData({
        loading: false
      })
      if (res.success) {
        this.setData({
          type: res.data.type,
          msg: res.data.message
        })
      } else {
        this.setData({
          type: 'exp',
          msg: res.msg || JSON.stringify(res)
        })
      }
    })
  },
 toCouponList() {
   wx.redirectTo({
     url: '/pages/couponcenter/index/index'
   })
 },

})
