import IntersectionObserver from '../../../utils/intersection-observer.js';

const app = getApp();

Component({
  externalClasses: ['class'],
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'apply-shared'
  },
  properties: {
    // 图片路径
    src: {
      type: String,
      value: ''
    },
    // 图片宽度
    width: {
      type: String,
      value: '100%'
    },
    // 图片高度
    height: {
      type: String,
      value: 'auto'
    },
    style: {
      type: String,
      value: ''
    },
    // 图片裁剪模式
    mode: {
      type: String,
      value: 'aspectFill'
    },
    // 是否使用webp格式
    webp: {
      type: Boolean,
      value: true
    },
    // 是否开启懒加载
    lazyLoad: {
      type: Boolean,
      value: true
    },
    // 占位图
    placeholder: {
      type: Boolean,
      value: true
    },
    // 错误占位图
    errorPlaceholder: {
      type: String,
      value: ''
    },
    // 错误背景色
    errorBg: {
      type: String,
      value: '#f5f5f5'
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      value: true
    },
    // 是否允许长按菜单
    showMenuByLongpress: {
      type: Boolean,
      value: false
    }
  },
  data: {
    shouldLoad: false, // 是否应该加载图片
    loaded: false, // 图片是否已加载
    error: false, // 是否加载出错
  },
  lifetimes: {
    attached() {
      // 如果不需要懒加载，直接开始加载流程
      if (!this.data.lazyLoad || !wx.canIUse('createIntersectionObserver')) {
        this.startImageLoad();
        return;
      }

      // 初始化观察者
      this.initObserver();
    },
    detached() {
      // 组件销毁时，断开观察者
      if (this.observer) {
        this.observer.disconnect();
        this.observer = null;
      }

      // 从加载队列中移除
      if (this.data.sequentialLoad) {
        this.removeFromQueue();
      }
    }
  },
  methods: {
    // 初始化观察者
    initObserver() {
      const { instanceId } = this.data;

      // 创建观察者，距离页面20px时开始加载
      this.observer = new IntersectionObserver({
        selector: `#img`,
        context: this,
        threshold: 0.0000001, // 只要有一点出现在视口中就触发
        initialRatio: 0,
        viewport: {
          bottom: 80, // 距离底部20px时触发
          top: 50, // 距离顶部20px时触发
          left: 30,
          right: 30
        },
        onEach: () => {
          console.log('onEachonEach', this.data.src);
          // 图片进入视口，开始加载
          this.startImageLoad();

          // 加载后断开观察者
          if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
          }
        }
      });

      // 连接观察者
      this.observer.connect();
    },

    // 开始图片加载
    startImageLoad() {
      // 直接加载模式
      this.setData({ shouldLoad: true });
    },

    // 图片加载完成
    onImageLoad(e) {
      this.setData({
        loaded: true,
        error: false
      });
      this.triggerEvent('load', e.detail);
    },

    // 图片加载失败
    onImageError(e) {
      this.setData({
        error: true
      });


      this.triggerEvent('error', e.detail);
    },

    // 重试加载
    retryLoad() {
      this.setData({
        error: false
      });
      this.startImageLoad();
    }
  }
});
