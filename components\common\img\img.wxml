<wxs src="/utils/utils.wxs" module="utils"></wxs>
<view wx:if="{{!shouldLoad}}" id="img" style="min-height: 2px; {{style}}" class="class">
  <!-- 占位符 -->
  <view wx:if="{{!loaded && placeholder &&!shouldLoad}}" class="img-placeholder">
    <!-- <image wx:if="{{placeholder}}" src="{{placeholder}}" mode="aspectFill " style="width: 100%; height: 100%;" /> -->
    <!-- <text class="placeholder-text" style="color: #999; font-size: 24rpx;">加载中...</text> -->
  </view>
</view>
<!-- 实际图片 -->
<image wx:elif="{{shouldLoad}}" class="img-main {{loaded ? 'img-loaded' : ''}} class" src="{{utils.jpg2jpeg(src)}}" mode="{{mode || ''}}" webp="{{webp}}" show-menu-by-longpress="{{showMenuByLongpress}}" bindload="onImageLoad" binderror="onImageError" style="{{style}}">
  <!-- 支持slot，允许在image内部添加内容 -->
  <slot></slot>
</image>

<!-- 错误状态 -->
<view wx:elif="{{error}}" class="img-error" style="width: 100%; height: 100%; background: {{errorBg || '#f5f5f5'}}; display: flex; align-items: center; justify-content: center; flex-direction: column;">
  <image wx:if="{{errorPlaceholder}}" src="{{errorPlaceholder}}" mode="aspectFit" style="max-width: 60%; max-height: 60%;" />
  <text wx:else style="color: #999; font-size: 24rpx;">加载失败</text>
  <text wx:if="{{showRetry}}" class="retry-btn" bindtap="retryLoad" style="color: #007aff; font-size: 24rpx; margin-top: 10rpx;">点击重试</text>
</view>
