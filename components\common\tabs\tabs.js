const app = getApp()
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
    addGlobalClass: true,
    multipleSlots: true,
    virtualHost: true
  },
  /**
   * @index
   * @list {Array}展示列表
   * @key {String} 列表索引
   * @lineWidth {Number} 标签宽
   * @lineHeight {Number} 标签高
   * @title {String} 标题内容
   * @showLine {Boolean} showLine = [false|true] 显示激活条 
   * @lineWidth
   * @color
   * @tabStyle
   * @activeStyle
   * @bg
   * @duration
   * @top
   * @styles
   * @fontStyle
   * @isFlex  是否flex排列(计算位移方法变化)
   */
  properties: {
    index: {
      type: Number,
      value: 0
    },
    list: {
      type: Array,
      value: []
    },
    key: {
      type: String,
      value: ''
    },
    lineWidth: {
      type: Number,
      value: -1
    },
    lineHeight: {
      type: Number,
      value: -1
    },
    color: {
      type: String,
      value: '#f00'
    },
    tabStyle: {
      type: String,
      value: ''
    },
    activeStyle: {
      type: String,
      value: ''
    },
    bg: {
      type: String,
      value: ''
    },
    duration: {
      type: Number,
      value: 0.2
    },
    sticky: {
      type: Boolean,
      value: false
    },
    top: {
      type: String,
      value: '0'
    },
    styles: {
      type: String,
      value: ''
    },
    fontStyle: {
      type: String,
      value: ''
    },
    showLine: {
      type: Boolean,
      value: true
    },
    lineStyle: {
      type: String,
      value: ''
    },
    style2: {
      type: String,
      value: ''
    },
    isFlex: {
      type: Boolean,
      value: false
    },
    tapChange: {
      type: Boolean,
      value: true
    }
  },
  data: {
    active: -1,
    scrollLeft: 0,
    linePosition: ''
  },
  attached() {},
  detached() {},
  watch: {
    'list': function() {
      this.setActive(this.properties.index || 0)
    },
    'index': function(newVal) {
      this.setActive(this.properties.index)
    }
  },
  methods: {
    onChange(e) {
      let active = e.currentTarget.dataset.index
      this.triggerEvent('change', active);
      if (!this.data.tapChange) return
      this.setActive(active)
    },
    setActive(active) {
      if (this.data.list.length == 0) return
      if (active > this.data.list.length - 1) active = this.data.list.length - 1
      if (this.data.key ? !this.data.list[active][this.data.key] : !this.data.list[active]) active = -1
      if (active == this.data.active) return
      this.setData({
        active
      }, () => {
        if (active >= 0) {
          if (this.data.showLine) {
            // wx.nextTick(() => {
            this.setLine()
            // })
          }
          this.scrollIntoView()
        } else {
          this.setData({
            linePosition: 'display:none'
          })
        }
      })

    },
    async setLine(skip) {
      const {
        color,
        active,
        duration,
        lineWidth,
        lineHeight
      } = this.data;
      let tabbox = await this.getRect('#tab-box', true) || []
      tabbox = tabbox[0] || {
        left: 0
      }
      let rects = await this.getRect('.tab', true) || {}
      const rect = rects[active];
      // console.log(rect);
      // console.log(rect.left, tabbox.left,rect.width);
      const width = lineWidth !== -1 ? app.util.rpx2px(lineWidth) : rect.width / 3;
      const height = lineHeight !== -1 ? `height: ${lineHeight}rpx;` : '';
      let left = rects.slice(0, active).reduce((prev, curr) => prev + curr.width, 0);
      if (this.data.isFlex) left = rect.left - tabbox.left
      left += (rect.width - width) / 2;
      this.setData({
        linePosition: `
            ${height};
            width: ${width}px;
            transform: translateX(${left}px);
            transition-duration: ${skip?duration/2:duration}s;
          `
      });
      if (!skip) {
        wx.nextTick(() => {
          setTimeout(() => {
            this.setLine(1)
          }, 150)
        })
      }
    },
    // scroll active tab into view
    async scrollIntoView() {
      const {
        active
      } = this.data;
      let tabRects = await this.getRect('.tab', true) || {}
      let navRect = await this.getRect('.tabnav') || {}

      const tabRect = tabRects[active];
      const offsetLeft = tabRects
        .slice(0, active)
        .reduce((prev, curr) => prev + curr.width, 0);
      this.setData({
        scrollLeft: offsetLeft - (navRect.width - tabRect.width) / 2
      });

    },
    getRect(selector, all) {
      return new Promise(resolve => {
        wx.createSelectorQuery()
          .in(this)[all ? 'selectAll' : 'select'](selector)
          .boundingClientRect(rect => {
            if (all && Array.isArray(rect) && rect.length) {
              resolve(rect);
            }
            if (!all && rect) {
              resolve(rect);
            }
          })
          .exec();
      });
    }
  }
});
