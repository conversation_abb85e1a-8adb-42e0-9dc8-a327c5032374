<wxs module="filters" src="./pubwxs.wxs"></wxs>
<view class="cu-custom" style="height:{{CustomBar}}px">
  <view class="cu-bar fixed bg-f8f8f8" style="height:{{CustomBar}}px;padding-top:{{StatusBar}}px;">
    <view class="action" bindtap="BackPage" wx:if="{{newsid>0}}">
      <view>
        <image lazy-load src="https://snsimg.sanfu.com/sanfu1653903884323.png" style="width: 32rpx; height:32rpx;"></image>
      </view>
    </view>
    <view class="content" style="top:{{StatusBar}}px">好物圈</view>
    <slot name="right"></slot>
  </view>
</view>
<block>
  <!-- 搜索模块入口 -->
  <view class="cu-bar tabs  pt-20 pb-20">
    <!-- <view class="search-form round" catchtap="onSearchTap">
			<i class="iconfont icon-sousuo"></i>
			<text>输入商品关键词或货号</text>/sfsns/pages/mine/index
		</view> -->
    <navigator hover-class="none" url="/sfsns/pages/mine/index" class="head-left" wx:if="{{userinfo.u_whether_enable_posting==1 || newsPower.cpAddNews==1}}">
      <image lazy-load src="https://snsimg.sanfu.com/sanfu1652775860400.png"></image>
    </navigator>
    <view class="head-middle justify-center align-center text-center">
      <view wx:for="{{newsType}}" wx:key="newsTypelist" wx:for-index="newstIndex" class="middle{{newstIndex>0 ? 1:0}} {{active2==newstIndex ?'act' : ''}}" data-index="{{newstIndex}}" bindtap="goactive2">
        <view class="middle1-top">{{item.name}}</view>
        <view class="middle1-fot" wx:if="{{active2==newstIndex}}"></view>
      </view>

      <!-- <view class="middle1 {{active==0?'act':''}}" data-index="0" bindtap="goclick">
        <view class="middle1-top">关注</view>
        <view class="middle1-fot" wx:if="{{active==0}}"></view>
      </view> -->
      <!-- <view class="middle1 {{active==1?'act':''}}" data-index="1" bindtap="goclick">
        <view class="middle1-top">发现</view>
        <view class="middle1-fot" wx:if="{{active==1}}"></view>
      </view> -->
      <!-- <view class="middle1 {{active==2?'act':''}}" data-index="2" bindtap="goclick">
				<view class="middle1-top">附近</view>
				<view class="middle1-fot" wx:if="{{active==2}}"></view>
			</view> -->
    </view>
    <view class="head-right" catchtap="onSearchTap">
      <i class="iconfont icon-sousuo"></i>
    </view>
  </view>
</block>
<view style="height: calc(100vh - 106rpx - {{CustomBar}}px);position: relative;">
  <!--全屏模式" -->
  <swiper style="height:100%;" autoplay="{{false}}" easing-function="linear" current="{{changeIndex}}" bindanimationfinish="swiperanimationfinish" vertical="{{true}}" class="v-image-video" wx:if="{{feed==1}}" duration="200">
    <block wx:for="{{datalist}}" wx:for-index="index1" wx:key="newsDataList" wx:for-item="nitem">
      <swiper-item>
        <view class="cu-list" style="height: calc(100vh - 106rpx - {{CustomBar}}px); ">
          <!-- <view class="userInfo">{{nitem.u_namebase64}}</view>  direction='{{0}}'-->
          <view class="cu-list-video" wx:if="{{nitem.cn_videourl!=''}}" style="height: calc(100vh - 106rpx - {{CustomBar}}px);">
            <image lazy-load mode="aspectFill" wx:if="{{playStatus==0||playStatus==4}}" src="{{filters.jpg2jpeg(nitem.cn_piclist[0],1000)}}" bindtap="openVideo" style="height: 100%;position: relative;">
              <view class="v-sign">
                <view id="playBtn" class="v-item1">
                  <view class="v-item2"></view>
                </view>
              </view>
            </image>
            <!-- <video  poster="{{filters.jpg2jpeg(photoList[0].bigImg)}}" bindtap="openVideo" id="myVideo" class="video" src="{{disUrl}}" muted="{{!videoSound}}" controls="{{playStatus==2}}" show-center-play-btn="{{false}}" title="{{filters.replaceg(goodsname,'三福', '')}}"  show-fullscreen-btn vslide-gesture-in-fullscreen="{{false}}" bindfullscreenchange="playfullscreen">
              <view catchtap="playMute" class="video-sound uni-icon uni-icon-sound {{videoSound?'':'dis'}}"></view>
            </video> -->
            <video wx:if="{{playStatus!=0&&index1==dataIndex}}" poster="{{filters.jpg2jpeg(nitem.cn_piclist[0],1000)}}" src="{{nitem.cn_videourl}}" id="video{{active2}}-{{index}}" style="height: calc(100vh - 106rpx - {{CustomBar}}px);" autoplay="{{true}}" custom-cache="{{false}}" controls="{{true}}" loop="{{false}}" show-fullscreen-btn="{{false}}" show-center-play-btn="{{true}}" play-btn-position="center" enable-progress-gesture="{{true}}" enable-play-gesture="{{true}}" object-fit="{{nitem.cnVideoSize<1 ? 'cover':'contain'}}" bindloadedmetadata="videoLoaded" bindended="playEnd"></video>
          </view>
          <swiper current="{{playIndex}}" indicator-dots="{{true}}" autoplay="{{false}}" circular="{{true}}" style="height: calc(100vh - 106rpx - {{CustomBar}}px);" indicator-active-color="#ffffff" wx:else>
            <swiper-item wx:for="{{nitem.cn_goodsinfo}}" wx:key="newslist" wx:if="{{item!=''}}" wx:for-item="picitem">
              <movable-area class=" movetool bg-image msize{{nitem.cn_picproportion}}" style="width:100%; height: 100%;position: relative;" catchtap>
                <image lazy-load src="{{filters.jpg2jpeg(picitem.pic,1000)}}" mode="aspectFill" style="position: absolute;width: 100%;height: 100%;left:0;top:0;"></image>
                <!--bindlongtap="downLoadimg" data-url="{{picitem.pic}}"-->
                <movable-view direction="all" wx:for="{{picitem.tags}}" x="{{(tagitem.x * psize > 185 ?  tagitem.x * psize - 142.5 * psize : tagitem.x * psize)}}" y="{{tagitem.y * psize}}" wx:key="tagsKey" wx:for-item="tagitem" disabled="true" data-id="3" catchtap="goclick2" class="sanfuicon{{(tagitem.x * psize > 185 ? 'r':'')}} line-1">{{tagitem.goods_name}}</movable-view>
                <!-- <span class="item-num">{{picindex+1}}/{{item.cn_goodsinfo.length}}</span> -->
              </movable-area>
            </swiper-item>
          </swiper>
          <!-- 喜欢、转发、购买 -->
          <view class="menu">
            <view class="menu1" data-id="1" bindtap="goclick2" data-liked="{{nitem.sqlliked}}" data-type="3">
              <view class="menu1-img">
                <image lazy-load src="https://snsimg.sanfu.com/sanfu1652794218591.png" wx:if="{{nitem.sqlliked}}"></image>
                <image lazy-load src="https://snsimg.sanfu.com/sanfu1652794179796.png" wx:else></image>
              </view>
              <view class="menu1-text">{{nitem.cn_like>0 ? nitem.cn_like :'喜欢'}}</view>
            </view>
            <view class="menu1" data-id="2" bindtap="goclick2">
              <view class="menu1-img">
                <image lazy-load src="https://snsimg.sanfu.com/sanfu1653031877882.png"></image>
              </view>
              <view class="menu1-text">转发</view>
            </view>
            <view class="menu1" data-id="3" bindtap="goclick2" wx:if="{{nitem.cn_goods>0}}">
              <view class="menu1-img">
                <image lazy-load src="https://snsimg.sanfu.com/sanfu1653031715489.png"></image>
              </view>
              <view class="menu1-text">购买</view>
            </view>
            <view class="main2" wx:if="{{(active==1 && userinfo.u_whether_enable_posting==1) || newsPower.cpAddNews==1}}" bindtap="showModal2" data-target="bottomModal">
              <image lazy-load src="https://snsimg.sanfu.com/sanfu1653031929234.png"></image>
            </view>
          </view>

          <!-- 下面数据文字 -->

          <view class="fot">
            <view class="fot1">
              <view class="cu-avatar round lg cu-img" style="background-image:url({{nitem.u_faceurl}});">
                <image lazy-load wx:if="{{nitem.u_daren>0}}" class="vdaren2" src="https://snsimg.sanfu.com/daren{{nitem.u_daren}}.png"></image>
              </view>
              <view class="content">
                <view class="text-black fs-40 con-text">{{nitem.u_namebase64}}</view>
                <!-- <view class='talk-image'>
									<image lazy-load src='https://snsimg.sanfu.com/sanfu1653031617534.png' class='talk-close' bindtap="hideModal3"></image>
							</view> -->
                <!-- <button class="theme-btn-small fr" bindtap='addfans' data-index="{{index}}" wx:if="{{newsinfo.sqlfans==0}}">关注</button> -->
              </view>
            </view>
            <view class="fot-top" wx:if="{{nitem.shopInfo!=null}}" bindtap="openShop">
              <view class="f-top1">前往[{{nitem.shopInfo.shoName}}]</view>
              <view class="f-top2" wx:if="{{nitem.shopInfo.distance>0}}">
                <view class="f-top2-img">
                  <image lazy-load src="https://snsimg.sanfu.com/sanfu1653036746151.png"></image>
                </view>
                <view class="f-top2-text">{{nitem.shopInfo.distance}}km</view>
              </view>
            </view>
            <view class="fot2" style="padding-right: 70rpx;">
              <view class="fot2-1">{{nitem.cn_title}}</view>
              <block wx:if="{{nitem.cn_newsinfo.length>44}}">
                <view class="fot2-2 mt-10 line-2 {{nitem.cn_ctid>0 ? '':' text-dent'}}" data-target="shoppingCart" bindtap="showModal">
                  <navigator hover-class="none" class="c-topic-css" url="/sfsns/pages/topic/topicdetails/topicdetails?id={{nitem.cn_ctid}}" wx:if="{{nitem.cn_ctid>0}}">#{{nitem.ct_title}}#</navigator>{{filters.toString(nitem.cn_newsinfo)}}
                </view>
                <view class="fot2-3" data-target="shoppingCart" bindtap="showModal">展开</view>
              </block>
              <block wx:else>
                <view class="fot2-2 mt-10 line-2 {{nitem.cn_ctid>0 ? '':' text-dent'}}">
                  <navigator hover-class="none" class="c-topic-css" url="/sfsns/pages/topic/topicdetails/topicdetails?id={{nitem.cn_ctid}}" wx:if="{{nitem.cn_ctid>0}}">#{{nitem.ct_title}}#</navigator>{{filters.toString(nitem.cn_newsinfo)}}
                </view>
              </block>
            </view>
          </view>
        </view>
      </swiper-item>
    </block>
  </swiper>
  <!--列表模式-->
  <view class="card-pbl  cf" wx:if="{{feed==0}}" style="height: 100%;">
    <scroll-view style="height: 100%;" scroll-top="{{scrolltop}}" scroll-y="true" bindscrolltoupper="bindTop" bindscrolltolower="bindBottom" bindtouchend="buttonEnd" bindtouchstart="buttonStart">
      <!-- <view class="cu-bar2" wx:if="{{active==0}}">
			<navigator hover-class="none" url="/sfsns/pages/follow/recommend/recommend" class="main1 cu-item arrow">推荐关注 </navigator>
		</view> -->
      <view class="list-box" style="margin-top: 0rpx;" wx:if="{{active==1}}">
        <!-- <view class="main-box2">
        <view wx:for="{{newsType}}" wx:key="newsTypelist" wx:for-index="newstIndex" class="main1-f2 {{active2==newstIndex ?'act3' : ''}}" data-index="{{newstIndex}}" bindtap="goactive2">
          {{item.name}}
        </view>
      </view> -->
        <view class="main1-f-img2">
          <image lazy-load src="https://snsimg.sanfu.com/sanfu1653035671880.png" data-feed="1" bindtap="gofeed"></image>
        </view>
      </view>
      <view class='w50'>
        <block wx:for="{{datalist}}" wx:key="homelist" wx:if="{{index % 2 == 0}}">
          <view class="card-pbl-son type1">
            <view data-index="{{index}}" bindtap="viewNews" class="card-pbl-header  size{{item.cn_picproportion}} radius cf" style="background-image: url('{{item.cn_piclist[0]}}/img70{{item.cn_picproportion}}')">
              <image lazy-load wx:if="{{item.cn_videourl!='' && item.cn_videourl!=null}}" class="playicon" src="https://snsimg.sanfu.com/icon/play.png"></image>
            </view>
            <view class="card-pbl-con pl_20 pr_20 cf">
              <view class="card-pbl-text" data-index="{{index}}" bindtap="viewNews">{{item.cn_title}}</view>
              <view class="card-pbl-footer">
                <navigator hover-class='none' url="{{item.cn_uid==userinfo.id ? 'sfsns/pages/mine/index' : ''}}" class="card-pbl-user fl face_info ">
                  <image lazy-load src="{{item.u_faceurl}}"></image>
                  <image lazy-load wx:if="{{item.u_daren>0}}" class="vdaren" src="https://snsimg.sanfu.com/daren{{item.u_daren}}.png"></image>
                  <view class="lineone">{{item.u_namebase64}}</view>
                </navigator>
                <view wx:if="{{item.sqlliked>0}}" catchtap='addfavorite' data-type="0" data-index="{{index}}" class="card-pbl-flowers fr">
                  <i class="iconfont icon-xihuan"></i>
                  <text> {{filters.toInt(item.cn_like)}}</text>
                </view>
                <view wx:else class="card-pbl-user fr" catchtap='addfavorite' data-type="0" data-index="{{index}}">
                  <i class="iconfont icon-xihuank" style="font-size:28rpx;"></i>
                  <text> {{filters.toInt(item.cn_like)}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
      <view class='w50'>
        <block wx:for="{{datalist}}" wx:key="homelist1" wx:if="{{index % 2 == 1}}">
          <view class="card-pbl-son card-pbl-sonr type1">
            <view data-index="{{index}}" bindtap="viewNews" class="card-pbl-header size{{item.cn_picproportion}} radius cf" style="background-image: url('{{item.cn_piclist[0]}}/img70{{item.cn_picproportion}}')">
              <image lazy-load wx:if="{{item.cn_videourl!='' && item.cn_videourl!=null}}" class="playicon" src="https://snsimg.sanfu.com/icon/play.png"></image>
            </view>
            <view class="card-pbl-con pl_20 pr_20 cf">
              <view class="card-pbl-text" data-index="{{index}}" bindtap="viewNews">{{item.cn_title}}</view>

              <view class="card-pbl-footer">
                <navigator hover-class='none' url="{{item.cn_uid==userinfo.id ? 'sfsns/pages/mine/index' : ''}}" class="card-pbl-user fl face_info lineone">
                  <image lazy-load src="{{item.u_faceurl}}"></image>
                  <image lazy-load wx:if="{{item.u_daren>0}}" class="vdaren" src="https://snsimg.sanfu.com/daren{{item.u_daren}}.png"></image>
                  <view class="lineone">{{item.u_namebase64}}</view>
                </navigator>
                <!--右点赞信息-->

                <view wx:if="{{item.sqlliked>0}}" catchtap='addfavorite' data-type="0" data-index="{{index}}" class="card-pbl-flowers fr">
                  <i class="iconfont icon-xihuan"></i>
                  <text> {{filters.toInt(item.cn_like)}}</text>
                </view>
                <view wx:else class="card-pbl-user fr" catchtap='addfavorite' data-type="1" data-index="{{index}}">
                  <i class="iconfont icon-xihuank" style="font-size:28rpx;"></i>
                  <text> {{filters.toInt(item.cn_like)}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>
  <view wx:if="{{feed==1}}">
    <view class="cu-bar2" wx:if="{{active==0}}">
      <navigator hover-class="none" url="/sfsns/pages/follow/recommend/recommend" class="main1 cu-item arrow">推荐关注 </navigator>
    </view>
    <block wx:if="{{active==1}}">
      <view class="main-box">
        <!-- <view wx:for="{{newsType}}" wx:key="newsTypelist" wx:for-index="newstIndex" class="main1-f {{active2==newstIndex ?'act3' : ''}}" data-index="{{newstIndex}}" bindtap="goactive2">
          {{item.name}}
        </view> -->
      </view>
      <view class="main1-f-img">
        <image lazy-load src="https://snsimg.sanfu.com/sanfu1653031572289.png" data-feed="0" bindtap="gofeed" style="z-index: 100;"></image>
      </view>
    </block>
  </view>
</view>
<!--内容展开-->
<view class="cu-modal bottom-modal shoppingCart  {{modalName=='shoppingCart'?'show':''}}" bindtap="hideModal">
  <view class="cu-dialog" catchtap>
    <view class="cuIcon-close c-viceFontcolor fs-40" bindtap="hideModal"></view>
    <view>
      <view class="fot1" bindtap="hideModal">
        <view class="cu-avatar round lg cu-img" style="background-image:url({{datalist[dataIndex].u_faceurl}});">
          <image lazy-load wx:if="{{datalist[dataIndex].u_daren>0}}" class="vdaren2" src="https://snsimg.sanfu.com/daren{{datalist[dataIndex].u_daren}}.png"></image>
        </view>
        <view class="content">
          <view class="text-black fs-40 con-text2 ml-20">{{datalist[dataIndex].u_namebase64}}</view>
        </view>
        <view class='talk-image'>
          <image lazy-load src='https://snsimg.sanfu.com/sanfu1653031617534.png' class='talk-close2'></image>
        </view>
      </view>
      <view class="fot2 fot3">
        <view class="fot2-1" style="text-align: left;color: #353535;font-size:32rpx; font-weight: bold;">{{datalist[dataIndex].cn_title}}</view>
        <view class="fot2-2 pt-10 pb-30 mt-10" style="text-align: left;color: #353535;font-size:28rpx;"><text>{{datalist[dataIndex].cn_newsinfo}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
<!-- 转发 -->
<share id="sanfu-share" styles="padding-bottom:0;" />
<!-- 购买 -->
<view class="cu-modal bottom-modal shoppingCart  {{modalName3=='goumai'?'show':''}}">
  <view style="width: 100%;height: 100%;position: fixed;top: 0;right: 0;left: 0;z-index: 1112;" bindtap="hideModal3"></view>
  <view class="cu-dialog2" wx:if="{{shop}}" style="z-index: 1115;">
    <view class='talk-header' bindtap="hideModal3">
      <view class='talk-count'>加购商品</view>
      <image lazy-load src='https://snsimg.sanfu.com/sanfu1653031617534.png' class='talk-close'></image>
    </view>
    <scroll-view class='talk-body' scroll-y="true">
      <block wx:for="{{datalist[dataIndex].goodsInfo}}" wx:key="goodinfo">
        <view class="cu-shop">
          <navigator hover-class="none" url="/pages/goodsDisplay/goodsDisplay?sns=1&goods_sn={{item.goodsSn}}" class="shop-left">
            <image lazy-load style="border-radius: 6rpx;" src="{{filters.jpg2jpeg(item.lImg)}}"></image>
          </navigator>
          <view class="shop-right">
            <navigator hover-class="none" url="/pages/goodsDisplay/goodsDisplay?sns=1&goods_sn={{item.goodsSn}}" class="shop-right1">{{item.goodsName}}</navigator>
            <!-- <view class="shop-right2">{{item.goodsName}}</view> -->
            <scroll-view scroll-x="true" class="shop-right3" wx:if="{{item.bImgList.length>0}}">
              <view class="shop-right3-1" wx:for="{{item.bImgList}}" wx:key="sonGoods" wx:for-item="simg">
                <image lazy-load style="border-radius:2rpx;" src="{{filters.jpg2jpeg(simg)}}" bindtap="pictureView" data-src="{{simg}}" data-list="{{item.bImgList}}"></image>
              </view>
            </scroll-view>
            <view class="shop-right4">
              <view class="shop-right4-1">￥{{item.qiangPrice||item.groupBuyPrice||item.memberPrice||item.salePrice}}</view>
              <view class="shop-right4-2">
                <view class="right4-image">
                  <!-- 未收藏 -->
                  <image lazy-load src="https://snsimg.sanfu.com/sanfu1653036837400.png" data-index="{{index}}" data-id="{{item.id}}" bindtap="addFavoriveProduct" wx:if="{{item.isCollect==0}}"></image>
                  <!-- 已收藏 -->
                  <image lazy-load src="https://snsimg.sanfu.com/sanfu1653036623940.png" data-index="{{index}}" data-id="{{item.id}}" bindtap="cancleFavoriveProduct" wx:else></image>
                </view>
                <view class="right4-image" style="margin-left: 14rpx;" data-index="{{index}}" catchtap="gocart">
                  <image lazy-load src="https://snsimg.sanfu.com/sanfu1653036284795.png"></image>
                </view>
              </view>
            </view>
            <view class="shop-right5" wx:if="{{item.promoList.length>0}}">
              <view catchtap="onTopic" class="flex align-center theme-fs-24 theme-c-136" catchtap="pack">
                <text class="right5-text">{{item.promoList[0].promoName}}</text>
                <i class="iconfont icon-xia theme-fs-22 theme-c-136 icon-t" wx:if="{{item.promoList.length>1}}"></i>
              </view>
              <view class="right5-fot" wx:if="{{packShow && item.promoList.length>1}}">
                <view class="mt-10" wx:for="{{item.promoList}}" wx:key="goodsLabel" wx:for-item="labelitem">{{labelitem.promoName}}</view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </scroll-view>
  </view>
</view>
<!-- 评论 -->
<view class="cu-modal bottom-modal shoppingCart an {{modalName4=='pinglun'?'show':''}}">
  <view style="width: 100%;height: 100%;position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 1112;" bindtap="hideModal4"></view>
  <view class="cu-dialog2" style="z-index: 1115;">
    <view class='talk-header'>
      <view class='talk-count'>共0 条评论</view>
      <image lazy-load src='https://snsimg.sanfu.com/sanfu1653031617534.png' class='talk-close' bindtap="hideModal4"></image>
      <!-- <view class="cuIcon-close2 c-viceFontcolor fs-40"bindtap="hideModal4"></view> -->
    </view>
    <!-- 评论体 -->
    <scroll-view class='talk-body  mb-40' scroll-y="true" bindscrolltolower="onScrollLoad">
      <block>
        <view class='talk-item mb-30' bindtap='replyFocus' wx:for="{3}" wx:key="comments">
          <view class='talk-item-left'>
            <image lazy-load class='talk-item-face' src="https://snsimg.sanfu.com/sanfu1653031715489.png"></image>
          </view>
          <view class='talk-item-right'>
            <view class='talk-item-nickname'>宇哥</view>
            <view>
              <text class='talk-item-content'>帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆</text>
              <text class='talk-item-time' style="margin-left: 30rpx;color: #888888;">3-16</text>
            </view>
            <!-- 回复 -->
            <view class="talk-h">
              <view class="h-left">
                <image lazy-load src="https://snsimg.sanfu.com/sanfu1653031762430.png"></image>
              </view>
              <view class="h-right">
                <view class='talk-item-nickname'>宇哥</view>
                <view>
                  <view class='talk-item-content' catchtap="addrecommend">帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆帆</view>
                  <view class='talk-item-time' style="color: #888888;">3-16</view>
                </view>
              </view>
              <!-- 2层右边小虹心 -->
              <view class="talk-right2">
                <view class="right2-image">
                  <image lazy-load src="https://snsimg.sanfu.com/sanfu1653031809140.png"></image>
                  <!-- <image lazy-load src="https://snsimg.sanfu.com/sanfu1653031836796.png"></image> -->
                </view>
                <view class="right2-text">888</view>
              </view>
            </view>

            <view class="talk-item-text" catchtap="showSecondComments">
              <text>—— 展开10条回复</text>
              <!-- <image lazy-load src="https://snsimg.sanfu.com/sanfu1653036475365.png" class="video-logo-more"></image> -->
              <i class="iconfont icon-xia theme-fs-22 theme-c-136 icon-t2"></i>
            </view>
          </view>
          <view class="talk-right2">
            <view class="right2-image" catchtap="golove">
              <image lazy-load src="https://snsimg.sanfu.com/sanfu1653031809140.png" wx:if="{{love}}"></image>
              <image lazy-load src="https://snsimg.sanfu.com/sanfu1653031836796.png" wx:else></image>
            </view>
            <view class="right2-text">888</view>
          </view>
        </view>

      </block>
    </scroll-view>
    <form bindsubmit="addnewscomments" report-submit='true'>
      <view class="input-fayan flex align-center">
        <!-- {{recommentnc!='' ? '回复'+recommentnc+'：' :''}}{{commentinfo}} -->
        <input type="text" style='padding:0rpx 20rpx;' cursor-spacing='10' adjust-position='true' placeholder="说点什么吧......" value="" bindinput='intputext' bindconfirm='addnewscomment' name="newsinfo" placeholder-class="input-ys" />
        <button form-type='submit' catchtap='addnewscomment'>发送</button>
      </view>
    </form>
  </view>
</view>



<view class="authorization_view" wx:if="{{authorization}}" bindtap="getUserInfo"></view>

<!-- sku修改弹窗https://snsimg.sanfu.com/sanfu1657853490396.png
 -->

<checkSize></checkSize>

<view class="newUser" wx:if="{{newNuser=='0'}}">
  <view class="newUser_header" style="top:calc(20rpx + {{CustomBar}}px);">
    <image lazy-load src="https://snsimg.sanfu.com/sanfu1657851582874.png"></image>
  </view>
  <view class="newUser_top">
    <image lazy-load src="https://snsimg.sanfu.com/sanfu1657850827765.png"></image>
  </view>
  <view class="newUser_left">
    <image lazy-load src="https://snsimg.sanfu.com/sanfu1657850874395.png"></image>
  </view>
  <view class="newUser_right">
    <image lazy-load src="https://snsimg.sanfu.com/sanfu1657850887624.png"></image>
  </view>
  <view class="newUser_bottom">
    <image lazy-load src="https://snsimg.sanfu.com/sanfu1658198296722.png"></image>
  </view>
  <image lazy-load src="https://snsimg.sanfu.com/sanfu1657851970376.png" bindtap="hiddenNewUser" class="newUser_foot"></image>
</view>
