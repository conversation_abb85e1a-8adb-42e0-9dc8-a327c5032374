const app = getApp()
Component({
  options: {
    virtualHost: true,
    addGlobalClass: true
  },
  properties: {
    type: {
      type: Number,
      value: 1,
    },
    value: {
      type: Number,
      observer(value) {
        if (value !== this.data.innerValue) {
          this.setData({
            innerValue: value
          });
        }
      },
    },
    readonly: <PERSON><PERSON><PERSON>,
    disabled: <PERSON><PERSON><PERSON>,
    allowHalf: <PERSON><PERSON>an,
    size: null,
    icon: {
      type: String,
      value: 'star',
    },
    voidIcon: {
      type: String,
      value: 'star-o',
    },
    color: {
      type: String,
      value: '#ffd21e',
    },
    voidColor: {
      type: String,
      value: '#c7c7c7',
    },
    disabledColor: {
      type: String,
      value: '#bdbdbd',
    },
    count: {
      type: Number,
      value: 5,
      observer(value) {
        this.setData({
          innerCountArray: Array.from({
            length: value
          })
        });
      },
    },
    gutter: null,
    touchable: {
      type: Boolean,
      value: true,
    },
    iconClass: {
      type: String,
      value: 'yd-icon yd-icon-star',
    },
    voidIconClass: {
      type: String,
      value: 'yd-icon yd-icon-star-outline',
    },
  },
  data: {
    innerValue: 0,
    innerCountArray: Array.from({
      length: 5
    }),
  },
  methods: {
    onSelect(event) {
      const {
        data
      } = this;
      const {
        score
      } = event.currentTarget.dataset;
      if (!data.disabled && !data.readonly) {
        this.setData({
          innerValue: score + 1
        });
        this.setData({
          value: score + 1
        });
        wx.nextTick(() => {
          this.triggerEvent('input', score + 1)
          this.triggerEvent('change', score + 1)
        });
      }
    },
    onTouchMove(event) {
      const {
        touchable
      } = this.data;
      if (!touchable)
        return;
      const {
        clientX
      } = event.touches[0];
      app.util.getRect('.icon', true, this).then((list) => {
        const target = list
          .sort((item) => item.right - item.left)
          .find((item) => clientX >= item.left && clientX <= item.right);
        if (target != null) {
          this.onSelect(Object.assign(Object.assign({}, event), {
            currentTarget: target
          }));
        }
      });
    },
  },
});
