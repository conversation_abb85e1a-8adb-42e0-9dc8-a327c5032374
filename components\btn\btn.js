const app = getApp()
import Auth from '../../auth/index';
Component({
  options: {
    addGlobalClass: true
  },
  properties: {},
  data: {
    zhankai: 0,
    close: 0,
    propChecked: false
  },
  attached() {},
  detached() {},
  methods: {
    openProp(e) {
      this.setData({
        propChecked: !this.data.propChecked
      })
    },
    closeProp() {
      this.setData({
        propChecked: false
      })
    },
    /* 快速展开、收起 */
    open() {
      if (this.zhankai) {
        this.zhankai = 0
      } else {
        this.zhankai = 1
      }
    },
    close() {
      this.setData({
        close: 1
      })
    },
    /* 关闭快速导航按钮 */
    close1() {
      if (this.close) {
        this.close = 0
        this.zhankai = 0
      } else {
        this.close = 1
        this.zhankai = 1
      }
    },
    /* 主页跳转 */
    goindex() {
      wx.switchTab({
        url: '/pages/index/index'
      })
    },
    /* 客服按钮跳转 */
    gokefu() {
      app.util.to_kefu()
    },
    myOrder() {
      let curID = wx.getStorageSync('cardid') || '';
      if (curID === '' || curID === undefined || curID === 'null') {
        Auth()
      } else {
        app.toH5(`user/myOrder`)
      }
    },
    /* 分类跳转 */
    gosort() {
      wx.switchTab({
        url: '/pages/sort/sort'
      })
    },
    /* 购物车跳转 */
    cart() {
      let pages = getCurrentPages()
      let route = pages[pages.length - 1].route
      if (route == 'pages/cart/cart' || route == 'pages/cart/cart1') return

      let curID = wx.getStorageSync('cardid') || '';
      if (curID === '' || curID === undefined || curID === 'null') {
        Auth()
      } else {
        app.toH5(`cart`)
      }
    },
    goScan() {
      app.toH5(`pagescan`)
    }

  }
});
