<view class="collapse-container" style="{{styles}};{{!border?'border:none':''}}">
  <view wx:if="{{showTitle}}" class="main" bindtap="change" hover-class="{{hover?'hover':''}}" hover-start-time="20" hover-stay-time="100" style="{{titleStyle}}">
    <view class="title">
      <slot wx:if="{{slotTitle}}" name="title"></slot>
      <block wx:if="{{!slotTitle}}">{{title}}</block>
    </view>
    <slot name="value"></slot>
    <text class="uni-icon uni-icon-arrowdown" style="{{height?'transform: rotate(-180deg);':''}};transition-duration:{{duration}}ms;{{iconStyle}}"></text>
  </view>
  <!-- 内容高度小于最小配置高度则用最低高度 -->
  <view class="other" style="height: {{height==0&&showItemHeight==0&&minHeight?loadHeight?loadHeight:minHeight+'px':height}};min-height: {{showMinHeight}};transition-duration:{{duration}}ms;">
    <view class="collapse-item {{showItemHeight?show1?showClass:hideClass:''}} " style="{{height?'':'border:none;'}}{{!border?'border:none;':''}}{{itemStyle}}" bindtap="{{showOpen&&((tapUnfold&&tapfold&&height!=0)||(tapUnfold&&height==0))?'change':''}}">
      <slot></slot>
    </view>
  </view>
  <view wx:if="{{showOpen}}" style="font-size: 24rpx;color: #666;padding: 16rpx 0 6rpx;margin-top: -10rpx;line-height: 1;overflow: hidden;display: flex;align-items: center;justify-content:center;transition: all 0.3s;transform: translateY(100%);{{showOpen?'transform: translateY(0)':''}};{{moreStyle}}" catchtap="change">{{height!=0?'收起':'展开'}}
    <text class="{{icon||'f7 icondrop-down delta'}} " style="{{height!=0?'transform: rotate(-180deg);':''}};transition: all .5s;{{iconStyle2}}"></text>
  </view>
  <view wx:else style="{{moreStyle}}"></view>
  <!-- test height:{{height}}  showItemHeight:{{showItemHeight}} -->
</view>
