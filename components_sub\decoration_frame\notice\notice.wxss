.notice {
  position: relative;
  box-sizing: border-box;
  font-size: 0;
  color: #333;
  display: flex;
  align-items: center;
  padding: 2rpx 0;
}

.notice-title {
  font-size: 24rpx;
  border-right: 1px solid #333;
  padding-right: 16rpx;
  margin-right: 16rpx;
  display: inline-block;
  vertical-align: middle;
  padding-left: 16rpx;
  line-height: 24rpx;
}

.noticeIcon {
  width: 28rpx;
  height: 28rpx;
  vertical-align: middle;
  line-height: 28rpx;
}

.notice-text {
  font-size: 24rpx;
  break-after: avoid;
  margin-left: 10rpx;
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  height: 32rpx;
  overflow: hidden;
  position: relative;
  flex: 1;
}

.notice-text-info {
  position: absolute;

}

.whiteSpace {
  white-space: unset;
  transition: transform 0.3s;
}

@keyframes wordsLoop {
  0% {
    transform: translateX(0px);
  }

  20% {
    transform: translateX(0px);
  }

  85% {
    transform: translateX(calc(-100% + 500rpx));
  }

  100% {
    transform: translateX(calc(-100% + 500rpx));
  }
}

.moveX {
  animation: 9s wordsLoop linear infinite normal;
}
