<view wx:if="{{showPopup&&!versionNew}}" class="uni-popup" style="z-index:{{zIndex}}" catchtouchmove="clear">
  <uni-transition modeClass="{{['fade']}}" styles="{{maskClass}};{{maskStyles}}" duration="{{duration}}" show="{{showTrans}}" bind:click="onTap" />
  <uni-transition modeClass="{{ani}}" styles="{{transClass}}{{styles}}" duration="{{duration}}" show="{{showTrans}}" zIndex="{{zIndex}}" bind:click="onTap">
    <view class="uni-popup__wrapper-box" style="{{type=='center'?'height:auto;':''}}" catchtap="clear">
      <slot />
    </view>
  </uni-transition>
</view>
<block wx:elif="{{showPopup}}">
  <page-container show="{{newPop}}" round overlay duration="{{duration}}" position="{{type}}" close-on-slide-down="{{false}}" bindbeforeenter="clear" bindenter="clear" bindafterenter="clear" bindbeforeleave="onTap" custom-style="{{type=='bottom'?'padding-bottom:env(safe-area-inset-bottom);':''}}{{type=='center'?'background:transparent;width:fit-content;height:fit-content;left:50%;top:50%; transform: translateX(-50%) translateY(-50%);':''}}overflow: visible;{{styles}}" overlay-style="{{maskStyles}};" z-index="{{zIndex}}">
    <slot />
  </page-container>
</block>
<!-- 右侧弹出设置width时需同时设置margin-left   width:85%;margin-left:15%;  右侧无圆角 -->
