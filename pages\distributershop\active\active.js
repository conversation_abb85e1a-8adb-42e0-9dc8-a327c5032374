// pages/distributershop/active/active.js
import Share from '../../../components/common/share/index.js'
const util = require('../../../utils/util.js')
import common from '../common.js'
import shareCanvas from '../../../utils/shareCanvas.js'
const prefix = (uri) => {
  return 'ms-sanfu-wap-customer-distribution/distribution/' + uri;
}
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showLoading: false,
    totalCount: 0,
    page: 1,
    has_more: 4, //对应loadingtext  3：无数据   4，全局loading
    loadingText: ['点击加载更多', '正在加载...', '没有更多了', '暂无该推荐'],
    activitys: [],
    showqrcode: true,
    shareItem: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.getPages()
    this.workTrace(3)
  },
  onPullDownRefresh: function() {
    this.data.page = 1;
    this.setData({
      has_more: 4
    })
    this.getPages()
    wx.stopPullDownRefresh()
  },
  onReachBottom: function() {
    if (this.data.has_more != 0) return;
    this.setData({
      has_more: 1
    })
    this.getPages()
  },
  toDetail(e) {
    let item = e.currentTarget.dataset.item || {}
    console.log(item, e)
    app.toH5('user/disshop-active?id=' + item.actId)
  },

  /**
   * 分页接口
   */
  getPages() {
    let obj = this.data.reqData
    app.reqGet(prefix('act/page'), {
      sid: wx.getStorageSync('sid'),
      flag: 1,
      page: this.data.page,
      pageSize: 5
    }, res => {
      if (res.success) {
        let data = res.data
        if (this.data.page == 1) this.data.activitys = []
        for (let i in data.result) this.data.activitys.push(data.result[i])
        if (data.totalCount == this.data.activitys.length) {
          this.data.has_more = 2
        } else {
          this.data.has_more = 0
          this.data.page++
        }
        if (data.totalCount == 0 || data.totalCount == null) this.data.has_more = 3
        this.setData({
          has_more: this.data.has_more,
          activitys: this.data.activitys,
        })
      } else
        // console.log(el)
        this.setData({
          has_more: 2
        })
    })
  },

  closeQR() {
    this.setData({
      showqrcode: true
    })
  },
  showShare(e) {
    let that = this
    let shareItem = e.currentTarget.dataset.shareitem
    that.setData({
      shareItem: shareItem
    })
    Share().then(el => {
      console.log('微信好友')
      let startCount = shareItem['shareCnt'] || 0
      that.transfer(shareItem.actId, 3)
      // that.updateShareCount('share_act', shareItem['actId'], startCount)
    }).catch(el => {
      that.transfer(shareItem.actId, 3)
      let url = shareItem.subtitle.split('链接')[1]
      shareItem.subtitle = shareItem.subtitle.split('链接')[0]
      if (shareItem.linkUrl) url = shareItem.linkUrl
      console.log(url, shareItem.subtitle)
      this.sharecanvas({
        imgList: [shareItem.picture],
        title1: shareItem.title,
        title2: shareItem.subtitle,
        actId: shareItem.actId,
        url: url
      })
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function(options) {
    var that = this;
    let shareItem = that.data.shareItem
    if (!shareItem.title) shareItem = this.data.activitys[0]
    let endUrl = `https://${app.apidomain}/user/disshop-active?id=${shareItem.actId}`
    let cusurl = shareItem.subtitle && shareItem.subtitle.split('链接')[1] || ''
    cusurl = cusurl.replace(/\s+/g, '')
    if (cusurl) {
      endUrl = cusurl
    }
    if (shareItem.linkUrl) endUrl = shareItem.linkUrl
    if (endUrl.indexOf('?') == -1) {
      endUrl = endUrl + '?'
    }
    if (wx.getSystemInfoSync().environment == 'wxwork')
      endUrl += '&from=qymall'
    // 设置菜单中的转发按钮触发转发事件时的转发内容
    console.log('endUrl', endUrl)
    var shareObj = {
      title: shareItem.title, // 默认是小程序的名称(可以写slogan等)
      path: '/pages/pcweb/pcweb?shareUrl=' + encodeURIComponent(endUrl), // 默认是当前页面，必须是以‘/’开头的完整路径
      imageUrl: shareItem.picture, //that.data.shareItem.baseImg,     //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      complete: function() {
        // 转发结束之后的回调（转发成不成功都会执行）
      }
    };
    app.sr.track('page_share_app_message', {
      "from_type": options.from,
      "share_title": shareItem.title,
      "share_path": '/pages/pcweb/pcweb?shareUrl=' + encodeURIComponent(endUrl),
      "share_image_url": shareItem.picture
      // more...
    })
    // 来自页面内的按钮的转发
    if (options.from == 'button') {
      var eData = options.target.dataset;
      // 此处可以修改 shareObj 中的内容
      shareObj.path = '/pages/pcweb/pcweb?shareUrl=' + encodeURIComponent(endUrl)
    }
    // 返回shareObj
    return shareObj;
  },
  ...common,
  ...shareCanvas
})
