const app = getApp()
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    config: {
      type: null,
      value: ''
    },
    unitData: {
      type: null,
      value: ''
    },
    unitName: {
      type: null,
      value: ''
    },
    pid: {
      type: null,
      value: ''
    },
    isSub: {
      type: Boolean,
      value: false
    },
    index: {
      type: null,
      value: ''
    }
  },
  data: {

  },
  attached() {},
  methods: {
    async toH5(e) {
      const i = e.currentTarget.dataset.i
      const item = this.data.unitData.hotAreaList[i].areaData
      try {
        app.sf.track('mallpage_click', {
          track_cfg_id: this.data.pid,
          track_title: this.data.unitName || this.data.config.unitName || '热区',
          text: this.data.unitData.hotAreaList[i].trackMemo
        })
      } catch (e) {}
      if (item.jumpUrl) {
        app.toH5(item.jumpUrl)
      }
    }
  }
});
