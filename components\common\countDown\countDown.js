import {
  isSameSecond,
  parseFormat,
  parseTimeData
} from './utils';

function simpleTick(fn) {
  return setTimeout(fn, 30);
}
/**
   * **倒计时组件**
   * 
   * @property {boolean} useSlot 是否使用slot
   * @property {boolean} millisecond 毫秒
   * @property {Number} type 内置展示类型
   * @property {Number} time 时间秒数
   * @property {String} format 格式
   * @property {boolean} autoStart 自动开始
   * @event {Function} change 
   * @event {Function} finish 
   */
Component({
  /**
   * 组件的属性列表
   */
  options: {
    // addGlobalClass: true,
    multipleSlots: true,
    virtualHost: true
  },
  properties: {
    useSlot: Boolean,
    millisecond: {
      type: Boolean,
      value: false,
    },
    type: {
      type: Number,
      value: 0,
    },
    time: {
      type: Number,
      observer: 'reset',
    },
    format: {
      type: String,
      value: 'HH:mm:ss',
    },
    autoStart: {
      type: Boolean,
      value: true,
    },
    styles: {
      type: String,
      value: ''
    }
  },
  data: {
    timeData: parseTimeData(0),
    formattedTime: '0',
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  },
  detached() {
    clearTimeout(this.tid);
    this.tid = null;
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {
      this.start(1)
    },
    hide: function() {
      this.pause()
    },
    resize: function() {},
  },
  methods: {
    // 开始
    start(type) {
      if (this.counting) {
        return;
      }
      this.counting = true;

      if (type != 1) this.endTime = Date.now() + this.remain;
      this.tick();
    },
    // 暂停
    pause() {
      this.counting = false;
      clearTimeout(this.tid);
    },
    // 重置
    reset() {
      this.pause();
      this.remain = this.data.time;
      this.setRemain(this.remain);
      if (this.data.autoStart) {
        this.start();
      }
    },
    tick() {
      if (this.data.millisecond) {
        this.microTick();
      } else {
        this.macroTick();
      }
    },
    microTick() {
      this.tid = simpleTick(() => {
        this.setRemain(this.getRemain());
        if (this.remain !== 0) {
          this.microTick();
        }
      });
    },
    macroTick() {
      this.tid = simpleTick(() => {
        const remain = this.getRemain();
        if (!isSameSecond(remain, this.remain) || remain === 0) {
          this.setRemain(remain);
        }
        if (this.remain !== 0) {
          this.macroTick();
        }
      });
    },
    getRemain() {
      return Math.max(this.endTime - Date.now(), 0);
    },
    setRemain(remain) {
      this.remain = remain;
      const timeData = parseTimeData(remain);
      if (this.data.useSlot) {
        this.triggerEvent('change', timeData);
      }
      this.setData({
        formattedTime: parseFormat(this.data.format, timeData),
        ...timeData
      });
      if (remain < 1000) {
        this.pause();
        this.triggerEvent('finish');
      }
    },
  }
})
