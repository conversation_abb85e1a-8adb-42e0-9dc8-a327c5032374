// pages/distributershop/verifyImgList/verifyImgList.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    viewList: [],
    tab: 0,
    loadingText: ['点击加载更多', '正在加载...', '没有更多了', '暂无该推荐']
  },
  onShow() {
    this.init()
  },
  onReachBottom() {
    this.getList()
  },
  onPullDownRefresh() {
    wx.stopPullDownRefresh()
    this.init()
  },
  init() {
    this.setData({
      viewList: [{
          title: '待审核',
          page: 1,
          has_more: 0,
          status: 0,
          data: []
        },
        {
          title: '已审核',
          page: 1,
          has_more: 0,
          status: 1,
          data: []
        }
      ]
    })
    this.getList()
  },
  /* 根据statue的状态去进行列表的刷新 state：0-4 */
  changeTab(e) {
    let index = e.detail
    this.setData({
      tab: index
    })
    if (this.data.viewList[index].data.length == 0) {
      this.getList()
    } else
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      })
  },
  async getList() {
    const index = this.data.tab
    const item = this.data.viewList[index]
    // 判断是否可加载
    if (item.has_more != 0) return
    // 加载中
    this.setData({
      [`viewList[${index}].has_more`]: 1
    })
    const res = await app.reqGet('ms-sanfu-wap-customer-distribution/goods/listVerifyImg', {
      sid: wx.getStorageSync('sid'),
      status: item.status,
      page: item.page,
      pageSize: 8
    })
    if (res.success) {
      let list = res.data
      item.data = [...item.data, ...list]
      // 判断是否可加载  页数，数据数
      if (list.length < 8) {
        item.has_more = 2
      } else {
        item.has_more = 0
      }
      item.page++
      this.setData({
        [`viewList[${index}]`]: item
      })
    } else {
      app.util.reqFail(res)
    }
    this.setData({
      loading: false
    })
  },
  toDetail(e) {
    let id = e.currentTarget.dataset.id
    app.toH5('/pages/distributershop/verifyImgDetail/verifyImgDetail?gooid=' + id)
  },
})
