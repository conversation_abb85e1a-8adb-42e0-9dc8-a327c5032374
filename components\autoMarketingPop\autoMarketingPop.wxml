<!-- 背景遮罩 -->
<uni-transition modeClass="{{['fade']}}" styles="position:fixed;bottom:0;top:0;left:0;right:0;background-color:rgba(0, 0, 0, 0.3);z-index:9999;" duration="200" show="{{show&&popupViewType>0}}" />
<!-- 顶弹窗 -->
<uni-transition modeClass="{{['slide-top','fade']}}" styles="z-index:99999;position: fixed;top: 5%;left:50rpx;margin-top:44px;width: 650rpx;overflow: hidden;display: flex;flex-direction: column;align-items:center;" duration="400" show="{{show&&popupViewType==1}}" zIndex="{{zIndex}}">
  <image wx:if="{{popupPic}}" bindtap="tapbox" mode="widthFix" src="{{popupPic}}" style="width: 650rpx;border-radius:12rpx;display: flex;"></image>
  <view wx:else class="top-box" bindtap="tapbox">
    <view class="title">{{popupTitle||(couponList.length>0?'三福送您'+couponList.length+'张优惠券啦~':'点击查看最新优惠活动~')}}</view>
    <view class="top-box-btn">{{couponList.length>0?'立即领取':'查看活动'}}</view>
    <view class="auto-close" bindtap="close">
      <countDown wx:if="{{show&&popupViewType==1}}" time="{{16*1000}}" format="ss" bindfinish="close"></countDown>秒后自动关闭
    </view>
  </view>
  <view class="uni-icon uni-icon-close" style="margin-top: 20rpx;color: #fff;font-size: 60rpx;" bindtap="close">
  </view>
</uni-transition>
<!-- 中弹窗 -->
<uni-transition modeClass="{{['fade']}}" styles="z-index:99999;position: fixed;top: 0;left:0;width: 100%;height:100%;overflow: hidden;display: flex;flex-direction: column;align-items:center;justify-content:center;padding-bottom:25%;" duration="350" show="{{show&&popupViewType==2&&(popupPic||couponList.length>0)}}" zIndex="{{zIndex}}">
  <view>
    <image wx:if="{{popupPic}}" mode="widthFix" src="{{popupPic}}" style="width: 650rpx;border-radius:12rpx;display: flex;" bindtap="tapbox"></image>
    <view wx:elif="{{couponList.length>0}}" class="center-coupon">
      <image class="img" src="https://img.sanfu.com/sf_access/uploads/HtfBotY76E7ZR2owWWDIj87a70w494It.png">
        <view class="title" data-content="{{popupTitle||'恭喜您获得优惠券'}}">{{popupTitle||'恭喜您获得优惠券'}}</view>
      </image>
      <view class="content">
        <scroll-view scroll-y style="width: 100%;max-height: 410rpx;">
          <view wx:for="{{couponList}}" wx:key="index" class="coupon-one">
            <view class="money"><text class="money-text">{{item.money}}</text></view>
            <view class="line">
              <view></view>
            </view>
            <view class="right">
              <view class="title right-item">{{item.name}}</view>
              <view class="right-item" wx:if="{{item.leastMoney}}">满{{item.leastMoney}}元使用</view>
              <view class="right-item" wx:if="{{item.validBegin}}">{{item.validBegin}}-{{item.validEnd}}</view>
            </view>
          </view>
        </scroll-view>
        <view class="btn" bindtap="tapbox">一键领取</view>
      </view>
    </view>
    <view  style="margin-top: 30rpx;color: #f0f0f0;display: flex;align-items: center;justify-content: center;" bindtap="close">
      <view class="uni-icon uni-icon-closeempty" style="font-size: 60rpx;text-align: center;line-height: 70rpx;width: 70rpx;height: 70rpx;background: rgba(255,255,255,.15);border-radius: 50%;border: 4rpx solid #d1d1d1;"></view>
    </view>
  </view>
</uni-transition>
