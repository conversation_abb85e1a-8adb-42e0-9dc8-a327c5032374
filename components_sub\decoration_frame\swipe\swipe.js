const app = getApp()
import common from '../common.js'
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    config: {
      type: null,
      value: ''
    },
    unitData: {
      type: null,
      value: '',
      observer: function(newVal, oldVal) {
        // 控制隐藏
        this.checkShow()
      }
    },
    pid: {
      type: null,
      value: ''
    },
    // 门店后传入  优先预处理展示
    shopInfo: {
      type: null,
      value: '',
      observer: function(newVal, oldVal) {
        if (newVal.orgId && newVal.dsho_id && !this.load) {
          // console.log('shopInfoobserver22222222');
          let refresh = false
          this.data.waitList.forEach(item => {
            let e = this.deleteImg(item.data)
            // console.log(e, 'eeee');
            if (e === 0 && this.data.unitData.list) {
              this.data.unitData.list.splice(item.i, 0, item.data)
              refresh = true
            }
          })
          if (refresh) {
            this.load = true
            this.setData({
              unitData: this.data.unitData
            })
          }
        }
      }
    },
    current: {
      type: Number,
      value: '',
      observer: function(newVal, oldVal) {
        if (newVal != this.data.cindex) {
          this.setData({
            cindex: newVal
          })
        }
      }
    },
    index: {
      type: null,
      value: ''
    },
    menuButton: {
      type: null,
      value: ''
    },
    styles: {
      type: String,
      value: ''
    },
    unitName: {
      type: null,
      value: ''
    }
  },
  data: {
    show: false,
    waitList: [],
    cindex: 0,
    fadeTimer: null // 添加渐变轮播定时器
  },
  attached() {
    wx.nextTick(() => {
      if (this.data.cIndex === 0) {
        this.swiperChange({
          detail: {
            current: 0
          }
        })
      }
      // 初始化渐变轮播
      if (this.data.config.sType === 3) {
        this.initFadeSwiper()
      }
    })
  },
  detached() {
    // 清除定时器
    if (this.data.fadeTimer) {
      clearInterval(this.data.fadeTimer)
    }
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: async function() {
      // 页面显示时，如果是渐变轮播，重新初始化轮播
      if (this.data.config.sType === 3) {
        this.initFadeSwiper()
      }
    },
    hide: function() {
      // 页面隐藏时，清除所有轮播定时器
      if (this.data.fadeTimer) {
        clearInterval(this.data.fadeTimer)
        this.data.fadeTimer = null
      }
    },
    resize: function() {
      // 页面尺寸变化时，重新获取导航栏高度
    }
  },
  methods: {
    ...common,
    checkShow() {
      const newVal = this.data.unitData
      for (let i = 0; i < newVal.list.length; i++) {
        let e = this.deleteImg(newVal.list[i])
        if (e > 0) {
          if (e === 2 && this.data.waitList.findIndex(e => e.i === i) === -1) {
            this.data.waitList.push({
              i,
              data: newVal.list[i]
            })
          }
          newVal.list.splice(i, 1)
          i--
        } else if (newVal.list[i].trackMemo && newVal.list[i].link && newVal.list[i].link.match(/&deletePromo=1/)) {
          newVal.list.splice(i, 1)
          i--
        }
      }
      this.setData({
        unitData: newVal,
        show: true
      })
    },
    async toH5(e) {
      const i = e.currentTarget.dataset.i
      const item = this.data.unitData.list[i]
      try {
        app.sf.track('mallpage_click', {
          track_cfg_id: this.data.pid,
          track_title: this.data.unitName || this.data.config.unitName || '轮播',
          text: item.trackMemo
        })
      } catch (e) {}
      if (item.link) {
        app.toH5(item.link)
      }
    },
    swiperChange(e) {
      if (e.detail.current === this.data.cindex) return
      if (this.changing) return
      this.changing = true
      this.setData({
        cindex: e.detail.current
      })
      wx.nextTick(() => {
        this.changing = false
      })
      this.triggerEvent('change', this.data.cindex)
      try {
        if (this.data.index < 5 && (this.data.config.sType === 2 || this.data.config.sType === 4)) {
          const item = this.data.unitData.list[e.detail.current]
          app.$bus.emit('changeTcolor', item.tColor)
          const pages = getCurrentPages()
          const page = pages[pages.length - 1]
          page.swipeChange(item.tColor)
        }
      } catch (e) {
        //TODO handle the exception
      }
      // 重置渐变轮播定时器
      if (this.data.config.sType === 3) {
        if (this.data.fadeTimer) {
          clearInterval(this.data.fadeTimer)
        }
        this.initFadeSwiper()
      }
    },
    // 初始化渐变轮播
    initFadeSwiper() {
      if (this.data.config.sType === 3 && this.data.unitData.list && this.data.unitData.list.length > 1) {
        const interval = this.data.config.interval || 4500
        this.data.fadeTimer = setInterval(() => {
          let nextIndex = (this.data.cindex + 1) % this.data.unitData.list.length
          this.swiperChange({
            detail: {
              current: nextIndex
            }
          })
        }, interval)
      }
    }
  }
})
