
@import "/font/uni-icon.wxss";
.goods-container {
  box-sizing: border-box;
  overflow: hidden;
}

.goods-title {
  position: relative;
  padding: 0 20rpx 0 30rpx;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  height: 100rpx;
  display: flex;
  align-items: center;
  line-height: 1;
}

.goods-title .title-content {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.goods-title .seckill-time {
  margin-left: 16rpx;
  background: linear-gradient(to left, #ffeef0, #fdd7d5);
  border-radius: 30rpx;
  height: 40rpx;
  font-size: 28rpx;
  color: #e60012;
  font-weight: bold;
  width: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.goods-title .more {
  color: #999;
  font-size: 24rpx;
  margin-left: auto;
}

.goods-list {
  display: flex;
  box-sizing: border-box;
}

.goods-list.layout-1 {
  display: flex;
  flex-direction: column;
}

/* 横向滚动布局样式 */
.goods-list.layout-scroll {
  width: 100%;
  white-space: nowrap;
}

.goods-list.layout-scroll .scroll-content {
  display: inline-flex;
  flex-direction: row;
  white-space: nowrap;
}

.goods-list.layout-2 {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.goods-list.layout-3 {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
} 