Component({
  options: {
    virtualHost: true
  },
  properties: {
    item: {
      type: Object,
      value: {}
    },
    config: {
      type: Object,
      value: {}
    },
    styles: {
      type: String,
      value: ''
    }
  },

  methods: {
    onTap() {
      this.triggerEvent('tap', {
        item: this.data.item
      })
    },
    openSku(e) {
      const app = getApp()
      const item = this.data.item
      if (!item.lImg) {
        item.lImg = item.mImg || item.listImg
      }
      app.$bus.emit('setSkuGoods', {
        goods: item
      })
    }
  }
})
