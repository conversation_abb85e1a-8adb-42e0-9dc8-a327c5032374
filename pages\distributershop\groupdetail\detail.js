// pages/distributershop/groupdetail/detail.js
import Share from '../../../components/common/share/index.js'

import methods from '../common.js'
const app = getApp()
const prefix = (uri) => {
  return 'ms-sanfu-wap-customer-distribution/distribution/' + uri
}
Page({

  /**
   * 页面的初始数据
   */
  data: {
    pageStatus: 0, // 0 正常 2未定位
    type: '',
    showqrcode: true,
    goodsflag: false,
    discard: '', // 导购员卡号
    disshop: '', //导购员所在店号
    detail: {
      coverUrl: '',
      collectCnt: 0, //收藏次数
      goodsAmount: 0,
      headImgUrl: "",
      likeCnt: 0,
      nickName: "",
      pictures: [],
      recommendDesc: "",
      recommendId: "",
      recommendTitle: "",
      shareCnt: 0,
      collected: 0,
      liked: 0
    },
    goodslist: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log(options)
    this.data.options = options
    this.checkLocation()
  },
  init() {
    let options = this.data.options
    if (wx.getStorageSync('isStaff')) {
      this.setData({
        showShare: true
      })
    }
    if (options.type == 'preview') {
      this.setData({
        type: options.type //'preview'
      })
      wx.setNavigationBarTitle({
        title: '组合商品预览',
      })
    } else {
      this.data.discard = wx.getStorageSync('discard')

      this.data.disshop = wx.getStorageSync('disshop')
      this.viewer(2, options.id, this.data.discard)
    }
    this.getDetails(options.id)
  },
  checkLocation() {
    app.getLocation('', () => {
      if (app.local.get('dsho_id')) {
        this.setData({
          pageStatus: 0
        })
        this.init()
      } else {
        this.setData({
          pageStatus: 2
        })
      }
    })
  },
  /** 获取商品明细 */
  async getDetails(id) {
    let res
    this.setLoading(1)
    if (this.data.type == 'preview') {
      res = await app.reqGet(prefix('goodshDrafts/detail'), {
        sid: wx.getStorageSync('sid'),
        recommendId: id,
        channel: 'distributionFront'
      })
    } else {
      res = await app.reqGet(prefix('goodsh/detail'), {
        sid: wx.getStorageSync('sid'),
        id: id
      })
    }
    this.setLoading(0)
    if (res.success) {
      if (res.data.collectCnt === 0 && res.data.collected) {
        res.data.collectCnt = res.data.collectCnt + 1
      }
      if (res.data.likeCnt === 0 && res.data.liked) {
        res.data.likeCnt = res.data.likeCnt + 1
      }
      // 处理图片组及描述
      let contentarr = []
      let descarr = res.data.recommendDesc.split('[imgloc')
      console.log(descarr)
      for (let i in descarr) {
        if (!/\@[\d]\]/.test(descarr[i])) {
          contentarr.push({
            tempType: 'text',
            data: descarr[i]
          })
        } else {
          let subdescarr = descarr[i].split(']')
          if (subdescarr[0].indexOf('@') == 0) {
            subdescarr[0] = parseInt(subdescarr[0].split('@')[1] - 1)
            //添加图片模板
            if (res.data.tempPicList[subdescarr[0]]) {
              if (!res.data.coverUrl) {
                res.data.coverUrl = res.data.tempPicList[subdescarr[0]].imgList[0].originalUrl
              }
              contentarr.push(res.data.tempPicList[subdescarr[0]])
            }
            subdescarr.shift()
            subdescarr = subdescarr.join()
            if (subdescarr)
              contentarr.push({
                tempType: 'text',
                data: subdescarr
              })
          }
        }
      }
      res.data.tempList = contentarr
      this.setData({
        detail: res.data
      })
      wx.setNavigationBarTitle({
        title: res.data.recommendTitle || '图文详情'
      })
      console.log(this.data.detail.tempList)
    } else {
      wx.showToast({
        title: res.msg,
        icon: 'none'
      })
    }

  },
  async dianzan() {
    let detail = this.data.detail
    if (detail.liked === 1) {
      return
    }
    this.setLoading(1)
    let res = await this.traceRecord({
      fromCusId: this.data.discard,
      id: detail.recommendId,
      operateType: 4,
      scene: 2
    })
    this.setLoading(0)
    console.log(res)
    if (!res.success) {
      wx.showToast({
        title: res.msg,
        icon: 'none'
      })
    } else {
      let liked = 1
      let likeCnt = this.data.detail.likeCnt + 1
      this.setData({
        dianzanflag: true,
        detail: {
          ...this.data.detail,
          ...{
            likeCnt,
            liked
          }
        }
      })
    }

  },
  async collect() {
    let detail = this.data.detail
    if (detail.collected === 1) {
      return
    }
    let res = await this.traceRecord({
      fromCusId: this.data.discard,
      id: detail.recommendId,
      operateType: 3,
      scene: 2
    })
    if (!res.success) {
      wx.showToast({
        title: res.msg,
        icon: 'none'
      })
    } else {
      let collectCnt = this.data.detail.collectCnt + 1
      let collected = 1
      this.setData({
        collectflag: true,
        detail: {
          ...detail,
          ...{
            collectCnt,
            collected
          }
        }
      })
    }

  },
  async forward() {
    let detail = this.data.detail
    if (detail.collected === 1) {
      return
    }
    let res = await this.traceRecord({
      fromCusId: this.data.discard,
      id: detail.recommendId,
      operateType: 1,
      scene: 2
    })
    if (!res.success) {
      wx.showToast({
        title: res.msg,
        icon: 'none'
      })
    } else {
      let shareCnt = this.data.detail.shareCnt + 1
      this.setData({
        forwardflag: true,
        detail: {
          ...this.data.detail,
          ...{
            shareCnt
          }
        }
      })
    }

  },
  goGoodsDetail(e) {
    let goods = e.currentTarget.dataset.item
    if (!goods.gooId) return
    if (this.data.type == 'preview') {
      wx.showToast({
        title: '预览不支持打开商品',
        icon: 'none'
      })
      return
    }
    let shop = app.local.get('sho_id')
    let card = wx.getStorageSync('discard')
    let url = `goods/goodsDisplay?goods_sn=${goods.gooId}&type=SHOP&disshop=${shop}&discard=${card}`
    app.toH5(url)
  },
  /** 获取购物车商品列表 */
  async getGoods() {
    if (this.data.goodslist.length > 0) {
      return
    }
    this.setLoading(1)
    let res = await app.reqGet(prefix('goodsh/goods/list'), {
      sid: wx.getStorageSync('sid'),
      shoId: app.local.get('dsho_id'),
      id: this.data.detail.recommendId,
      orgId: wx.getStorageSync('orgId')
    })
    this.setLoading(0)
    if (res.success) {
      console.log(res)
      this.setData({
        goodslist: res.data
      })
    } else {
      wx.showToast({
        title: res.msg,
        icon: 'none'
      })
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {},


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {},
  openSku(e) {
    let i = e.currentTarget.dataset.item
    app.$bus.emit('setSkuGoods', {
      goods: i
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function(options) {
    let shareItem = this.data.detail
    let endUrl = `pages/transcenter/index?uri=/pages/distributershop/groupdetail/detail&data=disshop=${app.local.get('sho_id')},discard=${wx.getStorageSync('discard')},id=${shareItem.recommendId}`
    if (wx.getSystemInfoSync().environment == 'wxwork')
      endUrl += ',from=qymall'
    console.log('endUrl', endUrl)
    // 设置菜单中的转发按钮触发转发事件时的转发内容
    var shareObj = {
      title: shareItem.recommendTitle, // 默认是小程序的名称(可以写slogan等)
      path: endUrl, // 默认是当前页面，必须是以‘/’开头的完整路径
      imageUrl: shareItem.coverUrl || '',
    }
    app.sr.track('page_share_app_message', {
      "from_type": options.from,
      "share_title": shareItem.recommendTitle,
      "share_path": endUrl,
      "share_image_url": shareItem.coverUrl || ''
      // more...
    })
    // 返回shareObj
    return shareObj
  },
  closeQR() {
    this.setData({
      showqrcode: true
    })
  },
  changeGoodsFlag: function(e) {
    let come = e.currentTarget.dataset.come
    console.log(e, come)
    this.setData({
      goodsflag: 1
    })
    this.getGoods()
  },
  closeGoodsFlag() {
    this.setData({
      goodsflag: 0
    })
  },
  goCart() {
    console.log('cart?discard=' + wx.getStorageSync('discard') + '&disshop=' + app.local.get('sho_id'))
    app.toH5('cart?discard=' + wx.getStorageSync('discard') + '&disshop=' + app.local.get('sho_id'))
  },
  async showShare(e) {
    let detail = this.data.detail
    Share().then(el => {
      this.transferDetail(detail.recommendId, 2)
    }).catch(el => {
      this.transferDetail(detail.recommendId, 2)
      wx.navigateTo({
        url: '../groupshare/groupshare?type=group&id=' + detail.recommendId,
      })

    })
  },
  showimg(e) {
    wx.previewImage({
      current: e.currentTarget.dataset.r, // 当前显示图片的http链接
      urls: [e.currentTarget.dataset.r] // 需要预览的图片http链接列表
    })
  },
  ...methods,
})
