const app = getApp()
import common from '../common.js'
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    config: {
      type: null,
      value: ''
    },
    unitData: {
      type: null,
      value: '',
      observer: function(newVal, oldVal) {
        this.checkShow()
      }
    },
    shopInfo: {
      type: null,
      value: '',
    },
    pid: {
      type: null,
      value: ''
    },
    menuButton: {
      type: null,
      value: ''
    },
    index: {
      type: null,
      value: ''
    },
    isTop: {
      type: Boolean,
      value: false
    }
  },
  data: {
    currentTab: 0,
    observerOpt: {
      viewport: { top: 0, bottom: 999999999 }
    },
    showBg: false
  },
  attached() {
    this.loadBus()

    this.setData({
      observerOpt: {
        viewport: { top: -this.data.menuButton.t, bottom: 999999999 }
      }
    })
  },
  detached() {
    app.$bus.off('getColumnPageH')
    app.$bus.off('changeTcolor')
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {
      this.loadBus()
    },
    hide: function() {
      app.$bus.off('getColumnPageH')
      app.$bus.off('changeTcolor')
    },
    resize: function() {}
  },
  methods: {
    loadBus() {
      if (this.load) return
      this.load = 1
      app.$bus.on('getColumnPageH', () => {
        return (this.data.config.h || 80) + (this.data.config.mB || 0) + (this.data.config.mT || 0)
      })
      if (this.data.isTop) {
        app.$bus.on('changeTcolor', data => {
          if (data === this.data.tColor) return
          this.setData({
            tColor: data
          })
        })
      }
    },
    ...common,
    checkShow() {
      const newVal = this.data.unitData
      let currentTab = this.data.config.currentTab
      for (let i = 0; i < newVal.list.length; i++) {
        newVal.list[i].key = i
        let e = this.deleteImg(newVal.list[i])
        if (e > 0) {
          newVal.list.splice(i, 1)
          i--
          if (currentTab > i) currentTab--
        }
      }
      this.setData({
        unitData: newVal,
        currentTab
      })
    },
    handleTabClick(e) {
      const index = e.detail
      if (this.data.currentTab === index) return
      const item = this.data.unitData.list[index]
      console.log(item);

      try {
        app.sf.track('mallpage_click', {
          track_cfg_id: this.data.pid,
          track_title: this.data.unitName || this.data.config.unitName || '分页路由',
          text: item.trackMemo
        })
      } catch (e) {}
      if (item.linkType === 1 && item.link) {
        app.toH5(item.link)
        return
      } else {
        app.$bus.emit('setSubPageIndex', item, this.data.isTop)
      }
      this.setData({
        currentTab: index,
        tColor: ''
      })
    },
    nov(e) {
      if (this.data.showBg) return
      this.setData({
        showBg: true
      })
      app.$bus.emit('setSubTop', true)
    },
    each(e) {
      if (!this.data.showBg) return
      this.setData({
        showBg: false
      })
      app.$bus.emit('setSubTop', false)
    }
  }
});
