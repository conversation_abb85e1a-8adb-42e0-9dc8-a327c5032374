/* pages/couponcenter/history/history.wxss */
page {
  min-height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.swiper-tab {
  width: 100%;
  text-align: center;
  line-height: 80rpx;
  font-weight: 700;

}

.swiper-tab-list {
  font-size: 30rpx;
  display: inline-block;
  width: 20%;
  color: #444;
  margin-right: 4%;
  margin-left: 4%;
  position: relative;
}

.on {
  color: #E60012;
  /* border-bottom: 5rpx solid #E60012; */
}

.on::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 38%;
  border: 2px solid;
  border-radius: 5px;
  width: 20%;
  background: #E60012;
}

.swiper-box {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}

.swiper-box .coupon-list {
  width: 100%;
  background: #F8F8F8;
}


.popup {
  background: #f8f8f8;
  width: 650rpx;
  min-height: 400rpx;
  color: #303030;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 200rpx;
  display: flex;
  flex-direction: column;
}

.popup>view {
  margin: 0 auto;
  text-align: center;
  margin-bottom: 10rpx;
  font-size: 40rpx;
  font-weight: 700;
}

.popup>text {
  font-size: 28rpx;
}

.cancel-btn {
  color: #E60012;
  padding: 16rpx 8rpx;
  margin-top: 20rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #E60012;
  border-radius: 12rpx;
  min-width: 160rpx;
}
