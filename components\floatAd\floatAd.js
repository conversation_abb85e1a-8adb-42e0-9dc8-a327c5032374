const app = getApp()
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  /**
   * **自动化营销弹窗组件**
   * 
   * @property {boolean} show 是否展示
   * @property {Number} pageId 页面入口ID
   * @property {String} value 页面入口传参
   * @event {Function} getPopup 获取是否有可展示弹窗后触发
   */
  properties: {
    zIndex: {
      type: Number,
      value: 99
    },
    pageId: {
      type: Number,
      value: ''
    },
    show: {
      type: Boolean,
      value: false
    },
    isCallback: {
      type: Boolean,
      value: false
    },
    isslot: {
      type: Boolean,
      value: false
    },
  },
  data: {
    opened: false
  },
  attached() {},
  watch: {
    'show': function(newVal) {
      if (newVal) {
        wx.nextTick(() => {
          this.getCfg()
        })
      }
    }
  },
  detached() {},
  methods: {
    // 获取自动化营销方案
    async getCfg() {
      if (this.data.opened) return
      this.data.opened = true
      const res = await app.reqGet('ms-sanfu-spi-common/getFloatAd', {
        sid: wx.getStorageSync('sid'),
        seqno: this.data.pageId,
      })
      if (res.success && res.data) {
        app.sf.track('float_ad_show', {
          track_seqno: this.data.pageId,
          track_cfg_id: res.data.configId
        })
        this.setData({
          ...res.data
        })
      } else {
        this.data.opened = false
      }
    },
    // 关闭
    close() {
      this.setData({
        imgUrl: ''
      })
    },
    tapbox() {
      // this.close()
      if (this.data.isCallback) {
        this.triggerEvent('callback');
      } else {
        app.toH5(this.data.pageUrl)
      }
      app.sf.track('float_ad_click', {
        track_seqno: this.data.pageId,
        track_cfg_id: this.data.configId
      })
    }
  }
});
