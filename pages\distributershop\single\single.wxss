/* pages/distributershop/single/single.wxss */
@import "/font/icon_f7.wxss";
@import '../qrcode.wxss';
.single-date{
  position: absolute;
  top: 0;
  left: 0;
  color: white;
  font-size: 22rpx;
  background: rgba(0,0,0,0.8);
  border-radius:0 0 10rpx 0;
  padding: 0 6rpx;
}

.single-page{background:white;}
.show-detail-page{height: 800rpx;padding-bottom: 100rpx;width: 100%;background: #fff;}
.show-detail-page .detail{padding-top: 10rpx;color: #777;line-height: 32rpx;font-size: 26rpx;}
.single-items-list{width: 100%;padding-top: 20rpx;position: relative;}
.line-split{height: 20rpx;background: #EBEBEB;width: 100%;}
.single-item{margin: 0 auto;margin-bottom: 20rpx;padding-top: 16rpx;width: 95%;background: white;}
.single-item .line1{display: flex;border-bottom: 2rpx solid #eee;}
.single-item .line2 .top{font-size: 26rpx;color: #000;padding: 6rpx 0}
.single-item .line2 .bottom{font-size: 26rpx;color: #777;max-height: 40rpx;line-height: 40rpx;text-overflow: ellipsis;overflow: hidden;text-overflow: -o-ellipsis-lastline;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;}
.single-item .line3{padding:10rpx 0;}
.single-item .line3 .top{font-size: 26rpx;color: #000;padding: 6rpx 0;}
.single-item .line3 .bottom{font-size: 26rpx;max-height: 80rpx;line-height: 40rpx;color: #777;text-overflow: ellipsis;overflow: hidden;text-overflow: -o-ellipsis-lastline;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;}
.single-item .image{ width:240rpx;
    height: 240rpx;margin-right: 10rpx;border-radius: 6rpx;
    position: relative;
    overflow:hidden;
    background-position: center center;
    background-repeat: no-repeat;
    -webkit-background-size:cover;
    -moz-background-size:cover;
    background-size:cover;}
    .price{
      color: #F63979;padding-top: 10rpx;
    }
.single-item .item-detail{flex: 1;display: flex;flex-direction: column;}
.single-item .item-detail .title{width: 100%;font-size: 26rpx;color: #444;text-overflow: ellipsis;overflow: hidden;text-overflow: -o-ellipsis-lastline;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;height: 80rpx;line-height: 40rpx;}
.show-detail{font-size: 26rpx;color: #F63979;text-align: center;width: 100%;}
.go-back{font-size: 36rpx;color: white;text-align: center;width: 100%;height: 100rpx;line-height: 100rpx;position: fixed;bottom: 0px;left: 0;background: linear-gradient(to right, #FF87A5, #F63979);}
.sub-icon{margin-left: 40rpx;font-size: 22rpx;background: linear-gradient(to right, #FF6E93, #FF6E93);color: white;border-radius: 10rpx;padding: 2rpx 10rpx;}
.viewer{display: flex;font-size: 24rpx;margin-top: 30rpx;align-items: center;justify-content: space-between;padding-right: 20rpx;}
.viewer-stock{color: #999;font-size: 26rpx;margin-right: auto;}
.viewer-count{display: flex;align-items: center;position: relative;color: #C7C6C6;padding: 20rpx;}
.viewer-count text{color: #555;font-size: 26rpx;left: 120%;top: -10rpx;margin-left: 10rpx;}
.viewer-share{display: flex;align-items: center;}
.viewer-share text{background: #FF6E93;color: white;border-radius: 40rpx;padding: 2rpx 10rpx;font-size: 20rpx;}
/* 搜索框 */
.bar-top {box-sizing: border-box;width: 100%;height: 80rpx;padding-right: 16rpx;display: flex;align-items: center;background: #FF6E93;}
.bar-top>image {width: 50rpx;height: 50rpx;margin-left: 16rpx;}
.bar-top>text {width: 70rpx;height: 70rpx;margin-left: 16rpx;color: #f6f6f6;font-size: 56rpx;display: flex;align-items: center;justify-content: center;}
.bar-top>view.item {margin: 0;margin-left: 16rpx;height: 70rpx;min-width: 70rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;color: #fff;}
.bar-top>view.item>text {display: block;font-size: 24rpx;line-height: 100%;color: #202020;}
.bar-top>view.search-input {height: 60rpx;flex: 1;background-color: #fff;border-radius: 16rpx;font-size: 28rpx;margin: 0;margin-left: 16rpx;padding-left: 16rpx;display: flex;flex-direction: row;align-items: center;overflow: hidden;}
.bar-top>view.search-input>input {flex: 1;border: none;min-height: auto;display: inline-block;color: #fff;font-size: 30rpx;}
.bar-top>view.search-input>image {width: 32rpx;height: 32rpx;margin-right: 10rpx;}

.top-nav{
  box-sizing: border-box;
  height: 72rpx;
  border-bottom: 2rpx solid #ececec;
  display: flex;
  align-items: center;
  justify-content: space-around;
  font-size: 30rpx;
  font-weight: 700;
  color: #999;
}