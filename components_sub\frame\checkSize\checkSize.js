const app = getApp()
import Auth from '../../../auth/index';
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    new: {
      type: Boolean,
      value: true
    }
  },
  data: {
    multiple: false, // 可多选

    selectType: 0, //0普通  1拼团   2秒杀
    selColor: '',
    selSize: '',
    buyNum: 1,
    barCode: '',

    imgUrl: '',
    showType: 0, // 0 加购+下单  1 加购  2下单

    promoList: [],
    showSizeList: [], // 显示的尺码列表
    showColorList: [], // 颜色列表
    tImgUrl: '', // 默认商品图.
    groupList: [], //拼团列表的商品数
    sizeIdx: 0, //拼团多选下，选中的尺码索引
  },
  attached() {
    this.skuList = []
    this.options = ''
    this.goods = ''
    this.load()
  },
  detached() {
    app.$bus.off('setSkuGoods')
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function () {
      if (this.isload) return
      this.load()
    },
    hide: function () {
      this.isload = false
      app.$bus.off('setSkuGoods')
    },
    resize: function () { },
  },
  methods: {
    load() {
      this.isload = true
      app.$bus.on('setSkuGoods', data => {
        this.triggerEvent('open', 1)
        
        this.oldSelect = data.select || ''
        this.goods = data.goods
        this.text = data.text
        if (this.goods.qiangId) {
          this.goods.rushPurchaseId = this.goods.qiangId
          this.goods.scPrice = this.goods.salePrice
        } else if (this.goods.groupBuyId) {
          this.goods.scPrice = this.goods.salePrice
        } else if (this.goods.memberPrice) {
          this.goods.salePrice = this.goods.memberPrice
        }

        if (this.goods.gooId && !this.goods.goodsSn) {
          this.goods.goodsSn = this.goods.gooId
        }
        this.setData({
          isBack: data.isBack || undefined,
          groupSalePrice: this.goods.groupBuyPrice, //拼团
          qiangPrice: this.goods.qiangPrice, //秒杀
          commonPrice: this.goods.salePrice, //普通
        })
        // selectType: 0普通  1拼团   2秒杀
        // showType 0 加购+购买  1 加购  2立即购买
        this.setData({
          skuShow: 1,
          selectType: this.goods.groupBuyId ? 1 : this.goods.rushPurchaseId ? 2 : 0,
          showType: data.showType == 1 ? 1 : 0,
          canMultiple: this.goods.groupBuyId ? true : false
        })
        this.setData({
          imgUrl: '',
          groupList: [],
          showSizeList: [],
          showColorList: [],
          selColor: '',
          selSize: '',
          barCode: ''
        })
        this.data.barCode = ''
        this.getSkuData()
        this.checkLimit()
      })
    },
    async checkLimit() {
      if (await app.util.checkGoodsDistributer(this.data.goodsSn) > 0)
        wx.removeStorageSync('discard')
    },
    close(e) {
      this.triggerEvent('close', 0)
      let list = this.saveSkuVuex()
      if (!app.globalData.gooSelect) app.globalData.gooSelect = {}
      if (list.length == 1) {
        app.globalData.gooSelect[this.goods.goodsSn] = list[0]
      } else delete app.globalData.gooSelect[this.goods.goodsSn]
      this.setData({
        skuShow: false
      })
    },
    getSkuData() {
      // 清空尺寸缓存
      console.log(app.globalData);
      this.data.tImgUrl = this.goods.lImg || this.goods.mImg || this.goods.sImg || 'https://img.sanfu.com/sf_access/uploads/GmFvcfsdLb2KmHQnW1aF3HG9dyk8zGxF.png'
      this.shopId = app.local.get('dsho_id')
      app.reqGet('ms-sanfu-wap-goods-item/selectBacodeNew', {
        shoId: this.shopId,
        goodsSn: this.goods.goodsSn,
        qiangId: this.goods.rushPurchaseId || ''
      }, res => {
        if (res.success) {
          this.skuList = res.data.colorList
          // 促销过滤秒杀、拼团
          this.promoBarcodeList = []
          if (this.data.selectType != 2 && this.data.selectType != 1) {
            res.data && res.data.promoBarcodeList.forEach(e => this.promoBarcodeList = [...this.promoBarcodeList, ...e.promoList])
          }
          console.log(this.promoBarcodeList);
          const showData = []
          // 预售列表处理 
          let preSalePlanGoodsList = []
          const showPreSaleList = []
          res.data.preSalePlanGoodsList && res.data.preSalePlanGoodsList.forEach(e => {
            let time = `预售最迟${new Date(e.estimateConTime).getMonth() + 1}/${new Date(e.estimateConTime).getDate()}发货`
            preSalePlanGoodsList = [...preSalePlanGoodsList, ...e.barcodeList.map(e2 => {
              return {
                preSaleId: e.preSaleId,
                time,
                barcode: e2
              }
            })]
            if (showPreSaleList.every(e => e.title != time)) {
              showPreSaleList.push({
                title: time
              })
            }
          })
          if (showPreSaleList.length) {
            showData.push(showPreSaleList)
          }
          console.log('this.preSalePlanGoodsList', preSalePlanGoodsList);
          // ---遍历总色码列表
          this.allImg = []

          // 取全部尺码
          let allShopStock = 0
          let c1, c2
          let defaultColor = '' // 默认选择颜色
          let defaultSize = ''
          this.skuList.map(colors => {
            let changeColor
            if (!colors.color) {
              changeColor = true
              colors.color = colors.color || '默认' + (index ? index : '')
            }
            colors.preSaleTimes = []
            colors.sizeList.map((sitem, index) => {
              // 色码去空格
              sitem.sizeName = sitem.sizeName && sitem.sizeName.replace(/\s+/g, '')
              if (!colors.bImg && sitem.bImg) {
                colors.bImg = sitem.bImg
                this.allImg.push(colors.bImg) // push所有图片链接
              }
              delete sitem.bImg
              // 促销
              if (this.promoBarcodeList.some(e => e.barcode === sitem.barcode)) {
                sitem.showPromo = true
                colors.showPromo = true
              }

              if (sitem.colorId == colors.colorId && changeColor) {
                sitem.colorName = colors.color
              }
              // 预售
              const preSale = preSalePlanGoodsList.find(e => e.barcode == sitem.barcode)
              if (preSale && preSale.preSaleId) {
                sitem.preSaleId = preSale.preSaleId
                sitem.preSaleTime = preSale.time
                sitem.preSale = true
                colors.preSale = true
                colors.preSaleTimes.push(preSale.time)
                if (sitem.stockAmount > 0 && !defaultColor) {
                  defaultColor = colors.color
                }
              } else if (showPreSaleList.length) {
                if (showPreSaleList.every(e => e.title != '现货')) {
                  showPreSaleList.unshift({
                    title: '现货'
                  })
                }
                if (sitem.stockAmount > 0 && !c1) {
                  c1 = colors.color
                  defaultColor = c1
                }
              } else if (sitem.stockAmount > 0 && !defaultColor && !colors.preSale) {
                // 取第一个颜色
                defaultColor = colors.color
              }

              let hasSize = this.data.showSizeList.some(el => el.sizeName == sitem.sizeName)
              if (!hasSize) {
                this.data.showSizeList.push({
                  sizeName: sitem.sizeName,
                  disable: false,
                  barcode: ''
                })
              }
              sitem.stockAmount = sitem.stockAmount < 0 ? 0 : sitem.stockAmount
              allShopStock += sitem.stockAmount
            })
          })

          console.log('this.skuList', this.skuList);
          if (allShopStock == 0) {
            wx.showToast({
              title: `该商品已售罄`,
              icon: 'none',
              duration: 1500
            })
            this.setData({
              stockAmount: 0
            })
          }
          console.log('总库存', allShopStock)
          this.data.showSizeList.sort((a, b) => { // 尺码排序
            return a.sizeName - b.sizeName
          })
          // 默认图设置
          if (this.data.tImgUrl && this.allImg.indexOf(this.data.tImgUrl) == -1)
            this.allImg.unshift(this.data.tImgUrl)
          // 默认预售选项
          this.data.selPresale = showPreSaleList.length ? 0 : ''
          this.data.showPreSaleList = showPreSaleList
          this.updateColorList()
          // 获取历史选择数据
          let hisSelect = this.getHistorySelect()
          if (this.oldSelect) {
            this.data.selColor = this.oldSelect.colorName
            this.data.selSize = this.oldSelect.sizeName
            this.data.barCode = this.oldSelect.barCode
            this.updateSizeList()
            this.updateStockAmount()
            this.setData({
              selColor: this.data.selColor,
              selSize: this.data.selSize,
              buyNum: this.oldSelect.amount,
              imgUrl: this.oldSelect.imgUrl
            })
          } else if (hisSelect) {
            console.log(hisSelect);
            this.data.selColor = hisSelect.colorName
            this.data.selSize = hisSelect.sizeName
            this.data.barCode = hisSelect.goodsid
            this.updateSizeList()
            this.updateStockAmount()
            this.setData({
              selColor: this.data.selColor,
              selSize: this.data.selSize,
              buyNum: hisSelect.amount
            })
          } else if (defaultColor) {
            // 默认选中第一个
            this.data.selColor = defaultColor
            let defaultSizeItem = this.updateSizeList(1)
            this.setData({
              selColor: this.data.selColor,
            })
            if (defaultSizeItem) {
              this.setData({
                sizeIdx: defaultSizeItem.index,
                selSize: defaultSizeItem.sizeName,
                stockAmount: defaultSizeItem.sAmount
              })
              this.data.barCode = defaultSizeItem.barcode
              this.updateStockAmount()
            }
          }
          if (res.data.hasPromoBarcode && Array.isArray(res.data.promoBarcodeList) && res.data.promoBarcodeList.length == 0) {
            this.data.tip = '* 促销色码已售罄'
          } else {
            this.data.tip = ''
          }
          this.setData({
            selPresale: this.data.selPresale,
            showPreSaleList: this.data.showPreSaleList,
            classId: res.data.classId, // 分类
            hasPromo: this.promoBarcodeList.length ? true : false,
            tImgUrl: this.data.tImgUrl,
            tip: this.data.tip,
            showAfterLimit: res.data.noReasonFlag === 0 ? true : false,
          })
        } else {
          app.util.reqFail(res)
        }
      })
    },
    getHistorySelect() {
      if (!app.globalData.gooSelect) return
      let data = app.globalData.gooSelect[this.goods.goodsSn]
      if (data && data.goodsid) {
        for (let i in this.skuList) {
          for (let j in this.skuList[i].sizeList) {
            let item = this.skuList[i].sizeList[j]
            if (item.barcode == data.goodsid) {
              if (item.stockAmount == 0) return ''
              return {
                colorName: this.skuList[i].color,
                sizeName: item.sizeName,
                goodsid: data.goodsid,
                amount: data.amount <= item.stockAmount ? data.amount : item.stockAmount
              }
            }
          }
        }
      }
      return ''
    },
    updateColorList() {
      // return
      let check_item, check_item_num = 0 //默认check
      if (this.data.showSizeList.length <= 0) return
      this.skuList.map((item, index) => {
        item.disable = true
        let noPreSaleNum = 0
        for (let i in item.sizeList) {
          if (item.sizeList[i].colorName != item.color) continue
          if (item.sizeList[i].stockAmount > 0) {
            check_item = item.sizeList[i]
            check_item_num++
            item.disable = false
            if (!item.sizeList[i].preSale) {
              noPreSaleNum++
            }
          }
        }
        // 预售判断 240807
        if (this.data.showPreSaleList.length) {
          if (this.data.showPreSaleList[this.data.selPresale].title !== '现货') {
            if (item.preSaleTimes.every(e => e !== this.data.showPreSaleList[this.data.selPresale].title)) {
              item.disable = true
            }
          } else if (!noPreSaleNum) {
            item.disable = true
          }
        }
      })
      wx.nextTick(() => {
        let colorList = []
        for (let i in this.skuList) {
          let item = this.skuList[i]
          colorList.push({
            bImg: item.bImg,
            color: item.color,
            showPromo: item.showPromo,
            disable: item.disable,
            preSale: item.preSale
          })
        }
        this.setData({
          showColorList: colorList
        })
        console.log(colorList);
      })
    },
    updateSizeList() {
      const selColor = this.data.selColor
      if (this.skuList.length <= 0) return
      let check_item, check_item_num = 0 //默认check
      if (!selColor) {
        this.data.showSizeList.map((item, index) => {
          item.disable = true
          item.sAmount = 0
          item.index = index
          item.showPromo = ''
          item.preSale = ''
          for (let i in this.skuList) {
            if (!item.disable) break
            for (let s in this.skuList[i].sizeList) {
              if (this.skuList[i].sizeList[s].sizeName == item.sizeName) {
                item.sAmount += this.skuList[i].sizeList[s].stockAmount
                item.selectnum = 0
                item.skuCusLimitamount = 0
                if (this.skuList[i].sizeList[s].stockAmount > 0) {
                  check_item_num++
                  if (!check_item) check_item = item
                  item.disable = false
                }
              }
            }
          }
        })
      } else {
        let sizeList = this.skuList.find(o => o.color == selColor).sizeList
        this.data.showSizeList.map((item, index) => {
          item.sAmount = 0
          item.disable = true
          item.index = index
          for (let s in sizeList) {
            if (sizeList[s].sizeName == item.sizeName) {
              // console.log(sizeList[s], item);
              item.sAmount = sizeList[s].stockAmount
              if (sizeList[s].skuCusLimitamount > 0 && sizeList[s].stockAmount < sizeList[s].stockAmount) {
                item.sAmount = sizeList[s].skuCusLimitamount
              }
              // 231024 最大限购35
              if (item.sAmount > 35) {
                item.sAmount = 35
              }
              item.selectnum = sizeList[s].selectnum || 0
              item.barcode = sizeList[s].barcode || ''
              item.showPromo = sizeList[s].showPromo // 色码促销
              item.disable = item.sAmount > 0 ? false : true
              item.preSale = sizeList[s].preSale
              // 预售判断 240807
              if (this.data.showPreSaleList.length) {
                if (this.data.showPreSaleList[this.data.selPresale].title !== '现货') {
                  if (!item.preSale || sizeList[s].preSaleTime !== this.data.showPreSaleList[this.data.selPresale].title) {
                    item.disable = true
                  }
                } else if (item.preSale) {
                  item.disable = true
                }
              }
              check_item_num++
              if (!check_item && !item.disable) check_item = item
              if (this.data.selSize == item.sizeName && item.disable) {
                // 取消颜色根据色码变化后 更改颜色无货色码取消默认
                wx.showToast({
                  title: '当前尺码已无货，请重新选择',
                  icon: 'none'
                })
              }
            }
          }
        })

      }
      wx.nextTick(() => {
        this.setData({
          showSizeList: this.data.showSizeList,
        })
      })
      return check_item
    },
    selectPresale(e) {
      let index = e.currentTarget.dataset.index
      this.setData({
        selPresale: index
      })
      this.updateColorList()
      this.updateSizeList()
      this.updateStockAmount()
    },
    selectColor(e) {
      let index = e.currentTarget.dataset.index
      let disable = this.skuList[index].disable
      // if (disable) return
      let selColor = this.skuList[index].color
      let imgUrl = this.skuList[index] && this.skuList[index].bImg
      if (selColor == this.data.selColor) {
        if (this.skuList.length === 1) return
        this.data.selColor = ''
        imgUrl = this.data.tImgUrl
      } else this.data.selColor = selColor
      if (imgUrl != '') {
        this.data.imgUrl = imgUrl
      } else {
        this.data.imgUrl = this.data.tImgUrl
      }
      this.setData({
        imgUrl: this.data.imgUrl,
        selColor: this.data.selColor
      })
      this.updateSizeList()
      this.updateStockAmount()
    },
    // 尺码选择
    selectSize(e) {
      let index = e.currentTarget.dataset.index
      let selSize = this.data.showSizeList[index].sizeName
      let disable = this.data.showSizeList[index].disable
      // if (disable) return
      if (selSize == this.data.selSize) {
        if (this.data.showSizeList.length === 1) return
        this.data.selSize = ''
      } else this.data.selSize = selSize || ''
      this.setData({
        buyNum: 1,
        selSize: this.data.selSize,
        sizeIdx: this.data.selSize ? index : ''
      })
      this.updateStockAmount()
    },
    updateStockAmount() {
      if (this.data.selSize == '' || this.data.selColor == '') {
        this.setData({
          barCode: '',
          disable: ''
        })
        return
      }
      let color = this.skuList.filter(el => el.color == this.data.selColor)[0] || {}
      let list = color.sizeList || []
      let size = list.filter(el => el.sizeName == this.data.selSize)[0] || {}
      if (!size.barcode) {
        this.setData({
          barCode: ''
        })
        return
      }
      let promoList = this.promoBarcodeList.filter(item => size.barcode === item.barcode) || {}
      this.setData({
        stockAmount: size.skuCusLimitamount > 0 ? size.skuCusLimitamount : size.stockAmount || 0, // 官网库存
        skuCusLimitamount: size.skuCusLimitamount,
        promoList: promoList,
        barCode: size.barcode || '',
        disable: this.data.showSizeList.find(e => e.sizeName == this.data.selSize).disable
      })
    },
    stepperNumChange(e, isdefault) {
      let barcode = e.currentTarget.dataset.barcode
      let i1, i2
      for (let i in this.skuList) {
        if (this.skuList[i].color == this.data.selColor) {
          for (let j in this.skuList[i].sizeList) {
            // console.log(this.skuList[i].sizeList[j].barcode, barcode);
            if (this.skuList[i].sizeList[j].barcode == barcode) {
              i1 = i
              i2 = j
              break
            }
          }
          break
        }
      }
      if (i1 >= 0 && i2 >= 0) {
        this.skuList[i1].sizeList[i2].selectnum = parseInt(e.detail)
        // 将尺码的索引值存入groupListIdx
        this.skuList[i1].sizeList[i2].groupListIdx = '' + i1 + i2;
        this.addGroupList(this.skuList[i1].sizeList[i2])
        if (!isdefault)
          this.setData({
            [`showSizeList[${i2}].selectnum`]: parseInt(e.detail)
          })
        // this.updateColorList()
        // console.log(this.skuList);
      }
    },
    addGroupList(groupObj) {
      //添加到拼团商品列表
      if (this.data.groupList.length == 0) {
        this.data.groupList.push(groupObj)
      } else {
        let idx = this.data.groupList.findIndex((v) => {
          return v.groupListIdx == groupObj.groupListIdx
        })
        if (idx < 0) {
          this.data.groupList.push(groupObj)
        } else if (idx > -1 && this.data.groupList[idx].selectnum == 0) {
          this.data.groupList.splice(idx, 1)
        }
        // 尺码排序
        if (this.data.groupList.length != 0) {
          this.data.groupList.sort((a, b) => {
            return a.groupListIdx - b.groupListIdx
          })
        }
      }
      this.setData({
        groupList: this.data.groupList
      })
    },
    delGroupItem(e) {
      // 移除拼团列表商品
      let index = e.currentTarget.dataset.index
      const i1 = this.data.groupList[index].groupListIdx[0],
        i2 = this.data.groupList[index].groupListIdx[1]
      this.skuList[i1].sizeList[i2].selectnum = 0

      this.data.groupList.splice(index, 1)
      this.setData({
        groupList: this.data.groupList,
        [`showSizeList[${i2}].selectnum`]: 0
      })
    },
    onNumChange(e) {
      let target = e.currentTarget.dataset.key
      this.setData({
        [target]: e.detail || 1
      })
    },
    saveSkuVuex() {
      // 提取已选列表
      let cartList = []
      this.tracklist = []
      if (this.data.multiple) {
        this.data.groupList.forEach(e => {
          cartList.push({
            goodsid: e.barcode,
            amount: e.selectnum
          })
        })
      } else {
        if (!this.data.barCode) return []
        this.tracklist = [{
          barcode: this.data.barCode,
          selectnum: this.data.buyNum,
          colorName: this.data.selColor,
          sizeName: this.data.selSize
        }]
        return [{
          goodsid: this.data.barCode,
          amount: this.data.buyNum
        }]
      }
      return cartList
    },
    async confirm(e) {

      let type = e.currentTarget.dataset.type
      let cartList = this.saveSkuVuex()
      if (cartList.length == 0) {
        wx.showToast({
          title: '请选择尺码/颜色',
          icon: 'none',
          duration: 1500
        })
        return
      }
      if (!app.local.get('dsho_id')) {
        wx.hideToast()
        app.getLocation('', res => {
          if (app.local.get('dsho_id')) {
            this.confirm(e)
          }
        }, '需要开启定位后下单')
        return
      }
      let bindcard = wx.getStorageSync('bindcard')
      if (bindcard == 4) {
        wx.hideToast()
        Auth(4, '', '', () => {
          this.confirm(e)
        }, this)
        return
      }

      var gooID = cartList && cartList[0] && cartList[0].goodsid
      console.log(gooID);
      if (gooID.length > 6) {
        gooID = gooID.slice(0, 6)
      }

      if (this.data.isBack) {
        this.triggerEvent('callback', {
          oldBarcode: this.barcode,
          newBarcode: this.data.barCode,
          amount: this.data.buyNum,
          name: this.data.selColor + ' ' + this.data.selSize,
          stockAmount: this.data.stockAmount,
          img: this.data.imgUrl
        })
        this.triggerEvent('close')
        this.setData({
          skuShow: false
        })
        return
      }

      wx.showToast({
        title: '请稍等...',
        icon: "none",
        duration: 100000,
        mask: true
      })
      let buyType = 0
      if (e.currentTarget.dataset.type == 'buyNow') buyType = 1

      let card = wx.getStorageSync('discard') == wx.getStorageSync('cardid') ? '' : app.local.get('discard') || ''
      app.reqPost('ms-sanfu-wap-cart/addNetSaleCart', {
        buyType: buyType, // 1立即购买 0：普通加购
        fromShoId: card ? app.local.get('sho_id') : '',
        fromCusId: card, // 导购商城加购新增字段
        sid: wx.getStorageSync('sid'),
        shoId: this.shopId,
        goodsidData: JSON.stringify(cartList),
        gooId: gooID,
        chanId: '',
        ...this.data.buyData,
        qiangId: this.goods.rushPurchaseId || '',
        groupBuyId: buyType == 1 && this.goods.rushPurchaseId == '' && this.goods.groupBuyId || '',
        kpiFlag: app.globalData.kpiFlag,
        providerUrlSendTimeStamp: app.globalData.providerUrlSendTimeStamp,
        providerUserId: app.globalData.providerUserId,
        wxWorkUserId: app.globalData.wxWorkUserId,
        customMaterialType: app.globalData.customMaterialType,
        customMaterialValue: app.globalData.customMaterialValue
      }, res => { // 加入购物车成功刷新购物车数量
        wx.hideToast()
        if (res.success) {
          let selColorCode, prod_num = 0
          this.data.showColorList.forEach((v) => {
            if (v.color == this.data.selColor) {
              selColorCode = v.colorId
            }
          })
          let page = getCurrentPages()
          if (this.data.groupList.length > 0) {
            //拼团多选，商品数累计
            this.data.groupList.forEach(v => {
              prod_num += v.selectnum
            })
          } else {
            prod_num = this.data.buyNum
          }
          console.log(this.goods);
          for (let i in this.tracklist) {
            const data = {
              gooid: gooID,
              shoid: this.shopId,
              barcode: this.tracklist[i].barcode,
              action_num: this.tracklist[i].selectnum,
              goodsName: this.goods.goodsName,
              price_original: this.goods.scPrice,
              price_current: this.qiang ? this.qiang.salePrice : this.group ? this.group.groupSalePrice : this.goods.salePrice,
              sku_name: this.tracklist[i].colorName + ' ' + this.tracklist[i].sizeName,
              action_type: buyType ? 2 : 0, // 0加购 1减购 2立即购买
              category: this.goods.category,
              classId: this.goods.classId,
              sku_count: this.tracklist[i].selectnum,
              srtype: 'first_add_to_cart',
              text: this.text
            }
            app.report.purchaseGoods(data)
            if (buyType) app.report.submitOrder(data)
          }
          /******** 数据记录 end **********/
          this.setData({
            skuShow: false
          })
          if (buyType) {
            if (this.data.selectType == 1) {
              if (this.goods.groupBuyId) {
                // 开团立即购买（多1参）
                this.toCount({
                  groupBuyId: this.goods.groupBuyId
                }, `count?cartType=1&groupBuyId=${this.goods.groupBuyId}`)
              }
            } else if (this.goods.rushPurchaseId) {
              this.toCount({
                qiang_id: this.goods.rushPurchaseId,
              }, `count?cartType=1&secKillQiang_id=${this.goods.rushPurchaseId}`)
            } else { //普通立即购买
              this.toCount({}, `count?cartType=1`)
            }
          } else {
            wx.showToast({
              title: '加入购物车成功',
              icon: 'none',
              duration: 2000
            })
            this.triggerEvent('callback', 1)
          }

        } else {
          app.util.reqFail(res)
        }
      })
    },
    toCount(obj, url) {
      app.reqGet('ms-sanfu-wap-cart/countNetSaleCart', {
        sid: wx.getStorageSync('sid'),
        buyType: 1,
        shoId: this.shopId,
        ...obj
      }, res => {
        if (res.success) {
          this.setData({
            skuShow: 0
          })
          setTimeout(() => {
            wx.nextTick(() => {
              wx.hideToast()
              app.local.set('countData', res, 60)
              app.toH5(url)
            })
          }, 150)
        } else {
          wx.hideToast()
          app.util.reqFail(res)
        }
      })
    },
    openImg(e) {
      let img
      if (e && e.currentTarget.dataset.img) {
        img = e.currentTarget.dataset.img
      } else img = this.data.imgUrl
      wx.previewImage({
        current: img, // 当前显示图片的http链接
        urls: this.allImg // 需要预览的图片http链接列表
      })
    },
    openMultiple() {
      if(!this.data.multiple){
        wx.showModal({
          title:'提示',
          content:'可分别选择不同色码添加数量',
          showCancel:false,
          confirmText:'我知道了'
        })
      }
      this.setData({
        multiple: !this.data.multiple
      })
    }
  },
});
