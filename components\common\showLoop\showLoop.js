const app = getApp()
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
    // addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    list: {
      type: Array,
      value: []
    },
    listkey: {
      type: String,
      value: ''
    },
    showType: {
      type: Number,
      value: 1
    },
    duration: {
      type: Number,
      value: 3500
    },
  },
  data: {
    content: '',
  },
  watch: {
    'list': function() {
      this.oldlist = this.data.list
      this.loadData()
    }
  },
  attached() {},
  detached() {},
  pageLifetimes: {
    show: function() {
      setTimeout(() => {
        this.loadData()
      }, 5000)
    },
    hide: function() {
      clearTimeout(this.timer)
    },
    resize: function() {},
  },
  methods: {
    loadData() {
      console.log('11111111111111111111');
      if (!(Array.isArray(this.oldlist) && this.oldlist.length > 0)) return
      let index = Math.floor(Math.random() * this.oldlist.length) // 随机index
      let text = this.oldlist[index]
      if (this.data.listkey) text = text[this.data.listkey]
      this.oldlist.splice(index, 1)
      let duration = this.data.duration + Math.random() * 5000 + 1000
      this.timer = setTimeout(() => {
        this.setData({
          content: text,
        })
        setTimeout(() => {
          this.setData({
            content: '',
          })
          this.loadData()
        }, 3000)
      }, duration)
    }
  },
});
