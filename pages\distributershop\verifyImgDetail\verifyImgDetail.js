// pages/distributershop/verifyImgDetail/verifyImgDetail.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    viewDetail: '',
    loading: true
  },
  onLoad(options) {
    this.gooid = options.gooid
    this.getDetail()
  },
  async getDetail() {
    const res = await app.reqGet('ms-sanfu-wap-customer-distribution/goods/getVerifyImgInfor', {
      sid: wx.getStorageSync('sid'),
      goodsSn: this.gooid
    })
    if (res.success) {
      this.setData({
        viewDetail: res.data
      })
    } else {
      app.uitl.reqFail(res)
    }
    this.setData({
      loading: false
    })
  },
  verifyImg(e) {
    // 审核操作
    const type = e.currentTarget.dataset.type
    wx.showModal({
      title: '确认操作吗',
      content: '请认真核对图片和实物是否相符',
      success: async r1 => {
        if (r1.confirm) {
          this.setData({
            loading: true
          })
          const res = await app.reqPost('ms-sanfu-wap-customer-distribution/goods/verifyImg', {
            sid: wx.getStorageSync('sid'),
            goodsSn: this.gooid,
            isPass: type
          })
          this.setData({
            loading: false
          })
          if (res.success) {
            this.getDetail()
          } else {
            app.uitl.reqFail(res)
          }
        }
      }
    })
  }
})
