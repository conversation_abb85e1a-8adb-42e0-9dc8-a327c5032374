.uni-popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
  height: 100%;
}
.uni-popup__mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
}
.mask-ani {
  -webkit-transition-property: opacity;
  transition-property: opacity;
  -webkit-transition-duration: 0.2s;
          transition-duration: 0.2s;
}
.uni-top-mask {
  opacity: 1;
}
.uni-bottom-mask {
  opacity: 1;
}
.uni-center-mask {
  opacity: 1;
}
.uni-popup__wrapper {
  display: block;
  position: absolute;
}
.top {
  top: 0;
  left: 0;
  right: 0;
  -webkit-transform: translateY(-500px);
          transform: translateY(-500px);
}
.bottom {
  bottom: 0;
  left: 0;
  right: 0;
  -webkit-transform: translateY(500px);
          transform: translateY(500px);
}
.center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-transform: scale(1.2);
          transform: scale(1.2);
  opacity: 0;
}
.uni-popup__wrapper-box {
  display: block;
  position: relative;
  height: 100%;
}
.content-ani {
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transition-duration: 0.2s;
          transition-duration: 0.2s;
}
.uni-top-content {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.uni-bottom-content {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.uni-center-content {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}
