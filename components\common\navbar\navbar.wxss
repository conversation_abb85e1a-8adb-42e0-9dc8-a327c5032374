@import "/font/icon_f7.wxss";
@import "/font/uni-icon.wxss";

view {
  box-sizing: border-box;
}

.nav-box {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  z-index: 9999;
  background: #fff;
  transition: 0.2s all;
  display: flex;
  flex-direction: column;
  /* background: linear-gradient(90deg,#ff6a8f,#f32871); */
}

.statusbar {
  width: 100%;
  height: 20px;
  max-height: 80px;
}

.titlebar {
  width: 100%;
  height: 20px;
  max-height: 80px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  /* background: #fff; */
  color: #202020;
  font-weight: 500;
  font-size: 28rpx;
  /* filter: brightness(20); */
}

.titlebar .title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  /* background: #fff; */
  /* color: #fff; */
}


.titlebar .slot-title {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.actionBtn {
  position: fixed;
  left: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 64rpx;
  top: 40px;
  border-radius: 999999999px;
  z-index: 99999999;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.95);
  transition: 0.2s all;
}

.actionBtn .baricon {
  padding: 0 24rpx;
  font-size: 42rpx;
  font-weight: 700;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  mix-blend-mode: difference;
  /* 负阴影效果 */
}

.actionBtn .uni-icon-bars {
  font-weight: normal;
  position: relative;
}

.actionBtn .uni-icon-bars::before {
  transform: scaleY(1.45);
}

.actionBtn .uni-icon-bars::after {
  content: " ";
  position: absolute;
  height: 70%;
  top: 15%;
  left: 0;
  width: 2rpx;
  transform: scale(0.8);
  background: #f0f0f0;
  border-radius: 10px;
  margin-left: -1rpx;
}

.hover-nav {
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.1);
}

.place-wrap {
  height: auto;
  position: relative;
  display: flex;
  flex-direction: column;
}

.hidetop {
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1;
}
