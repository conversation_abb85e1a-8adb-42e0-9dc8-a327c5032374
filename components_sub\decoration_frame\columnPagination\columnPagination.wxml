<block wx:if="{{unitData.list&&unitData.list.length}}">
  <view wx:if="{{!isTop}}" style="position:relative;top: -2px;">
    <observer options="{{observerOpt}}" start bindnov="nov" bindeach="each"></observer>
  </view>
  <tabs wx:if="{{!config.isTop||isTop}}" list="{{unitData.list}}" key="title" index="{{currentTab}}" activeStyle=";color:{{config.activeFontColor||'#303030'}};font-weight:700;font-size:{{config.activeFontSize}}rpx;" tabStyle="font-size:28rpx;color:{{tColor==='#ffffff'?'#f0f0f0':'#666'}};flex:unset;{{config.tabCenter ? 'flex:1;' : ''}};" style2="height:{{config.h||80}}rpx;" sticky="{{isTop?false:true}}" top="{{menuButton.t}}px" bg="{{config.bg|| (showBg?'#fff':'transparent')}};transition:all 0.05s;" styles="z-index:666;padding:0 {{config.mLR}}rpx;border-radius:{{config.radius}}rpx {{config.radius}}rpx 0 0;margin-bottom:{{config.mB}}rpx;margin-top:{{config.mT}}rpx;" duration="0" bind:change="handleTabClick" color="{{config.lineColor}}" tapChange="{{false}}">
    <img wx:if="{{config.lineImg}}" class="line-img" slot="line" src="{{config.lineImg}}" />
  </tabs>
</block>
