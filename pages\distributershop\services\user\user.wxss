page {
   background: #f3f3f3;
 }

.hover {
  background: #f9f9f9 !important;
}

 .member-item {
   padding: 0 30rpx;
   display: flex;
   align-items: center;
   height: 160rpx;
   position: relative;
   background: #fff;
   border-bottom: 2rpx solid #eee;
 }


 .member-item .member-img {
   background: #eee;
   height: 100rpx;
   width: 100rpx;
   border-radius: 50%;
   box-shadow: 0 0 6rpx #ddd;
 }

 .member-item .uni-icon-arrowright {
   font-size: 42rpx;
   color: #999;
 }


 .member-item .member-content {
   flex: 1;
   height: 100%;
   display: flex;
   flex-direction: column;
   padding: 16rpx 30rpx;
   justify-content: space-evenly;
   font-size: 30rpx;
 }

 .member-item .member-content .member-nick {
   display: flex;
   align-items: center;
   flex-wrap: wrap;
 }

 .member-item .member-content .member-nick .tag1 {
   padding: 2rpx 8rpx;
   color: #ff9e40;
   border: 2rpx solid #ff9e40;
   border-radius: 20rpx;
   font-size: 22rpx;
 }

 .member-item .member-content .member-other {
   font-size: 26rpx;
   color: #555;
 }

.uni-icon-info{
	color: #777;
	font-size: 34rpx;
	margin-left: 2rpx;
	padding: 20rpx;
	margin-top: -10rpx;
	margin-bottom: -20rpx;
	margin-left: -6rpx;
	margin-right: -42rpx;
}

 .consume-list {
   display: flex;
   flex-direction: column;
   background: #fff;
   padding: 4rpx 0;
	 color: #333;
 }

 .consume-item {
   display: flex;
   align-items: center;
   font-size: 30rpx;
 }

 .consume-one {
   display: flex;
   justify-content: center;
   padding: 4rpx 0;
	 align-items: center;
 }

.tags-container {
  margin-top: 10rpx;
  padding: 10rpx 30rpx;
  padding-right: 0;
  background: #fff;
}

.tags-container .title{
  font-size: 30rpx;
  color: #333;
  font-weight: 700;
  padding: 10rpx 0 20rpx;
}

.tags-item{
  display: flex;
  flex-wrap: wrap;
}

.tags-item view{
  margin-right: 20rpx;
  margin-bottom: 5px;
  font-size: 13px;
  padding: 0 16rpx;
  border-radius: 16rpx;
  color: #555;
  border: 2rpx solid #bbb; 
  line-height: 20px;
  height: 20px;
}

 .btn-box {
   width: 750rpx;
   min-height: 150rpx;
   display: flex;
   align-items: center;
   flex-wrap: wrap;
   padding: 30rpx 0 0 30rpx;
 }

 .btn-box view {
   width: 330rpx;
   height: 120rpx;
   background: #fff;
   line-height: 120rpx;
   text-align: center;
   border: 2rpx solid #ddd;
   border-radius: 10rpx;
   box-shadow: 0 0 4rpx #ddd;
   margin-bottom: 16rpx;
   margin-right: 30rpx;
 }

.behavior-top{
  display: flex;
  background: #fff;
  height: 80rpx;border-bottom: 2rpx solid #ddd;
  position: sticky;
  top: 0;
  left: 0;
  z-index: 100;
}

.behavior-top view{
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  font-size: 30rpx;
}

.behavior-box{
  display: flex;
  background: #fff;
}

.behavior-box .time{
  padding:20rpx 0;
  padding-top: 46rpx;
  font-size: 26rpx;
  width: 172rpx;
  text-align: center;
}

.behavior-box .behavior-item{
  padding: 20rpx;
  border-left: 2rpx solid #ddd;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.item-title{
  position: relative;
  font-size: 28rpx;
}

.item-title .title-dot{
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #ffb8b5;
  top: 50%;
  margin-top: -8rpx;
  left: -30rpx;
  border-radius: 50%;
}

.item-content{
  font-size: 26rpx;
  color: #777;
}

.item-content .img-list{
  display: flex;
  flex-wrap: wrap;
}

.item-content .img-list image{
  background: #eee;
  width: 128rpx;
  height: 128rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
	position: relative;
}

.item-content .img-list image text{
  position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	font-size: 32rpx;
	color: #000;
	text-align: center;
	background: linear-gradient(180deg, rgba(255,255,255,0), rgba(255,255,255,0.5));
}

.item-content .img-list image:nth-of-type(4n+4){
  margin-right: 0;
}

.item-content .coupon{
  width: 100%;
  background: #c7ffea;
  border-radius: 12rpx;
  color: #777;
  height: 160rpx;
  display: flex;
  margin-bottom: 10rpx;
}

.item-content .coupon .value{
  font-size: 60rpx;
  font-weight: 900;
  display: flex;
  height: 100%;
  padding: 0 20rpx;
  align-items: center;
}

.item-content .coupon .info{
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  padding: 20rpx 0;
  padding-right: 20rpx;
}