var changeGooItem = function(ins, ownerInstance) {
  // event.instance 来表示触发事件的组件的 ComponentDescriptor 实例。ownerInstance 表示的是触发事件的组件所在的组件的 ComponentDescriptor 实例，如果触发事件的组件是在页面内的，ownerInstance 表示的是页面实例。
  var st = ins.getState()
  var num = st.num
  var margin = st.margin
  console.log(111, margin);
  if (st.cnum == num && st.cmargin) return // 节流
  st.cnum = num
  st.cmargin = margin
  ins.removeClass('nleft2')
  ins.removeClass('nleft3')
  ins.removeClass('nleft4')
  ins.addClass('nleft' + num)
  ins.setStyle({
    'width': 'calc(100% / ' + num + ' - ' + Math.ceil(margin * (num - 1) / num + 1.1) + 'rpx)',
    'margin-left': margin + 'rpx'
  })
  var style = ins.getBoundingClientRect()
  var img = ownerInstance.selectComponent('.img')
  var height = 'calc(' + style.width + 'px' + '*' + st.h + ')'
  img.setStyle({
    'width': '100%',
    'height': height
  })
}

var numChange = function(newVal, oldVal, ownerInstance, ins) {
  if (newVal == 0) return
  var st = ins.getState()
  st.num = newVal
  if (st.h) changeGooItem(ins, ownerInstance)
}

var hChange = function(newVal, oldVal, ownerInstance, ins) {
  if (newVal == 0) return
  var st = ins.getState()
  st.h = newVal
  if (st.margin) changeGooItem(ins, ownerInstance)
}

var marginChange = function(newVal, oldVal, ownerInstance, ins) {
  // if (newVal == 0) return
  var st = ins.getState()
  st.margin = newVal
  if (st.num) changeGooItem(ins, ownerInstance)
}

var themeChange = function(newVal, oldVal, ownerInstance, ins) {
  console.log('themeChange');
  if (newVal != 1 && newVal != 3) return
  var st = ins.getState()
  st.cnum = ''
  st.cmargin = ''
  ins.removeClass('nleft2')
  ins.removeClass('nleft3')
  ins.removeClass('nleft4')
  ins.setStyle({})
  var img = ownerInstance.selectComponent('.img')
  img.setStyle({})
}

module.exports = {
  numChange: numChange,
  marginChange: marginChange,
  themeChange: themeChange,
  hChange: hChange
}
