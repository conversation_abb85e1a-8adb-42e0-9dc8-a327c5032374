<!--components/common/share.wxml-->
<uni-popup show="{{ isShowAlert }}" zIndex="999" type="bottom" bind:close="close" closeName="生成分享图" duration="280" styles="{{styles}}">
  <view class="alert">
    <view class="flex-center top">
      <view class="flex-center item" bindtap="pengyouquan" hover-class="hover" hover-start-time="20" hover-stay-time="100">
        <view class="flex-center share-icon uni-icon uni-icon-images" style="background: #4BD847;color: #fff;font-size: 56rpx;"></view>
        <view class="flex-center share-ways">生成分享图</view>
      </view>
      <button class="item" data-id="shareBtn" open-type="share" hover-class="hover" hover-start-time="20" hover-stay-time="100" bindtap="weixin">
        <view class="flex-center share-icon" style="background: #9CDF2F"><text style="font-size: 56rpx;" class="f7 iconDG-weixin"></text></view>
        <view class="flex-center share-ways">发送给朋友</view>
      </button>
    </view>
    <!-- <view class="flex-center bottom" bindtap="close">取消</view> -->
  </view>
  <slot></slot>
</uni-popup>
