// components/distributor/orderby/orderby.js
Component({
  options: {
    addGlobalClass: true
  },
  /**
   * 组件的属性列表
   * event_name: 点击事件名称
   * name: 排序展示标题
   * type: 0 -> 默认值,升降序
   *       1 -> 分类排序。需绑定对应的分类数组
   * groups: [{ event_name: '', name: '分类', type: 0, type_array}, ...]
   */
  properties: {
    groups: Array
  },

  /**
   * 组件的初始数据
   */
  data: {
    // groups:[]
    current: {},
    checkedBranch: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    checkBranch: function(e){
      let that = this
      let branch = e.currentTarget.dataset.item
      let item = that.data.current
      that.data.groups.map(el => {
        if (el.name == item.name) {
          el.order = el.order != 1 ? 1 : 0
        }
      })
      that.setData({
        current: that.data.groups.filter(el => el.name === item.name)[0],
        groups: that.data.groups
      })
      // console.log(branch, that.data.current)
      that.triggerEvent('orderBy', {
        all: that.data.groups,
        checkItem: branch,
        prop: that.data.current['prop'],
        item
      })
    },
    changeOrder: function(e){
      let that = this
      let item = e.currentTarget.dataset.item
      that.data.groups.map(el => {
        if(el.name == item.name){
          el.order = el.order != 1 ? 1 : 0
        }else{
          // 如果非0类型的重置 order 为 0
          // el.type == 1 && (el.order = 0)
          el.order = 0
        }
      })
      that.setData({
        current: that.data.groups.filter(el => el.name === item.name)[0],
        groups: that.data.groups
      })
      if(that.data.current.type === 1){
        return
      }
      that.triggerEvent('orderBy', {
        all: that.data.groups,
        checkItem: {
          classId: ''
        },
        prop: 'classId',
        item
      })
    },
    reset(){
      for(let i in this.data.groups){
        this.data.groups[i].order = 0
      }
      this.setData({
        groups: this.data.groups
      })
    }
  }
})
