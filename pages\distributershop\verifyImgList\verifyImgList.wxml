<wxs src="../../../utils/utils.wxs" module="utils" />
<tabs list="{{viewList}}" key="title" index="{{tab}}" bindchange="changeTab" color="#E60012" tabStyle="color:#909399;min-width:170rpx;flex:1" styles="width: 750rpx;background:#f6f6f6;z-index:1;" sticky></tabs>
<view class="item-box" wx:for="{{viewList[tab].data}}" wx:key="index" bindtap="toDetail" data-id="{{item.goodsSn}}">

  <image wx:if="{{item.bImg}}" src="{{utils.jpg2jpeg(item.bImg)}}"></image>
  <view>
    <view style="line-height: 1.2;">{{item.goodsName}}</view>
    <view>{{item.goodsSn}}</view>
    <view>{{item.price|| ''}}</view>
  </view>
  <view wx:if="{{tab==0}}" class="right">
    <view>提交人：{{item.creatorId}}</view>
    <view>提交时间：{{item.submitTime&&utils.split(item.submitTime,' ')[0]}}</view>
  </view>
  <view wx:else class="right">
    <view class="status {{item.isPass==2?'success':'fail'}}">{{item.isPass==2?'已通过':'未通过'}}</view>
    <view>提交人：{{item.creatorId}}</view>
    <view>审核时间：{{item.auditTime&&utils.split(item.auditTime,' ')[0]}}</view>
  </view>
</view>

<view wx:if="{{!loading &&viewList[tab].has_more != 4}}" class="show_end" bindtap="getList">
  <view wx:if="{{viewList[tab].has_more == 1}}" class="loadmore-icon"></view>
  <text>{{ loadingText[viewList[tab].has_more] }}</text>
</view>
<sf-loading wx:if="{{loading}}" full></sf-loading>
