import Alert from '../../../components/alert/index.js'
const app = getApp()
Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    shopCouponList: {
      type: Array,
      value: []
    },
    shopCouponGetList: {
      type: Array,
      value: []
    },
    type: {
      type: String,
      value: 'count'
    },
    shopId: {
      type: String,
      value: ''
    }
    // callbackGet',
  },
  data: {
    tabId2: 1,
    qmSF: '',
    fblist: []
  },
  async attached() {
    if (this.properties.type == 'cart') {
      this.data.tabId2 = 0
      this.getCoupon()
      this.setData({
        cartCoupons: this.data.shopCouponGetList
      })
      this.cartGetFubiList()
    } else if (this.properties.type == 'count') {
      this.data.tabId2 = 1
      this.countGetFubiList()
    }
    this.setData({
      tabId2: this.data.tabId2,
      fblist: this.data.fblist
    })
    const limitShop = await app.util.checkLimitShop()
    this.setData({
      limitShop
    })
  },
  detached() {},
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: async function () {
      const limitShop = await app.util.checkLimitShop()
      this.setData({
        limitShop
      })
    },
    hide: function () {},
    resize: function () {}
  },
  methods: {
    getCoupon(e) {
      app.reqGet(
        'ms-sanfu-wap-cart/listShopCoupon',
        {
          shoId: wx.getStorageSync('dsho_id'),
          sid: wx.getStorageSync('sid')
        },
        res => {
          if (res.success) {
            this.setData({
              cartCoupons: res.data
            })
          }
        }
      )
    },
    changeTabId2(e) {
      let id = e.currentTarget.dataset.id
      this.setData({
        tabId2: id
      })
    },
    close() {
      this.triggerEvent('close')
    },
    callbackUse(e) {
      let code = e.currentTarget.dataset.code
      let checked = e.currentTarget.dataset.checked
      this.triggerEvent('callbackUse', {
        code: code,
        checked: checked
      })
    },
    callbackGet(e) {
      let id = e.currentTarget.dataset.id
      this.triggerEvent('callbackGet', id)
    },
    confirm() {
      app.$bus.emit('countSelCoupon', 0)
    },
    toH5(e) {
      let url = e.currentTarget.dataset.url
      app.toH5(url)
    },
    alert(e) {
      let msg = e.currentTarget.dataset.msg
      wx.showModal({
        content: msg,
        showCancel: false
      })
    },
    getFubi() {
      app.reqGet(
        'ms-sanfu-wap-customer/getPoint',
        {
          sid: wx.getStorageSync('sid')
        },
        res => {
          if (res.success) {
            this.setData({
              total: res.data.restMoney,
              fbhistoryPoint: res.data.historyPoint,
              fbnextYear: res.data.nextYear
            })
          }
          // console.log(el, '我的福币')
        }
      )
    },
    cartGetFubiList() {
      app.reqGet(
        'ms-sanfu-wap-cart/listCartCouponByGift',
        {
          sid: wx.getStorageSync('sid'),
          shoId: wx.getStorageSync('dsho_id')
        },
        res => {
          if (res.success && res.data) {
            this.setData({
              fblist: res.data
            })
            if (res.data.length > 0) {
              this.getFubi()
            }
          }
          // console.log(el, '我的福币')
        }
      )
    },
    countGetFubiList() {
      //https: //tm.sanfu.com/ms-sanfu-wap-cart/listCartCouponByGift?sid=33e3957db9d5491fb5873d8a5ca8a28a&shoId=208
      app.reqGet(
        'ms-sanfu-wap-cart/listAccountCouponByGift',
        {
          sid: wx.getStorageSync('sid'),
          buyType: 0,
          shoId: wx.getStorageSync('dsho_id')
        },
        res => {
          if (res.success && res.data) {
            this.setData({
              fblist: res.data
            })
            if (res.data.length > 0) {
              this.getFubi()
            }
          }
          // console.log(el, '我的福币')
        }
      )
    },
    exchange(e) {
      let item = this.data.fblist[e.currentTarget.dataset.index]
      if (item.isHave == 1) {
        app.toH5(item.goodsUrl)
        return
      }
      wx.showModal({
        title: '提示',
        content: '您即将花费' + item.giftprice + '福币进行兑换（兑换结果以实际返回为准））',
        success: r => {
          if (r.confirm) {
            wx.showToast({
              title: '',
              icon: 'loading',
              duration: 9999999,
              mask: true
            })
            app.reqPost(
              'ms-sanfu-wechat-coupon/coupon/usePointExchangeCoupon',
              {
                sid: wx.getStorageSync('sid'),
                giftId: item.giftId,
                shoId: wx.getStorageSync('dsho_id') || 'GZW'
              },
              res => {
                wx.hideToast()
                if (res.success) {
                  if (res.data === 'completeMsg') {
                    wx.showModal({
                      title: '温馨提示',
                      content: '请完善会员信息，以便参与更多活动',
                      success: res => {
                        if (res.confirm) {
                          app.sf.track('click_complete_member_info')
                          app.toH5('wechat/user/showUseredit.htm?type=1')
                        }
                      }
                    })
                    return
                  }
                  wx.showModal({
                    title: '兑换成功',
                    content: '',
                    showCancel: false
                  })
                  if (this.properties.type == 'count') {
                    let page = getCurrentPages()
                    page = page[page.length - 1]
                    typeof page.getCountList == 'function' && page.getCountList()
                  }
                  // if (res.data)
                  //   app.subscribeMsg(2001, res.data)
                } else {
                  wx.showToast({
                    title: res.msg,
                    icon: 'none'
                  })
                }
              }
            )
          }
        }
      })
    },
    toRule(e) {
      let item = e.currentTarget.dataset.item
      item = item.replace(/\<br\/\>/g, '\n')
      if (this.data.limitShop) {
        item = item
          .split('\n')
          .filter(line => !line.includes('店'))
          .join('\n')
      }
      wx.showModal({
        content: item,
        showCancel: false
      })
    }
  }
})
