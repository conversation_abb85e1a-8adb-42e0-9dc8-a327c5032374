/* pages/auth/auth.wxss */

@-webkit-keyframes tipMove1 {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes tipMove {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.alet_container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999999;
  background-color: rgba(0, 0, 0, 0.15);
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 112rpx;
  box-sizing: border-box;
}

.tip_text_container {
  width: 600rpx;
  animation: tipMove 0.5s;
  -webkit-animation: tipMove1 0.5s;
  background-color: white;
  border: 2rpx;
  padding: 40rpx 20rpx 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border-radius: 16rpx;
  box-sizing: border-box;
}

.tip_text {
  font-size: 36rpx;
  color: #333;
  line-height: 36rpx;
  text-align: center;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  padding: 30rpx 20rpx;
  line-height: 1.25;
}

.tip_text text {
  font-size: 36rpx;
}

.btn-box {
  font-size: 30rpx;
  width: 100%;
  text-align: center;
  border: 2rpx;
  white-space: nowrap;
  text-overflow: clip;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.button {
  border: 0 solid !important;
  padding: 0;
  border-radius: 0px;
  margin: 0;
  line-height: 1.2;
  box-sizing: border-box;
}

.left {
  font-size: 32rpx;
  min-width: 200rpx;
  border: 2rpx solid #bbb;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  color: #666;
  margin-right: 36rpx;
  box-sizing: border-box;
}

.right {
  font-size: 32rpx;
  min-width: 200rpx;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  color: white;
  box-shadow: 0 0 2rpx #f02e72;
  background: linear-gradient(to right, #fd6c90, #f94980, #f02e72);
  box-sizing: border-box;
}
