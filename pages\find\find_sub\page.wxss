@import "main.wxss";
@import "iconfont.wxss";
@import "whole.wxss";

page {
  /* background-color: white; */
  background-color: #f2f2f2;
}

.cu-bar .search-form {
  background-color: #f7f7f7;
  justify-content: center;
}

.cu-bar .search-form [class*="icon"] {
  margin: 0 15rpx;
}

.cu-bar .search-form>i,
.cu-bar .search-form>text {
  color: #a5a5a5;
  font-size: 26rpx;
}

/* 导航栏 */
.bg-f8f8f8 {
  background: #F02E72 !important;
  color: #ffffff;
}

.tabs {
  background: #F02E72;
  position: relative;
  line-height: 54rpx;
  overflow: hidden;
}

.head-left {
  padding: 10rpx 12rpx;
  color: #ffffff;
  position: absolute;
  left: 15rpx;
  line-height: 36rpx;
}

.head-left image {
  width: 48rpx;
  height: 48rpx;
}

.head-middle {
  display: flex;
  color: #ffe0eb;
  margin: auto;
}

.middle1 {
  font-size: 30rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  flex: 1;
  margin-left: 58rpx;
  /* padding-bottom: 15rpx; */
}

.middle0 {
  font-size: 30rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  flex: 1;
  margin-left: 0rpx;
  /* padding-bottom: 15rpx; */
}

.head-right {
  position: absolute;
  right: 40rpx;
  color: #ffe0eb;

}

.act {
  color: #ffffff;
  /* border-bottom: 4rpx solid #ffffff; */

}

.middle1-fot {
  width: 40rpx;
  height: 6rpx;
  background: #ffffff;
  text-align: center;
  margin: auto;
}

.middle1-top {
  padding-bottom: 8rpx;
}

.bg-image {
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: -1;
}

.main1 {
  background: #ffffff;
  padding: 10rpx 45rpx 10rpx 25rpx;
  border-radius: 30rpx;
  font-size: 13px;
  font-family: PingFangSC;
  font-weight: 400;
  color: #F02E72;
}

.cu-bar2 {
  display: flex;
  justify-content: center;
  margin-top: 26rpx;
  position: absolute;
  top: 0;
  left: 37%;
  right: 37%;
}

.cu-list {
  position: relative;
  display: block;
  width: 100%;
}

/* 图片的轮播 */
.imagelist {
  height: 75vh;
}

/* 外层上下切换 */
.v-image-video {
  position: relative;
}

/* 右侧数据 */
.menu {
  position: fixed;
  right: 40rpx;
  bottom: 25%;
  z-index: 10;
}

.menu1 {
  margin-top: 20rpx;
}

.menu1-img image {
  width: 80rpx;
  height: 80rpx
}

.menu1-text {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 500;
  color: #FFFFFF;
  text-align: center;
}

.main2 {
  margin-top: 25rpx;
  z-index: 199;
}

.main2 image {
  width: 80rpx;
  height: 80rpx;
  z-index: 199;
}

.cu-item.arrow:before {
  position: absolute;
  top: 0;
  right: 30rpx;
  bottom: 0;
  display: block;
  margin: auto;
  width: 30rpx;
  height: 30rpx;
  /* color: var(--grey); */
  color: #F02E72;
  content: "\e6a3";
  text-align: center;
  font-size: 34rpx;
  font-family: "cuIcon";
  line-height: 30rpx
}

.fot {
  position: fixed;
  bottom: 0%;
  left: 0rpx;
  z-index: 9;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.45));
}

.fot1 {
  display: inline-flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.cu-img {
  width: 76rpx;
  height: 76rpx;
  margin-left: 30rpx;
}

.con-text {
  font-size: 30rpx;
  font-family: PingFangSC;
  font-weight: 500;
  color: #FFFFFF;
  margin-left: 25rpx;
}

.fot2 {
  padding-left: 30rpx;
  padding-right: 30rpx;
  width: 750rpx;
  padding-top: 14rpx;
  padding-bottom: 80rpx;
  position: relative;
}

.fot3 {
  background-color: #ffffff;
}

.fot2-1 {
  font-size: 32rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #FFFFFF;
}

.fot2-2 {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 46rpx;

}

.text-dent {
  text-indent: 50rpx;
}

.fot2-3 {
  width: 70rpx;
  height: 36rpx;
  background: #1B1B1B;
  opacity: 0.4;
  border-radius: 10rpx;
  color: #ffffff;
  text-align: center;
  position: absolute;
  bottom: 84rpx;
  right: 20rpx;
}

/* 二层tabs */
.main-box {
  display: flex;
  justify-content: center;
  margin-top: 26rpx;
  position: absolute;
  top: 0;
  width: 100%;
}

.main1-f {
  padding-left: 30rpx;
  padding-right: 30rpx;
  color: #e9e9e9;
  font-size: 30rpx;
}

.main1-f2 {
  color: #888888;
  padding-left: 30rpx;
  padding-right: 30rpx;
  font-size: 30rpx;
}

.act2 {
  color: #ffffff;
  font-family: PingFangSC;
  font-weight: 600;
}

.act3 {
  color: #F32871;
  font-family: PingFangSC;
  font-weight: 600;
  font-size: 30rpx;
}

.main1-f-img {
  position: absolute;
  top: 0;
  right: 30rpx;
  margin-top: 26rpx;
}

.main1-f-img image {
  width: 36rpx;
  height: 36rpx;
}

.main1-f-img2 {
  position: absolute;
  top: 25rpx;
  right: 30rpx;
  z-index: 99;
  /* margin-top: 26rpx; */
}

.main1-f-img2 image {
  width: 48rpx;
  height: 48rpx;
  position: fixed;
  right: 30rpx;
}

.fot-top {
  margin-left: 40rpx;

  display: inline-flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.f-top1 {
  padding: 8rpx 16rpx 8rpx 16rpx;
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #FFFFFF;
  /* background: linear-gradient(-90deg, #F02E72 0%, #FD6C90 100%);/ */
  background: #3E3E3E;
  border: 1rpx solid rgba(175, 175, 175, 0.9);
  opacity: 0.7;
  border-radius: 26rpx;
}

.f-top2 {
  display: flex;
  align-items: center;
}

.f-top2-img {
  margin-left: 8rpx;
}

.f-top2-img image {
  width: 22rpx;
  height: 24rpx;
}

.f-top2-text {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #FFFFFF;
}

/* 展开弹框 */
.an {
  bottom: 100rpx !important;
}

.icon-t2 {
  font-size: 24rpx !important;
}

.cuIcon-close {
  position: absolute;
  top: 50rpx;
  right: 20rpx;
  z-index: 3;
}

.cuIcon-close2 {
  position: absolute;
  top: 22rpx;
  right: 20rpx;
  z-index: 3;
}

.con-text2 {
  font-size: 30rpx;
  font-family: PingFangSC;
  font-weight: 500;
  color: #353535;
}

.cu-dialog {
  padding-top: 10rpx;
  padding-bottom: 0rpx;
}

.cu-dialog2 {
  /* padding-bottom: 32rpx; */
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 750rpx;
  max-width: 100%;
  background-color: #f8f8f8;
  overflow: hidden;
}

/* 转发 */
.item1-img image {
  width: 85rpx;
  height: 76rpx;
}

.cu-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.item1 {
  flex: 1;
}

.item1-text {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #353535;
  margin-top: 13rpx;
}

/* 评论 */
.talk-header {
  width: 100%;
  height: 70rpx;
  padding-top: 30rpx;
  text-align: center;
}

/*回复*/
.input-fayan {
  height: 100rpx;
  position: fixed;
  bottom: 0rpx;
  z-index: 9999;
  width: 100%;
  background-color: #f8f8f8;
}

.input-fayan input {
  border-radius: 40rpx;
  background: #FFFFFF;
  font-size: 30rpx;
  margin-left: 20rpx;
  width: 620rpx;
  height: 70rpx;
  text-align: left;
  text-indent: 1em;
}

.input-fayan input:focus {
  text-indent: 1em;
}

.input-fayan button {
  border: solid #FFFFFF 0rpx;
  /* background: none; */
  width: 135rpx;
  height: 72rpx;
  line-height: 72rpx;
  color: #ffffff;
  background: linear-gradient(270deg, #F02E72 0%, #FD6C90 100%);
  border-radius: 36rpx;
  font-size: 34rpx;
  font-family: 'PingFangSC-Medium';
  /* color: var(--theme_color_pink); */
  margin: 0 auto;
  margin-left: -46rpx;
}

/* 顶部 */
.talk-count {
  font-size: 14px;
  height: 40rpx;
  color: #6b696a;
}

.talk-close {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 22rpx;
  height: 22rpx;
}

.talk-close2 {
  position: absolute;
  top: 10rpx;
  right: 40rpx;
  width: 62rpx;
  height: 62rpx;
  padding: 20rpx 20rpx;
}

/* 评论体 */
.talk-body {
  max-height: 700rpx;
  margin-top: 30rpx;
}

/* 中部元素  */
.talk-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 100%;
  color: #353535;
  border-bottom: 1rpx solid #EEEEEE;
  padding-bottom: 34rpx;
}

.talk-item-left {
  display: flex;
  flex-direction: row;
  margin: 0rpx 23rpx;
}

.talk-item-face {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

/* 右边文字 */
.talk-item-nickname {
  font-size: 24rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #888888;
}

.talk-item-right {
  text-align: left;
  width: 64%;
}

.talk-item-content {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #353535;
}

.talk-right2 {
  position: absolute;
  right: 30rpx;
}

.right2-image {
  display: flex;
  justify-content: center;
}

.right2-image image {
  width: 31rpx;
  height: 29rpx;
}

.right2-text {
  /* margin-top: 10rpx; */
  font-size: 22rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #888888;
  margin-top: 7rpx;
}

/* 回复 */
.talk-h {
  margin-top: 24rpx;
  display: flex;

}

.h-left image {
  width: 40rpx;
  height: 40rpx;
  /* background: #F02E72; */
  border-radius: 50%;
}

.h-right {
  text-align: left;
  width: 92%;
  margin-left: 14rpx;
}

.video-logo-more {
  width: 21rpx;
  height: 14rpx;
}

.talk-item-text {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 500;
  color: #353535;
  margin-top: 18rpx;
}

/* 列表数据 */
.main-box {
  display: flex;
  justify-content: center;
  margin-top: 26rpx;
  width: 100%;
}

.main-box2 {
  display: flex;
  justify-content: center;
  /* margin-top: 26rpx; */
  width: 100%;
}

.list-box {
  margin-top: 26rpx;
  margin-bottom: 16rpx;
}

.img-con {
  width: 330rpx;
  height: 330rpx;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  border-radius: 12rpx 12rpx 0 0;
  position: relative;
}

.img-con .playicon {
  width: 80rpx !important;
  height: 80rpx !important;
  position: absolute;
  z-index: 1;
  left: 30rpx;
  top: 220rpx;
}

/* components/news/newslist.wxss */
.card-pbl-header {
  width: 100%;
  height: 330rpx;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}


.card-pbl-son {
  position: relative;
  width: 375rpx;
}

.card-pbl-son .playicon {
  width: 80rpx !important;
  height: 80rpx !important;
  position: absolute;
  z-index: 1;
  left: 30rpx;
  top: 220rpx;
}

.w50 {
  width: 50%;
  float: left;
  display: block;
  margin: 0rpx 0rpx;
  padding: 0rpx 0rpx;
  overflow: hidden;
}




/* 4:3 */
.card-pbl-header.size2 {
  height: 480rpx;
}

/* 3:4 */
.card-pbl-header.size1 {
  height: 270rpx;
}

/* 1:1 */
.card-pbl-header.size0 {
  height: 360rpx;
}

/* 分享 */
.bun_dl {
  padding: 0rpx 0rpx;
  margin-top: 56rpx;
  text-align: center;
  background-color: #ffffff;
}

.bun_dl .dt {
  width: 375rpx;
  text-align: center;
}

.bun_dl .dt image {
  width: 98rpx;
  height: 98rpx;
  text-align: center;
}

.bun_dl .dt .span {
  font-size: 30rpx;
  background-color: #ffffff;
  margin-top: 0rpx;
  line-height: 60rpx;
}

/* .dt-img{width: 90rpx !important;height: 90rpx !important;} */
.bun_dl .ff image {
  width: 54rpx;
  height: 54rpx;
  text-align: center;
}

/* .bun_dl .ff{margin-top: 30rpx;} */
.dt-img {
  padding-bottom: 7rpx;
}

.cleb {
  display: flex;
  align-items: center;
}

/* 购买 */
.cu-shop {
  display: flex;

  padding-left: 30rpx;
  padding-right: 30rpx;
}

.shop-left image {
  width: 212rpx;
  height: 288rpx;
}

.shop-right {
  margin-left: 38rpx;
  width: 442rpx;
  position: relative;
}

.shop-right1 {
  font-size: 30rpx;
  font-family: PingFangSC;
  font-weight: 500;
  color: #353535;
  text-align: left;
}

.shop-right2 {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #888888;
  margin-top: 16rpx;
}

.shop-right3 {
  display: flex;
  align-items: flex-start;
  margin-top: 16rpx;
  width: 100%;
  white-space: Nowrap;
  text-align: left;
}

.shop-right3-1 {
  display: inline-block;
}

.shop-right3-1 image {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  margin-right: 28rpx;
}

.shop-right4 {
  margin-top: 4rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.shop-right4-1 {
  font-size: 30rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #F02E72;
}

.shop-right4-2 {
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  top: 12rpx;
}

.right4-image image {
  width: 56rpx;
  height: 56rpx;
}

.shop-right5 {
  margin-top: 24rpx;
}

.right5-text {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #353535;
}

.icon-t {
  margin-left: 29rpx;
  font-size: 12px !important;
}

/* 购物车 */
.cu-shop {
  margin-bottom: 33rpx;
}

.shop-left2 {
  margin-top: -30rpx;
  margin-top: -30rpx;
  position: fixed;
}

.shop-left2 image {
  width: 200rpx;
  height: 200rpx;
}

.shop-r {
  margin-left: 33%;
  margin-top: 26rpx;
  text-align: left;
}

.r1 {
  font-size: 24rpx;
  font-family: PingFangSC;
  font-weight: 500;
  color: #F32871;
}

.r2 {
  font-size: 22rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #888888;
  text-decoration: line-through;
  margin-left: 10rpx;
}

.r3 {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #888888;
  margin-top: 34rpx;
}

.cu-shop2 {
  padding-left: 30rpx;
  padding-right: 30rpx;
  text-align: left;
}

.shop2-top {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #353535;
}

.shop2-middle {
  margin-top: 22rpx;

}

.mid1-img {
  width: 155rpx;
  height: 155rpx;
  background: #E4E4E4;
  border-radius: 8rpx;
}

.mid1-img image {
  width: 100%;
  height: 100%;
}

.mid1-text {
  width: 155rpx;
  height: 38rpx;
  line-height: 38rpx;
  background: #ffffff;
  text-align: center;
}

.shop2-mid {
  padding-top: 42rpx;
  padding-bottom: 37rpx;
  border-bottom: 1rpx solid #EDEDED;
  display: flex;
  align-items: center;
  position: relative;
}

.m1 {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #353535;
}

.m2 {
  margin-left: 4rpx;
}

.m3 {
  display: flex;
  align-items: center;
  margin-left: 57rpx;
}

.m3-left {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #888888;
}

.m3-right {
  padding-left: 15rpx;
  padding-right: 15rpx;
  text-align: center;
  border: 1px solid #F02E72;
  border-radius: 10rpx;
  color: #F02E72;
  margin-left: 10rpx;
}

.cu-shop3 {
  width: 100%;
  height: 94rpx;
  line-height: 94rpx;
  text-align: center;
  color: #ffffff;
  background: #FF6A8F;
}

.action-btn-sum {
  position: absolute;
  right: 0px;
  top: 17px;
  text-align: right;
  border: 1px solid #D9D9D9;
  border-radius: 2px;
}

.cu-btn {
  float: left;
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 0px;
  font-size: 18px;
  padding: 0px;
  background-color: #F7F6F6;
}

.ca-sum {
  float: left;
  width: 32px;
  height: 24px;
  text-align: center;
  font-size: 12px;
  line-height: 24px;
  background-color: #fbfbfb;
  border-left: 1px solid #D9D9D9;
  border-right: 1px solid #D9D9D9;
  min-height: 24px;
}

.size {
  margin-top: 48rpx;
}

.right5-fot {
  font-size: 26rpx;
  font-family: PingFangSC;
  font-weight: 400;
  color: #353535;
  text-align: left;
}

.coloract {
  border: 1rpx solid #FF6A8F;
}

.cu-modal.bottom-modal.show {
  /* margin-bottom: 16px !important; */
}

/* 视频 */
.cu-list-video video {
  width: 100%;
}

.feedvideo {
  height: 360rpx !important;
}

/* 冒泡悬浮按钮 */
.btn_Suspension {
  position: fixed;
  height: 100rpx;
  /* width: 100rpx; */
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  /* box-shadow: 1px 0px 1px 1px #ede7e7; */
}

.c-topic-css {
  display: inline-block;
  color: #ffffff;
  font-weight: bold;
  font-size: 30rpx;
  margin-right: 10rpx;
}

.userInfo {
  position: absolute;
  display: inline-block;
  z-index: 9999;
  color: #dc0000;
  font-size: 32rpx;
  font-weight: bold;
  top: 70%;
  left: 100rpx;
}

.noClass {
  display: none;
}


@keyframes scale-up-top {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 50% 0%;
    transform-origin: 50% 0%;
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 50% 0%;
    transform-origin: 50% 0%;
  }
}

@keyframes scale-up-bottom {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
  }
}

/* 蒙版 */
.allMark {
  height: 100vh;
  width: 100%;
  /* background: red; */
  position: fixed;
  top: 0;
  z-index: 9999;
}

.con {
  width: 240rpx;
  height: 289rpx;
  /* background: #F0F0F0; */
  border: 5rpx solid #F0F0F0;
  box-shadow: 2rpx 6rpx 9rpx 0rpx rgba(35, 35, 35, 0.1);
  border-radius: 0rpx 12rpx 12rpx 0rpx;
}

.head {
  display: flex;
  align-items: center;
  padding: 20rpx;
  position: relative;
}

.head-img {
  display: flex;
  z-index: 10;
}

.head-img image {
  width: 42rpx;
  height: 42rpx;
}

.head-mid {
  /* background: #e5e5e5; */
  background: black;
  opacity: 0.5;
  color: #ffffff;
  font-size: 20rpx;
  padding: 7rpx 20rpx 7rpx 20rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  margin-left: -17rpx;
}

.head-right {
  display: flex;
  position: absolute;
  right: 31rpx;
}

.head-right image {
  width: 24rpx;
  height: 24rpx;
}

/* 关注 */
.text-gz {
  width: 100rpx;
  height: 50rpx;
  background: linear-gradient(-90deg, #F02E72 0%, #FD6C90 100%);
  border-radius: 25rpx;
  margin-left: 12rpx;
  color: #ffffff;
  font-size: 26rpx;
  text-align: center;
  line-height: 50rpx;
}

.authorization_view {
  width: 100%;
  display: flex;
  height: 100vh;
  z-index: 9999;
  position: fixed;
  left: 0rpx;
  top: 0rpx;
}

.newUser {
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0rpx;
  top: 0rpx;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 99;
}

.newUser .newUser_header {
  width: 350rpx;
  height: 350rpx;
  position: absolute;
  right: 50rpx;
  top: 180rpx;
}

.newUser .newUser_header image {
  width: 350rpx;
  height: 350rpx;
}

.newUser .newUser_top {
  width: 100%;
  text-align: center;
  line-height: 56rpx;
  color: #ffffff;
  position: relative;
}

.newUser .newUser_top image {
  width: 350rpx;
  height: 350rpx;
  position: absolute;
  left: 200rpx;
  top: 430rpx;
}

.newUser .newUser_left {
  margin-top: calc(50vh - 200rpx);
  width: 45%;
  height: 30vh;
  float: left;
  text-align: left;
  color: #ffffff;
  position: relative;
  padding-left: 20rpx;
}

.newUser .newUser_left image {
  width: 350rpx;
  height: 350rpx;
  position: absolute;
  left: 30rpx;
  top: 150rpx;
}

.newUser .newUser_right {
  margin-top: calc(50vh - 200rpx);
  float: right;
  width: 45%;
  height: 30vh;
  text-align: right;
  color: #ffffff;
  position: relative;
  padding-right: 20rpx;
}

.newUser .newUser_right image {
  width: 350rpx;
  height: 350rpx;
  position: absolute;
  right: 30rpx;
  top: 150rpx;
}

.newUser .newUser_bottom {
  width: 100%;
  float: left;
  text-align: center;
  vertical-align: bottom;
  line-height: 56rpx;
  color: #ffffff;
  position: fixed;
  bottom: 400rpx;
  padding-top: 150rpx;
}

.newUser .newUser_bottom image {
  width: 350rpx;
  height: 350rpx;
  position: absolute;
  left: 200rpx;
  top: 30rpx;
}

.newUser_foot {
  width: 350rpx;
  height: 350rpx;
  position: absolute;
  left: 200rpx;
  bottom: -80rpx;
}

.v-image-video swiper-item,
.v-image-video video {
  position: fixed !important;
}


.v-sign {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 999;
}

.v-sign>.v-item1 {
  position: absolute;
  left: 50%;
  top: 40%;
  z-index: 9;
  border: solid 4rpx rgba(255, 255, 255, 0.75);
  border-radius: 70rpx;
  height: 140rpx;
  width: 140rpx;
  margin-left: -70rpx;
}

.v-sign>.v-item1>.v-item2 {
  content: '';
  display: block;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 30rpx 0 30rpx 40rpx;
  border-color: transparent transparent transparent rgba(255, 255, 255, 0.75);
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -30rpx 0 0 -10rpx;
}