<view class="popup">
  <view class="popup-top">
    <view class="popup-title">
      选择优惠券
      <view class="title-fb" wx:if="{{total}}">当前福币：{{total}}</view>
      <text class="uni-icon uni-icon-closeempty" style="font-size: 40rpx;color:#474747;" bindtap="close"></text>
    </view>
    <view wx:if="{{total&&fbhistoryPoint}}" class="fb-tip">您有{{fbhistoryPoint}}福币将在{{fbnextYear}}过期，请尽快兑换使用</view>
  </view>
  <view class="tab">
    <view class="tab-item {{tabId2==0?'active':''}}" wx:if="{{type=='cart'}}" bindtap="changeTabId2" data-id="0">可领({{cartCoupons&&cartCoupons.length}})</view>
    <view class="tab-item {{tabId2==1?'active':''}}" wx:if="{{type=='count'}}" bindtap="changeTabId2" data-id="1">可用({{shopCouponList.length}})</view>
    <view class="tab-item {{tabId2==2?'active':''}}" wx:if="{{type=='count'}}" bindtap="changeTabId2" data-id="2">不可用({{shopCouponGetList.length}})</view>
  </view>
  <view class="description">
    <scroll-view scroll-y style="width: 100%;height: 100%;position: absolute;left: 0;top:0;bottom: 0;right: 0;">
      <view wx:if="{{type=='cart'&&tabId2==0}}" style="padding: 14rpx 20rpx;">
        <view wx:if="{{cartCoupons&&cartCoupons.length == 0&&fblist.length==0}}" style="padding:15rpx;">
          <view style="color:#ccc;text-align:center;padding:10rpx;">没有可领取的优惠券</view>
        </view>
        <view class="coupon" style="margin: 15px auto;" wx:for="{{cartCoupons}}" wx:key="index">
          <view class="left">
            <view class='money-content'> <text class="money">{{item.couponMoney}}</text>元</view>
            <view class="rule">满{{item.leastMoney}}元使用</view>
          </view>
          <view class="right">
            <view class="title">{{item.couponTypeName}}</view>
            <!-- <view class="use_way">使用渠道：{{item.use_method}}</view> -->
            <text class="use_way" style="font-size: 24rpx;" wx:if="{{item.validDay}}">领取后{{item.validDay}}天内有效</text>
            <text class="use_way" style="font-size: 24rpx;" wx:elif="{{item.couponEndDate}}">{{item.couponBeginDate}}至{{item.couponEndDate}}</text>
            <view class="valid">
              <view wx:if="{{item.couponMemo}}" style="text-decoration: underline;color: #666;padding: 10rpx 0;margin-bottom: -10rpx;" catchtap="toRule" data-item="{{item.couponMemo}}">查看规则</view>
              <view class='to-use' bindtap="callbackGet" data-id="{{item.couponTypeId}}">领取</view>
            </view>
          </view>
        </view>
        <block wx:if="{{fblist.length > 0}}">
          <view class="coupon coupon-fb" wx:for="{{fblist}}" wx:key="index" bindtap="exchange" data-index="{{index}}">
            <view class="left" style="background: linear-gradient(135deg, #ffd3bb, #f9ab77, #f38143);">
              <view class='money-content'> <text class="money">{{item.couponMoney}}</text>元</view>
              <view class="rule">满{{item.leastMoney}}元使用</view>
            </view>
            <view class="right">
              <view class="title">{{item.couponTypeName}}</view>
              <view class="valid">
                <!-- <view class="use_way" wx:if="{{item.use_method}}">使用渠道：{{item.use_method||''}}</view> -->
                <!-- <text style="font-size: 24rpx;">{{item.giftbegindate}}至{{item.giftenddate}}</text> -->
                <view wx:if="{{item.couponMemo}}" style="text-decoration: underline;color: #666;padding: 10rpx 0;margin-bottom: -10rpx;" catchtap="toRule" data-item="{{item.couponMemo}}">查看规则</view>
                <view class='to-use-fb' wx:if="{{item.isHave==0}}">{{item.giftprice}}福币兑换</view>
                <view class='to-use-fb' wx:else>去使用</view>
              </view>
            </view>
          </view>
        </block>
      </view>
      <block wx:if="{{type=='count'&&tabId2==1}}">
        <view wx:if="{{shopCouponList.length == 0&&fblist.length==0}}" style="padding:15rpx;">
          <view style="color:#ccc;text-align:center;padding:10rpx;">没有已领取的优惠券</view>
        </view>
        <view wx:if="{{shopCouponList.length > 0}}" style="padding: 14rpx 20rpx 0;  ">
          <view style="display: flex;align-items: center;justify-content: center;position: relative;" wx:for="{{shopCouponList}}" wx:key="index">
            <radio checked="{{item.checked}}" color="#E60012" style="transform: scale(0.9);" />
            <view bindtap="callbackUse" data-code="{{item.code}}" data-checked="{{item.checked}}" style="height: 100%;width: 200rpx;position: absolute;left: 0;z-index: 6;"></view>
            <view class="coupon">
              <view class="left" style="padding: 16px 5px;">
                <view class='money-content'> <text class="money">{{item.money}}</text>元</view>
                <view class="rule">满{{item.leastMoney}}元使用</view>
              </view>
              <view class="right">
                <view class="title"> {{item.name}}</view>
                <!-- <view class="use_way">使用渠道：{{item.use_method}}</view> -->
                <text style="font-size: 22rpx;color:#909399">有效期至{{item.validEnd}}</text>
                <view class="valid">
                  <view wx:if="{{item.memo}}" style="text-decoration: underline;color: #666;padding: 10rpx 0;margin-bottom: -10rpx;font-size: 26rpx;" catchtap="toRule" data-item="{{item.memo}}">查看规则</view>
                </view>
                <view wx:if="{{item.goods_url}}" bindtap="toH5" data-url="{{item.goods_url}}">
                  <text style="color: #f42770;font-size: 24rpx;">查看可用商品</text>
                </view>

              </view>
            </view>
          </view>
        </view>
        <view wx:if="{{fblist.length > 0}}" style="padding: 20rpx 30rpx 20rpx 80rpx;">
          <view class="coupon coupon-fb" wx:for="{{fblist}}" wx:key="index" bindtap="exchange" data-index="{{index}}" style="margin: 0 0 24rpx 0;width: 100%;">
            <view class="left" style="background: linear-gradient(135deg, #ffd3bb, #f9ab77, #f38143);">
              <view class='money-content'> <text class="money">{{item.couponMoney}}</text>元</view>
              <view class="rule">满{{item.leastMoney}}元使用</view>
            </view>
            <view class="right">
              <view class="title">{{item.couponTypeName}}</view>
              <view class="valid">
                <!-- <view class="use_way" wx:if="{{item.use_method}}">使用渠道：{{item.use_method||''}}</view> -->
                <view wx:if="{{item.couponMemo}}" style="text-decoration: underline;color: #666;padding: 10rpx 0;margin-bottom: -10rpx;" catchtap="toRule" data-item="{{item.couponMemo}}">查看规则</view>
                <!-- <text style="font-size: 24rpx;">{{item.giftbegindate}}至{{item.giftenddate}}</text> -->
                <view class='to-use-fb' wx:if="{{item.isHave==0}}">{{item.giftprice}}福币兑换</view>
                <view class='to-use-fb' wx:else>去使用</view>
              </view>
            </view>
          </view>
        </view>
      </block>
      <block wx:if="{{type=='count'&&tabId2==2}}">
        <view wx:if="{{shopCouponGetList.length == 0}}" style="padding:15rpx;">
          <view style="color:#ccc;text-align:center;padding:10rpx;">没有不可用的优惠券</view>
        </view>
        <view wx:if="{{shopCouponGetList.length > 0}}" style="padding:15rpx;">
          <view wx:for="{{shopCouponGetList}}" wx:key="index" style="padding-bottom:20rpx;">
            <view class="coupon">
              <view class="left" style="background:#B2B2B2;padding: 16px 5px;">
                <view class='money-content'> <text class="money">{{item.money}}</text>元</view>
                <view class="rule">满{{item.leastMoney}}元使用</view>
              </view>
              <view class="right">
                <view class="title">{{item.name}}</view>
                <!-- <view class="use_way">使用渠道：{{item.use_method}}</view> -->
                <view class="valid">
                  <text style="font-size: 24rpx;">{{item.validBegin}}至{{item.validEnd}}</text>
                </view>
                <view wx:if="{{item.memo}}" style="text-decoration: underline;color: #666;padding: 10rpx 0;margin-bottom: -10rpx;" catchtap="toRule" data-item="{{item.memo}}">查看规则</view>
              </view>
            </view>
            <text wx:if="{{item.reason != ''}}" style="display: flex;align-items: center;margin-top: -16rpx;margin-bottom: 8rpx;">
              <text class="yd-icon yd-icon-warn-outline" style="margin-left:24rpx;color:#FF7956;font-size: 22rpx;margin-right: 10rpx;"></text>
              <text style="font-size:22rpx;color:#a0a0a0;">{{item.reason}}</text>
            </text>
          </view>
        </view>
      </block>
    </scroll-view>
  </view>
  <view class="confirm" wx:if="{{tabId2 == 1}}" bindtap="confirm">确认选择</view>
</view>
