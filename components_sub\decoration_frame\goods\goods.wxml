<view class="goods-container" style="z-index:{{index+1}};background: {{config.bg}}; margin: {{config.mTB}}rpx {{config.mLR}}rpx; border-radius: {{config.radius}}rpx; {{styles}}">
  <swipe wx:if="{{config.showTitle===2&&unitData.titleSwipe}}" config="{{unitData.titleSwipe.config}}" unitData="{{unitData.titleSwipe.data}}" shopInfo="{{shopInfo}}" pid="{{pid}}" unitName="{{config.unitName||'商品'}}" />
  <img wx:elif="{{config.showTitle && unitData.titleCfg.imgUrl}}" src="{{unitData.titleCfg.imgUrl}}" mode="aspectFill" bindtap="onTap" data-item="{{unitData.titleCfg}}" style="height: {{unitData.titleCfg.h}}rpx;width:100%;" placeholder="{{false}}"></img>
  <view wx:elif="{{config.showTitle}}" class="goods-title" style="background: {{unitData.titleCfg.bg}}; height: {{unitData.titleCfg.h}}rpx" bindtap="onTap" data-item="{{unitData.titleCfg}}">
    <view class="title-content" style="font-size: {{unitData.titleCfg.fontSize}}rpx">{{config.title}}</view>
    <view wx:if="{{unitData.type === 'seckill'&&unitData.list_countdown}}" class="seckill-time">
      <countDown time="{{unitData.list_countdown*1000}}" format="HH:mm:ss" bindfinish="hideCountDown"></countDown>
    </view>
    <view wx:if="{{unitData.titleCfg.link}}" class="more">
      查看更多
      <text class="uni-icon uni-icon-arrowright"></text>
    </view>
  </view>
  <!-- 横向滚动布局使用scroll-view -->
  <scroll-view wx:if="{{config.layout === 'scroll'}}" class="goods-list layout-scroll" scroll-x show-scrollbar="{{false}}">
    <view class="scroll-content" style="gap: {{config.goodsGap || 0}}rpx;padding: {{config.goodsPTB || 0}}rpx {{config.goodsPLR || 0}}rpx;">
      <goods-item class="gitem" wx:for="{{unitData.list}}" wx:key="index" item="{{item}}" config="{{config}}" bind:tap="onGoodsTap" unitData-index="{{index}}" data-item="{{item}}" />
    </view>
  </scroll-view>

  <!-- 其他布局使用普通view -->
  <view wx:else class="goods-list layout-{{config.layout}}" style="padding: {{config.goodsPTB || 0}}rpx {{config.goodsPLR || 0}}rpx; gap: {{config.goodsGap || 0}}rpx">
    <goods-item class="gitem" wx:for="{{unitData.list}}" wx:key="index" item="{{item}}" config="{{config}}" bind:tap="onGoodsTap" unitData-index="{{index}}" data-item="{{item}}" />
  </view>
  <img wx:if="{{unitData.bottomCfg.imgUrl}}" src="{{unitData.bottomCfg.imgUrl}}" mode="aspectFill" bindtap="onTap" data-item="{{unitData.bottomCfg}}" style="height: {{unitData.bottomCfg.h}}rpx;width:100%;display: block;"></img>
</view>
<observer id="observer" observeAll bindobserver="observer" start></observer>
