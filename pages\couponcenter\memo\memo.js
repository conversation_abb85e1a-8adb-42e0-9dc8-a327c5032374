// pages/couponcenter/memo/memo.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    coupon: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    let momo = wx.getStorageSync('memo')
    wx.removeStorageSync('memo')
    const limitShop = await app.util.checkLimitShop()
    
    // 删除带店的那行
    if (limitShop) {
      momo = momo.split('\r\n').filter(line => !line.includes('店')).join('\r\n')
    }
    this.setData({
      couponMemo: momo
    })

  },
  goback() {
    wx.navigateBack()
  }
})
