{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nmodule.exports = rfdc\n\nfunction copyBuffer (cur) {\n  if (cur instanceof Buffer) {\n    return Buffer.from(cur)\n  }\n\n  return new cur.constructor(cur.buffer.slice(), cur.byteOffset, cur.length)\n}\n\nfunction rfdc (opts) {\n  opts = opts || {}\n\n  if (opts.circles) return rfdcCircles(opts)\n  return opts.proto ? cloneProto : clone\n\n  function cloneArray (a, fn) {\n    var keys = Object.keys(a)\n    var a2 = new Array(keys.length)\n    for (var i = 0; i < keys.length; i++) {\n      var k = keys[i]\n      var cur = a[k]\n      if (typeof cur !== 'object' || cur === null) {\n        a2[k] = cur\n      } else if (cur instanceof Date) {\n        a2[k] = new Date(cur)\n      } else if (ArrayBuffer.isView(cur)) {\n        a2[k] = copyBuffer(cur)\n      } else {\n        a2[k] = fn(cur)\n      }\n    }\n    return a2\n  }\n\n  function clone (o) {\n    if (typeof o !== 'object' || o === null) return o\n    if (o instanceof Date) return new Date(o)\n    if (Array.isArray(o)) return cloneArray(o, clone)\n    if (o instanceof Map) return new Map(cloneArray(Array.from(o), clone))\n    if (o instanceof Set) return new Set(cloneArray(Array.from(o), clone))\n    var o2 = {}\n    for (var k in o) {\n      if (Object.hasOwnProperty.call(o, k) === false) continue\n      var cur = o[k]\n      if (typeof cur !== 'object' || cur === null) {\n        o2[k] = cur\n      } else if (cur instanceof Date) {\n        o2[k] = new Date(cur)\n      } else if (cur instanceof Map) {\n        o2[k] = new Map(cloneArray(Array.from(cur), clone))\n      } else if (cur instanceof Set) {\n        o2[k] = new Set(cloneArray(Array.from(cur), clone))\n      } else if (ArrayBuffer.isView(cur)) {\n        o2[k] = copyBuffer(cur)\n      } else {\n        o2[k] = clone(cur)\n      }\n    }\n    return o2\n  }\n\n  function cloneProto (o) {\n    if (typeof o !== 'object' || o === null) return o\n    if (o instanceof Date) return new Date(o)\n    if (Array.isArray(o)) return cloneArray(o, cloneProto)\n    if (o instanceof Map) return new Map(cloneArray(Array.from(o), cloneProto))\n    if (o instanceof Set) return new Set(cloneArray(Array.from(o), cloneProto))\n    var o2 = {}\n    for (var k in o) {\n      var cur = o[k]\n      if (typeof cur !== 'object' || cur === null) {\n        o2[k] = cur\n      } else if (cur instanceof Date) {\n        o2[k] = new Date(cur)\n      } else if (cur instanceof Map) {\n        o2[k] = new Map(cloneArray(Array.from(cur), cloneProto))\n      } else if (cur instanceof Set) {\n        o2[k] = new Set(cloneArray(Array.from(cur), cloneProto))\n      } else if (ArrayBuffer.isView(cur)) {\n        o2[k] = copyBuffer(cur)\n      } else {\n        o2[k] = cloneProto(cur)\n      }\n    }\n    return o2\n  }\n}\n\nfunction rfdcCircles (opts) {\n  var refs = []\n  var refsNew = []\n\n  return opts.proto ? cloneProto : clone\n\n  function cloneArray (a, fn) {\n    var keys = Object.keys(a)\n    var a2 = new Array(keys.length)\n    for (var i = 0; i < keys.length; i++) {\n      var k = keys[i]\n      var cur = a[k]\n      if (typeof cur !== 'object' || cur === null) {\n        a2[k] = cur\n      } else if (cur instanceof Date) {\n        a2[k] = new Date(cur)\n      } else if (ArrayBuffer.isView(cur)) {\n        a2[k] = copyBuffer(cur)\n      } else {\n        var index = refs.indexOf(cur)\n        if (index !== -1) {\n          a2[k] = refsNew[index]\n        } else {\n          a2[k] = fn(cur)\n        }\n      }\n    }\n    return a2\n  }\n\n  function clone (o) {\n    if (typeof o !== 'object' || o === null) return o\n    if (o instanceof Date) return new Date(o)\n    if (Array.isArray(o)) return cloneArray(o, clone)\n    if (o instanceof Map) return new Map(cloneArray(Array.from(o), clone))\n    if (o instanceof Set) return new Set(cloneArray(Array.from(o), clone))\n    var o2 = {}\n    refs.push(o)\n    refsNew.push(o2)\n    for (var k in o) {\n      if (Object.hasOwnProperty.call(o, k) === false) continue\n      var cur = o[k]\n      if (typeof cur !== 'object' || cur === null) {\n        o2[k] = cur\n      } else if (cur instanceof Date) {\n        o2[k] = new Date(cur)\n      } else if (cur instanceof Map) {\n        o2[k] = new Map(cloneArray(Array.from(cur), clone))\n      } else if (cur instanceof Set) {\n        o2[k] = new Set(cloneArray(Array.from(cur), clone))\n      } else if (ArrayBuffer.isView(cur)) {\n        o2[k] = copyBuffer(cur)\n      } else {\n        var i = refs.indexOf(cur)\n        if (i !== -1) {\n          o2[k] = refsNew[i]\n        } else {\n          o2[k] = clone(cur)\n        }\n      }\n    }\n    refs.pop()\n    refsNew.pop()\n    return o2\n  }\n\n  function cloneProto (o) {\n    if (typeof o !== 'object' || o === null) return o\n    if (o instanceof Date) return new Date(o)\n    if (Array.isArray(o)) return cloneArray(o, cloneProto)\n    if (o instanceof Map) return new Map(cloneArray(Array.from(o), cloneProto))\n    if (o instanceof Set) return new Set(cloneArray(Array.from(o), cloneProto))\n    var o2 = {}\n    refs.push(o)\n    refsNew.push(o2)\n    for (var k in o) {\n      var cur = o[k]\n      if (typeof cur !== 'object' || cur === null) {\n        o2[k] = cur\n      } else if (cur instanceof Date) {\n        o2[k] = new Date(cur)\n      } else if (cur instanceof Map) {\n        o2[k] = new Map(cloneArray(Array.from(cur), cloneProto))\n      } else if (cur instanceof Set) {\n        o2[k] = new Set(cloneArray(Array.from(cur), cloneProto))\n      } else if (ArrayBuffer.isView(cur)) {\n        o2[k] = copyBuffer(cur)\n      } else {\n        var i = refs.indexOf(cur)\n        if (i !== -1) {\n          o2[k] = refsNew[i]\n        } else {\n          o2[k] = cloneProto(cur)\n        }\n      }\n    }\n    refs.pop()\n    refsNew.pop()\n    return o2\n  }\n}\n"]}