.img-box {
  width: 100%;
  font-size: 0;
  position: relative;
  overflow: hidden;
}

.img-box .img {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.img-box .img:last-child {
  margin-bottom: 0 !important;
}



.type2-wrap {
  width: 100%;
  position: relative;

}

.type2-wrap .bg-img {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: all 0.1s;
  z-index: -1;
}


.type2-wrap .bg-img.active {
  opacity: 1;
  z-index: 0;
}


.type2-wrap .swipe-box {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 345px;
  height: 200px;
  margin: 0 auto;
  overflow: hidden;
  z-index: 10;
}

.type2-wrap .swipe-box .swipe {
  position: absolute;
  z-index: 1;
}

.swipe-box4 {
  width: 345px;
  height: 200px;
  margin: 0 auto;
  overflow: hidden;
  z-index: 10;
}

.swipe-box4 .swipe {
  height: 100%;
  z-index: 1;
}


.l-swiper-dot {
  position: absolute;
  bottom: 10rpx;
  padding: 8rpx;
  display: flex;
  left: 50%;
  transform: translateX(-50%);
}

.l-swiper-dot-item {
  height: 10rpx;
  width: 10rpx;
  border-radius: 20rpx;
  background: #3E3A3920;
  margin-left: 10rpx;
  transition: all .3s;
}

.l-swiper-active {
  background: #3E3A39dd;
  width: 42rpx;
  opacity: 1;
}

.l-swiper-active-i {
  background: #3E3A39dd !important;
  width: 42rpx !important;
  opacity: 1 !important;
}

.l-swiper-active-d {
  background: #3E3A3980 !important;
  width: 10rpx !important;
}

.swiper-dot {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  border-radius: 0 0 8rpx 8rpx;
  height: 10rpx;
}

.fade-indicators {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16rpx;
  z-index: 10;
}

.fade-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.fade-dot.active {
  background: #ffffff;
  transform: scale(1.2);
}


.swiper-dot-item {
  flex: 1;
  background: #3e3a3920;
  transition: all 0.1s;
  opacity: 0.35;
}

.swiper-dot-item.active {
  background: #3e3a39dd;
  opacity: 0.6;
}

/* 样式3：渐变轮播 */
.type3-wrap {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.type3-wrap .fade-swiper {
  width: 100%;
  position: relative;
}

.type3-wrap .fade-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.type3-wrap .fade-img.active {
  opacity: 1;
}
