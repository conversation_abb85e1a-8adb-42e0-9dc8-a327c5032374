<view class="step-list" style="{{styles}}">
  <view wx:for="{{list}}" wx:key="index" class="step-item">
    <view class="before" style="background: {{status>index?color:''}};border-color: {{status>=index?color:color4}};">
      <view class="{{status>index?'uni-icon uni-icon-checkmarkempty':''}}" style="font-size: 26rpx;color: {{color3}};"></view>
    </view>
    <view wx:if="{{status>index&&list2&&list2.length==list.length&&list2[index]}}" style="{{activefontStyle}}">{{list2[index]}}</view>
    <view wx:else style="{{status>=index?activefontStyle:fontStyle}}">{{item}}</view>
    <view class="after" style="background: {{color2}};"></view>
  </view>
</view>
