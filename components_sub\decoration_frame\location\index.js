const app = getApp()
Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    isPageTop: {
      type: Number,
      value: 1,
      observer: true
    }
  },
  data: {
    city: '', //当前定位城市
    // 新增
  },
  async attached() {
    this.loadHeader()
  },
  pageLifetimes: {
    show: function() {
      // 页面被展示
      console.log('showshow');
      let location = app.local.get('location') || ''
      let dsho_id = wx.getStorageSync('dsho_id')
      if (dsho_id && dsho_id != this.dsho_id) {
        this.dsho_id = dsho_id
        this.setData({
          city: location.city || '',
          dsho_id: this.dsho_id,
          showText: ''
        })
        if (!location.city) {
          this.getDistrictShopId()
        }
      }

    },
    hide: function() {
      // 页面被隐藏
    },
    resize: function(size) {
      // 页面尺寸变化
    }
  },
  methods: {
    async loadHeader(options) {
      let location = app.local.get('location')
      this.dsho_id = app.local.get('dsho_id')
      if (location && location.city && this.dsho_id) {
        this.setData({
          city: location.city,
          dsho_id: this.dsho_id,
          showText: ''
        })
      }
      await this.getDistrictShopId()
    },
    /* 加载大区店列表 */
    async getDistrictShopList() {
      await app.reqGet(
        'ms-sanfu-mgr-mall/decorationPageList/districtShopList', {},
        res => {
          if (res.success) {
            res.data.unshift({
              districtShoName: '关闭定位',
              districtShopId: ''
            })
            let objFn = (obj, objIndex, objs) => {
              return obj.districtShopId == this.data.dsho_id
            }
            let index = res.data.findIndex(objFn)
            this.setData({
              shopList: res.data,
              shoIndex: index
            })
          }
        }
      );
    },

    /* 获取区域店 */
    async getDistrictShopId() {
      if (this.shop) return // 已定位不再获取
      if (this.loading) return
      this.loading = true
      this.setData({
        showText: '定位中'
      })
      const results = await Promise.all([app.getWxLocation(), app.getAllShop(1)]);
      console.log('results', results);

      this.location = results[0];
      const shops = results[1];
      this.loading = false
      if (!shops || !this.location) {
        wx.hideToast()
        wx.showToast({
          title: !shops ? '获取门店列表失败' : '无法获取您的定位，为了给您提供优惠信息，请打开定位',
          icon: 'none',
          duration: 2000
        })
        this.setData({
          showText: ''
        })
        //获取异常
        return
      }
      // 门店，定位距离排序
      let defaultShop
      shops.forEach(el => {
        let gcj02 = app.util.gcj_encrypt(parseFloat(el.latitude), parseFloat(el.longitude))
        el.latitude = gcj02.lat
        el.longitude = gcj02.lon // 经度，浮点数，范围为180 ~ -180
        let disc = app.util.getdistance(this.location.lat, this.location.lon, el.latitude, el.longitude); // 获取2点距离
        el.distance = parseInt(disc);
        // 获取最近店
        if (!defaultShop && el.districtShoId) {
          this.shop = el
          defaultShop = true
        } else if (el.distance < this.shop.distance && el.districtShoId) {
          this.shop = el
        }
      });
      console.log('最近店', this.shop);
      // 支付店及主体
      if (!app.local.get('sho_id') && this.shop.distance <= 500 && this.shop.isOnline && this.shop.isWeixinPay && this.shop.orgId) {
        app.local.set('payShoId', this.shop.shoId, 24 * 60 * 60)
        app.local.set('orgId', this.shop.orgId)
      }
      app.local.set('dsho_id', this.shop.districtShoId, 24 * 60 * 60)
      app.local.set('Fsho_id', this.shop.shoId)
      app.sf.track('location_shop')
      this.dsho_id = this.shop.districtShoId
      this.triggerEvent('getindex', 1) // 首页请求
      this.dealAddress()
      console.log(this.shop);
      return
    },
    /* 获取默认地址 */
    // async getAddress() {
    //   const second = this.address ? 1 : 0
    //   await app.waitSid()
    //   const res = await app.util.getUserAddress();
    //     this.getCityWidth();
    //     this.address = res[0]
    //     this.setData({
    //       viewAddress: `${res[0].county}${res[0].street}${res[0].address}`,
    //     })
    //     console.log(this.address);
    //   this.addressReady = true
    //   this.dealAddress(second)
    // },
    // 显示地址 判断处理
    async dealAddress(second) {
      console.log('second', second, this.addressReady, this.shop);
      // if (!this.addressReady) return
      if (!this.shop) return
      if (second) return
      // 有地址判断城市

      // if (this.address && this.shop.city != this.address.city && !wx.getStorageSync('locationAlert') && false) {
      //   wx.setStorageSync('locationAlert', 1)
      //   wx.showModal({
      //     title: '',
      //     content: '您当前的收货地址不在您定位的城市，是否更换地址？',
      //     success: res => {
      //       if (res.confirm) {
      //         this.toAddress()
      //       }
      //     }
      //   })
      // }

      const map = await app.getAddress(this.location.lat, this.location.lon)
      if (map) {
        this.shop.city = map.addressComponent && map.addressComponent.city
        app.globalData.nearbyLocation = map
        let locationObj = {
          lat: this.location.lat,
          lon: this.location.lon,
          city: this.shop.city,
          county: map.addressComponent.district,
          province: map.addressComponent.province
        }
        // 腾讯地图处理
        // 没地址获取就近相关地址
        // if (!this.data.viewAddress) {
        this.setData({
          city: this.shop.city,
          showText: ''
        })
        app.local.set('location', {
          str: map.formatted_address,
          ...locationObj
        }, 24 * 60 * 60)
      } else {
        this.setData({
          city: this.shop.city,
          dsho_id: this.shop.districtShoId,
          showText: ''
        })
        app.local.set('location', {
          lat: this.location.lat,
          lon: this.location.lon,
        }, 24 * 60 * 60)
      }
    }
  }
});
