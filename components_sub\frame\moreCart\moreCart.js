const app = getApp()
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {},
  data: {
    viewList: []
  },
  attached() {},
  detached() {},
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {},
    hide: function() {
      this.closePop()
    },
    resize: function() {}
  },
  methods: {
    openPop() {
      this.setData({
        showPop: true
      })
    },
    closePop() {
      this.setData({
        showPop: false
      })
    },
    start(allgoodsId) {
      const orgId = wx.getStorageSync('orgId')
      if (this.otherOrgId && this.otherOrgId != orgId) return
      if (!this.otherOrgId || this.otherOrgId == orgId) {
        this.otherOrgId = orgId == 102 ? 101 : 102
        this.getOtherCartList()
      }
      this.data.allgoodsId = allgoodsId
    },
    // 获取其他店铺购物车列表
    async getOtherCartList() {
      app.globalData.oneceOrgId = this.otherOrgId
      const res = await app.reqGet('ms-sanfu-wap-cart/listNetSaleCart', {
        sid: wx.getStorageSync('sid'),
        shoId: wx.getStorageSync('dsho_id')
      })

      if (res.success && res.data.netSaleCartList) {
        let list = (res.data.netSaleCartList && res.data.netSaleCartList[0] && res.data.netSaleCartList[0].itemList) || []
        if (this.data.allgoodsId && this.data.allgoodsId != 1) {
          let cartGoodsId = this.data.allgoodsId.split(',')
          this.otherCartList = list.filter(i => !cartGoodsId.some(e => e == i.goodsId))
        } else {
          this.otherCartList = list
        }
        if (this.otherCartList.length > 0) {
          this.getFilterList()
        } else {
          this.setData({
            viewList: []
          })
        }
      }
    },
    // 过滤有效商品
    async getFilterList() {
      const res = await app.reqGet('ms-sanfu-wap-cart/listShareNetSaleCart', {
        sid: wx.getStorageSync('sid'),
        shoId: wx.getStorageSync('dsho_id'),
        goodsids: this.otherCartList.map(item => item.goodsId).join(',')
      })
      if (res.success && res.data.netSaleCartList) {
        let list = (res.data.netSaleCartList && res.data.netSaleCartList[0] && res.data.netSaleCartList[0].itemList) || []
        for (let i in list) {
          const e = list[i]
          delete e.goodsPromoList
          if (e.isQiang) {
            e.add_price = e.salePrice
            e.salePrice = e.qiangPrice
          }
          e.disable = 0
          // 失效商品判断
          if (e.isInvalid === 1) {
            e.disable = 2
          }
          // 未上架判断
          if ((e.goodsType == 7 || e.isValid == 0) && !(e.scanpage && e.shoId)) {
            e.disable = 2
          }
          // 查不到
          if (e.salePrice <= 0) {
            e.disable = 2
          }
        }
        const invalidGoods = this.otherCartList.filter(item => !list.some(e => e.goodsId === item.goodsId))
        invalidGoods.forEach(item => {
          item.disable = 2
        })
        list = list.concat(invalidGoods)
        list.sort((a, b) => a.disable - b.disable)
        this.data.viewList = list
        this.addCart()
        // this.triggerEvent('disableGoods', list.filter(e => e.disable === 2))
      }
    },
    async addCart(e) {
      let cartList = []
      let gooId = []
      let i
      if (e) {
        i = e.currentTarget.dataset.i
        const item = this.data.viewList[i]
        gooId.push(e.gooId)
        cartList.push({
          goodsid: item.goodsId,
          amount: 1,
          gooId: item.gooId,
          qiangId: item.qiangId
        })
      } else {
        this.data.viewList.forEach(e => {
          if (e.disable === 0) {
            gooId.push(e.gooId)
            cartList.push({
              goodsid: e.goodsId,
              amount: 1,
              gooId: e.gooId,
              qiangId: e.qiangId
            })
          }
        })
        if (gooId.length === 0) {
          this.setData({
            viewList: this.data.viewList
          })
          return
        }
      }
      const res = await app.reqPost('ms-sanfu-wap-cart/addShareNetSaleCart', {
        sid: wx.getStorageSync('sid'),
        shoId: wx.getStorageSync('dsho_id'),
        buyType: 0,
        gooId: gooId.join(),
        goodsidData: JSON.stringify(cartList)
      })
      if (res.success) {
        if (e) {
          this.data.viewList.splice(i, 1)
          this.setData({
            viewList: this.data.viewList
          })
          if (!this.data.viewList.length) {
            this.closePop()
          }
        } else {
          this.setData({
            viewList: this.data.viewList.filter(e => e.disable !== 0)
          })
        }
        this.triggerEvent('refreshCart')
      } else {
        if (!e) {
          this.setData({
            viewList: this.data.viewList
          })
        } else app.util.reqFail(res)
      }
    },
    async deleteOtherGoods(ids) {
      if (!this.otherOrgId) return
      // const item = this.data.viewList[i]
      app.globalData.oneceOrgId = this.otherOrgId
      const res = await app.reqPost('ms-sanfu-wap-cart/deleteNetSaleCart', {
        sid: wx.getStorageSync('sid'),
        shoId: wx.getStorageSync('dsho_id'),
        goodsidStr: ids
      })
      // if (res.success) {
      //   this.data.viewList.splice(i, 1)
      // } else {
      //   app.util.reqFail(res)
      // }
    }
  }
})
