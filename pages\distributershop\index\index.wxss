/* pages/distributershop/index/index.wxss */
@import '../qrcode.wxss';
@import "/font/icon_f7.wxss";

page {
  background: #f3f3f3;
}

.dis-nav {
  position: sticky;
  left: 0;
  top: 0;
  width: 100%;
  min-height: 100rpx;
  background: #ECF5FF;
  padding: 10rpx 20rpx;
  z-index: 10;
}

.dis-nav>.nav-item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.dis-nav .dis-card {
  margin: 10rpx 0;
  color: #409eff;
  font-size: 28rpx;
  flex: 1;
  display: flex;
  align-items: center;
}

.dis-nav .dis-shop {
  margin: 10rpx 0;
  height: 60rpx;
  display: flex;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  border-radius: 50rpx;
  overflow: hidden;
}

.dis-nav .dis-shop>view {
  padding: 0 16rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.dis-nav .dis-shop>.info {
  border: 2rpx solid #409eff;
  border-right: 0;
  border-radius: 50rpx 0 0 50rpx;
}

.dis-nav .dis-shop>.change {
  background: #A587ff;
  color: #fff;
}

.score {
  background: #fff;
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
  font-size: 28rpx;
  border-bottom: 16rpx solid #f3f3f3;
}

.score>.score-box {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
  padding: 20rpx 0;
}

.score>.score-box:last-of-type {
  border: none;
  padding-bottom: 10rpx;
}

.score>.score-box>.score-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score>.score-box>.score-item>.score-title {
  margin-bottom: 30rpx;
  position: relative;
}

.score>.score-box>.score-item>.score-value {
  font-weight: 700;
  font-size: 32rpx;
}

.score>.score-more {
  color: #0055ff;
  padding: 20rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 40rpx;
}

.menu {
  background: #fff;
  display: flex;
  flex-direction: column;
  font-size: 28rpx;
  border-bottom: 16rpx solid #f3f3f3;
}

.menu>.menu-tab {
  font-size: 32rpx;
  font-weight: 700;
  padding: 20rpx 30rpx;
  padding-bottom: 10rpx;
  color: #333;
}

.menu>.menu-box {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
  padding-bottom: 20rpx;
}

.menu>.menu-box>.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
  position: relative;
  width: 25%;
}

.menu>.menu-box>.menu-item>.menu-img {
  width: 82rpx;
  height: 82rpx;
  margin: 14rpx 0;
  border-radius: 50%;
  background: #eee;
}

.menu>.menu-box>.menu-item>.f7 {
  width: 110rpx;
  height: 110rpx;
  border-radius: 6rpx;
  font-size: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background: linear-gradient(120deg, #b2ffcf, #8a73ff); */
  /* color: #fff; */
  /*  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent; */
  color: #409eff;
}

.menu>.menu-box>.menu-item>.menu-title {
  font-size: 26rpx;
  margin-top: 4rpx;
  color: #444;
}

.menu .has-new {
  background: #38D726;
  color: white;
  padding: 0 6px;
  height: 16px;
  font-size: 12px;
  line-height: 16px;
  border-radius: 10px;
  box-shadow: 0 0 2px #38D726;
  position: absolute;
  top: -8px;
  right: 0;
}





.dis-block {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.dis-block .item {
  width: 50%;
  justify-content: center;
  align-items: center;
  display: flex;
}

.item-content {
  position: relative;
  margin: 5px 0;
  border-radius: 5px;
  box-shadow: 1px 1px 3px #555;
  justify-content: center;
  align-items: center;
  display: flex;
  background: linear-gradient(to right, #FF87A5, #FF6E93);
  color: white;
  width: 350rpx;
  height: 350rpx;
}



@keyframes tipMove {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  100% {
    opacity: 1;
    transform: scale(1)
  }
}

.showdisshop {
  box-sizing: border-box;
  width: 100%;
  height: 70rpx;
  top: 0;
  left: 0;
  padding: 20rpx 20rpx 0 20rpx;
  display: flex;
  color: #404040;
  font-size: 30rpx;
  font-weight: 700;
  display: flex;
  align-items: center;
  z-index: 6;
}

.showdisshop view {
  /* width: 136rpx; */
  height: 45rpx;
  background: #fff;
  color: #8f6aff;
  border: 2rpx solid #8f6aff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 70rpx;
  font-size: 26rpx;
  margin-left: 30rpx;
  padding: 0 16rpx;
}

.uni-icon-info {
  position: absolute;
  right: 0;
  color: #777;
  font-size: 34rpx;
  padding: 20rpx;
  margin-top: -16rpx;
  margin-bottom: -20rpx;
  margin-right: -60rpx;
}

.tip-box {
  display: flex;
  align-items: center;
  background: #fef0f0;
  line-height: 1;
  overflow: hidden;
}

.title {
  flex-shrink: 0;
  background: linear-gradient(90deg, #fff, #fef0f0);
  color: #f00;
  font-weight: 700;
  z-index: 1;
  align-self: stretch;
  padding: 10rpx 0 10rpx 20rpx;
}

@keyframes wordsLoop {
  0% {
    transform: translateX(0);
  }

  5% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

.noticeTxtInfo {
  white-space: nowrap;
  font-size: 30rpx;

}

.moveX {
  animation: 35s wordsLoop infinite linear;
}

.textarea {
  display: block;
  resize: vertical;
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  line-height: 1.5;
  box-sizing: border-box;
  font-size: inherit;
  color: #606266;
  background-color: #fff;
  background-image: none;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  margin: 0 16rpx;
  width: 560rpx;
  transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
}
