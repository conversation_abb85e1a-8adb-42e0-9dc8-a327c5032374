var util = require('../../../utils/util');
var app = getApp();
app.netWorkData = { //网站返回数据
  result: [{
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    },
    {
      code: -1,
      data: "",
      model: "",
      msg: '发起请求失败'
    }
  ]
}
app.globalData = {
  ...app.globalData,
  pwidth: 0, //手机屏幕宽度
  pheight: 0, //手机高度
  psize: 1, //比例
  newsPower: null, //文章权重
  version: "V1.2.0", //版本
  openid: "", //OPENID
  unionid: "", //关联编号 
  session_key: "", //Session Key
  sys_token: "", //搜宜车key
  latestdate: "", //上次登录日期
  tzdate: "", //通知更新日期
  pldate: "", //评论更新日期
  dzdate: "", //点赞更新日期
  cgdate: "", //最后更新草稿日期  
  htdate: "", //话题最后更新日期
  sanfu_sid: "", //三福sid
  sanfu_id: "", //三福会员 卡ID
  recommendid: "", //推荐者ID
  disshop: "", //商城传过来的参数
  sanfu_fubi: null, //福币等级
  latitude: 26.08198, //当前经度
  longitude: 119.30405, //当前纬度
  userInfo: null,
  //sys_url: 'https://tlive.sanfu.com/apis/',
  sys_url: util.apidomain == 'tm.sanfu.com' ? 'https://sanfuapi.souyisi.com:5043/apis/' : 'https://sns.sanfu.com/apis/',
  // sys_url: 'https://sanfuapi.souyisi.com:5043/apis/',
  //sys_url: 'http://venue.souyisi.cn:6043/apis/',
  sanfu_url: 'https://' + util.apidomain + '/', //三福接口地址
  level: 0,
  appid: util.apidomain == 'tm.sanfu.com' ? 'wx29c3524951936ce1' : 'wx24e34bc75882ebb3', //测试 wxad3e84712fc71fad 正式 wx24e34bc75882ebb3    //项目appid
  appkey: "SDFDSFDewrwerweSDFF4564SDFsdf", //项目appkey
  mapkey: "OP3BZ-OQCLO-YBDWE-SKFB7-B4KBO-A4BLU",
  province: "", //省份
  city: "", //城市
  address: "", //详细地址
  phone: 0, //手机号码
  tags: [], //标签表
  sendNews: null, //发布文章内容
  goodspic: null, //正在编辑的图片
  goodstag: null, //正在编辑商品标签
  baida: false, //是否发布百搭
  buyGoods: null, //商品购买记录
  beautyUrl: "", //美图地址

  jbtype: ["违法违规", "恶意广告", "造谣、伪科学", "不友善、内容不适", "其他"], //举报类型

  cpassed: 0, //文章默认是否审核
  newsID: 0, //预览的帖子
  newspreview: null, //预览的新闻
}
let info = wx.getWindowInfo()
app.globalData.StatusBar = info.statusBarHeight;
let custom = wx.getMenuButtonBoundingClientRect();
app.globalData.Custom = custom;
app.globalData.CustomBar = custom.bottom + custom.top - info.statusBarHeight;
app.globalData.pwidth = info.screenWidth;
app.globalData.pheight = info.screenHeight;
app.globalData.psize = info.screenWidth / 750;
