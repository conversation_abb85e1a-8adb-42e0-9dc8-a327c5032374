@keyframes show {
  0% {
    opacity: 0;
  }

  0.4% {
    opacity: 1;
  }

  5.5% {
    opacity: 1;
  }

  6% {
    opacity: 0;
  }

  100% {
    opacity: 0;
  }
}

.box {
  position: fixed;
  top: -16rpx;
  /* left: 0; */
  right: 0;
  z-index: 99999999999;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  flex-direction: column;
  animation-name: show;
  animation-duration: 100s;
  animation-timing-function: linear;
  animation-iteration-count: 1;
}

.arrow {
  width: 0;
  height: 0;
  margin-right: 120rpx;
  border-width: 16rpx;
  border-style: solid;
  border-color: transparent transparent rgba(0, 0, 0, 0.65) transparent;
}

.body {
  background-color: rgba(0, 0, 0, 0.65);
  box-shadow: 0 10rpx 20rpx -10rpx rgba(0, 0, 0, 0.65);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 64rpx;
  padding: 0 20rpx;
  margin-right: 40rpx;
}

.body > text {
  color: #fff;
  font-size: 24rpx;
  font-weight: 400;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: rgba(255, 255, 255, 0.9);
  width: 100%;
  padding: 150rpx 40rpx 0;
  box-sizing: border-box;
  z-index: 9999999;
}

.modal  view {
  margin: 10px 0;
  display: flex;
  /* align-items: center; */
  flex-direction: column;
}

.modal  view > text {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 5px;
  color: #333;
}

.modal  view > image {
  border-radius: 10px;
}

.modal .ok-btn {
  width: 100%;
  padding-top: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 200rpx;
}

.ok-btn > view {
  height: 80rpx;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 80rpx;
  padding: 0 50rpx;
  font-size: 30rpx;
  background: linear-gradient(90deg, #ff698f, #f32971);
  box-shadow: 0 10rpx 20rpx #ff698f;
  color: #fff;
  font-weight: 400;
  margin: 0;
}
