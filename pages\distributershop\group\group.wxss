/* pages/couponcenter/history/history.wxss */
@import "/font/icon_f7.wxss";
@import '../qrcode.wxss';

page {
  min-width: 100%;
  width: 750rpx;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.main {
  display: flex;
  flex-direction: column;
  min-width: 100%;
  width: 750rpx;
  min-height: 100%;
  height: 100%;
  flex: 1;
}

.main .swiper-tab {
  width: 100%;
  text-align: center;
  line-height: 60rpx;
  font-weight: 700;
}

.swiper-tab-list {
  font-size: 30rpx;
  display: inline-block;
  min-width: 200rpx;
  color: #444;
  margin-right: 4%;
  margin-left: 4%;
  position: relative;
}

.on {
  color: #f63979;
  /* border-bottom: 5rpx solid #F63979; */
}

.on::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 38%;
  border: 4rpx solid;
  border-radius: 10rpx;
  width: 20%;
  background: #F63979;
}

.swiper-box {
  width: 750rpx;
  flex: 1;
  position: relative;
}

.swiper-box .coupon-list {
  text-align: center;
  height: auto;
  width: 100%;
  /* overflow-y: scroll; */
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}

.none-history {
  padding: 40rpx;
  font-size: 30rpx;
}

.bg {
  height: 360rpx;
  width: 100%;
  position: relative;
  border-radius: 6rpx 6rpx 0 0;
}

.group-goods {
  margin: 0 1%;
  margin-top: 30rpx;
  height: 560rpx;
  width: 48%;
  box-shadow: 0 0 2rpx #aaa;
  border-radius: 6rpx;
  background: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.autor {
  height: 40rpx;
  line-height: 40rpx;
  display: flex;
  justify-content: space-between;
  color: #a6a6a6;
  font-size: 24rpx;
  border-radius: 10rpx 10rpx 0 0;
  background: white;
  padding: 0 10rpx;
  padding-bottom: 0;
}

.autor-name {
  max-width: 180rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  text-overflow: -o-ellipsis-lastline;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.article-title {
  /* border-top: 1px solid #eee; */
  display: flex;
  align-items: center;
  color: #000;
  font-size: 24rpx;
  background: white;
  padding: 10rpx;
  padding-top: 0;
  min-height: 80rpx;
}

.viewer {
  display: flex;
  font-size: 24rpx;
  justify-content: space-around;
  padding: 0 14rpx;
  background: white;
  border-radius: 0 0 10rpx 10rpx;
}

.viewer-count {
  display: flex;
  align-items: center;
  position: relative;
  color: #c7c6c6;
  padding: 14rpx;
}

.viewer-count .f7 {
  position: relative;
  font-size: 30rpx;
}

.viewer-count text {
  color: #555;
  font-size: 18rpx;
  position: absolute;
  height: 20rpx;
  right: 0;
  top: 0;
}

.viewer-share {
  display: flex;
  align-items: center;
}

.viewer-share text {
  background: #ff6e93;
  color: white;
  border-radius: 40rpx;
  padding: 2rpx 10rpx;
  font-size: 20rpx;
}

.viewer .left {
  display: flex;
  align-items: center;
}

.add-group-goods {
  display: flex;
  position: fixed;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  width: 120rpx;
  margin-left: 50%;
  left: -50rpx;
  bottom: 40rpx;
  background: white;
  color: #555;
  border-radius: 60rpx;
  box-shadow: 0 0 2rpx #f63979;
}

.caogaoxiang {
  display: flex;
  position: fixed;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 120rpx;
  width: 120rpx;
  margin-left: 50%;
  right: 50rpx;
  bottom: 50rpx;
  font-size: 22rpx;
  background: white;
  color: #f63979;
  border-radius: 60rpx;
  box-shadow: 0 0 2rpx #f63979;
}

.caogao-num {
  position: absolute;
  top: 10rpx;
  right: -2rpx;
  color: white;
  font-size: 22rpx;
  height: 30rpx;
  width: 30rpx;
  border-radius: 20rpx;
  background: #ee1137;
  line-height: 30rpx;
  text-align: center;
}

.caogao-pop {
  height: 600rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.caogao-pop scroll-view {
  height: 500rpx;
  width: 100%;
  padding: 0 16rpx;
  box-sizing: border-box;
}

.go-back {
  font-size: 32rpx;
  color: white;
  text-align: center;
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background: linear-gradient(to right, #ff87a5, #ff6e93);
}

.form-row {
  height: 90rpx;
  line-height: 90rpx;
  color: #555;
  overflow: hidden;
  position: relative;
  font-size: 26rpx;
  display: flex;
  border-bottom: 2rpx solid #eee;
}

.form-row .column {
  flex: 1;
  white-space: nowrap;
  display: flex;
  flex-wrap: nowrap;
  overflow: scroll;
}

.form-row .column text {}

.form-row .operators {
  /* border-left: 1px solid #bbb; */
  /* box-shadow: -2px 0 2px #bbb; */
  margin-left: 6rpx;
  color: #F6F6F6;
}

.form-row .operators text {
  padding: 6rpx 20rpx;
  border-radius: 10rpx;
  margin: 0 6rpx;
  box-shadow: 0 0 4rpx #aaa;
}

.form-row .operators .delete {
  background: white;
  color: #f63979;
  border: 2rpx solid #f63979;
}

.form-row .operators .edit {
  background: #f63979;
  color: white;
  border: 2rpx solid #f63979;
}

.form-row .operators .preview {
  background: #03be01;
  color: white;
  border: 2rpx solid #03be01;
  /*rgb(31, 177, 177);*/
}

/* 分享类型选择 */
.view-types {
  margin-top: 16rpx;
  width: 100%;
  height: 64rpx;
  display: flex;
}

.view-types view {
  flex: 1;
  background: #d7d7d7;
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4rpx;
}

.view-types view:last-of-type {
  margin: 0;
}