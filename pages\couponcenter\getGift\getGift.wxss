/* pages/couponcenter/getCoupon/getCoupon.wxss */
page {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background: #f3f3f3;
}

.container {
  width: 100%;
}

.coupon {
  padding: 0 20rpx;
  margin-top: 36rpx;
  position: relative;
  display: flex;
  height: 180rpx;
}

.coupon .content {
  flex: 1;
  background: #fff;
  height: 100%;
  padding-left: 12rpx;
  background: url(//img.sanfu.com/img/wechat/coupon/coupon_vip.png) no-repeat center #fff;
  background-size: 22%;
}

.coupon .content .top {
  padding: 12rpx;
  border-bottom: dashed 1px #afafaf;
  font-size: 24rpx;
  color: #afafaf;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.coupon::before {
  content: ' ';
  width: 64rpx;
  height: 100%;
  background: url(https://img.sanfu.com/img/index_boy/yhq-left.png) no-repeat top left;
  background-size: 100% 100%;
}

.coupon::after {
  content: ' ';
  width: 20rpx;
  background: url(https://img.sanfu.com/img/index_boy/yhq-right2.png) no-repeat top left;
  background-size: 100% 100%;
  height: 100%;
}

.btn {
  color: #ff5821;
  width: 280rpx;
  height: 80rpx;
  background: linear-gradient(180deg, #fff8f3, #ffad76);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 36rpx;
  font-weight: 700;
  letter-spacing: 4rpx;
  flex: auto;
  margin: 0rpx auto 30rpx;
}
