const app = getApp()
import Auth from '../../auth/index.js';
Page({

  // https://mp.weixin.qq.com/bizmall/activatemembercard?action=preshow&&&&

  data: {
    showLoading: true, //加载动画标识
    has_login: false, //登录状态
    orderCheckIndex: 0, //订单类型筛选
    topInfo: {
      headImgUrl: '',
      has_sign: false, //是否签到
      existOccupation: 1 //是否已填写职业[0-未填写/1-已填写]
    },

    vip_qy: [{
        iconName: 'icon-a-huiyuanxiaochengxuwode1',
        title: '会员抽奖',
        info: '',
        pageUrl: '/pages/prize/index/index?type=2'
      },
      {
        iconName: 'icon-a-huiyuanxiaochengxuwode',
        title: '福币兑换',
        info: '',
        pageUrl: '/pages/fubishop/index/index'
      },
      {
        iconName: 'icon-a-huiyuanxiaochengxuwode4',
        title: '商家联盟',
        info: '',
        pageUrl: '/pages/fubishop/differentIndustry/differentIndustry'
      },
      {
        iconName: 'icon-a-huiyuanxiaochengxuwode2',
        title: '包邮卡',
        info: '',
        pageUrl: 'h5user/coupon/showPostFreeCards'
      },
      {
        iconName: 'icon-a-huiyuanxiaochengxuwode6',
        title: '生日礼包',
        info: '',
        pageUrl: 'h5user/coupon/showBirthGift'
      },
      {
        iconName: 'icon-a-huiyuanxiaochengxuwode3',
        title: '退货补偿券',
        info: '',
        pageUrl: 'h5user/coupon/showReturnCoupon'
      },
      {
        img: 'https://img.sanfu.com/img/mgr_common/17508434855859875.png',
        title: 'FU力勋章',
        info: '',
        pageUrl: '?wxappid=sf2&wxpage=%2Fpages%2Fuser%2Fmedals%2Findex%2Findex'
      },
      
    ],
    cardInfo: [{
        cardLevel: 1,
        cardName: '三福普卡',
        discounts: '无折扣',
        imgUrl: 'https://img.sanfu.com/sf_access/uploads/dCLTZztjZm36rGoRFipL6AgE4tsDKDEG.png',
        bgCorlor: '#ee427e',
        func_style: 'background: linear-gradient(135deg, #fcd6e0, #f9b5cd);color:#E33974', //f5528d;',
        tip: '免费获得',
        cost: 0,
        needcost: 0,
      },
      {
        cardLevel: 2,
        cardName: '三福银卡',
        discounts: '9折',
        imgUrl: 'https://img.sanfu.com/sf_access/uploads/8LnUwtULwer8MQVB0H4BMkTbEQdD9M7w.png',
        bgCorlor: '#858a95',
        func_style: 'background: linear-gradient(135deg, #e3e4e6, #bfc6c1);color:#686867;', //#767676;',
        tip: '消费满300获得',
        cost: 300,
        needcost: 0,
      },

      {
        cardLevel: 3,
        cardName: '三福金卡',
        discounts: '8.8折',
        imgUrl: 'https://img.sanfu.com/sf_access/uploads/F4nXOm9mlLDQumHw1Pl6DatwPvq48H7x.png',
        bgCorlor: '#b3966b',
        func_style: 'background: linear-gradient(135deg, #efe0c7, #e0cdac);color:#7C602C', //7c602c;',
        tip: '消费满800获得',
        cost: 800,
        needcost: 0,
      },

      {
        cardLevel: 4,
        cardName: '三福白金卡',
        discounts: '8.5折',
        imgUrl: 'https://img.sanfu.com/sf_access/uploads/pcMMbPqQCtuvt07qktsLTLaR5zE77cX1.png',
        bgCorlor: '#c28d78',
        func_style: 'background: linear-gradient(135deg, #f5d6c8, #ddad9a);color:#612E19;',
        //c28d78;',
        tip: '消费满1500获得',
        cost: 1500,
        needcost: 0,
      }
    ],

    checkList: [{
        type: "online",
        button: "线上",
        num: 0
      },

      {
        type: "store",
        button: "门店",
        num: 0

      }
    ],
    orderList: {
      online: [{
          iconName: "iconquanbu",
          title: "全部订单",
          tourl: 'user/customer/orderList?status=0&sendType=1',
          info: 0
        },

        {
          iconName: "icondaifukuan",
          title: "待付款",
          tourl: 'user/customer/orderList?status=1&sendType=1'
        },
        // {
        //     iconName: "icondaifenxiang",
        //     title: "待分享",
        // },
        {
          iconName: "icondaifahuo",
          title: "进行中",
          tourl: 'user/customer/orderList?status=2&sendType=1'
        },
        {
          iconName: "icondaishouhuo",
          title: "待收货",
          tourl: 'user/customer/orderList?status=3&sendType=1'

        },
        {
          iconName: "icondaipingjia",
          title: "待评价",
          tourl: 'user/customer/orderList?status=4&sendType=1'
        },
        {
          iconName: "icontuihuanhuo",
          title: "退/换货",
          tourl: 'user/sale/returnList'
        }
      ],
      store: [{
          iconName: "iconquanbu",
          title: "全部订单",
          tourl: 'user/sale/storeEvalute?sendType=2'
        },
        {
          iconName: "icondaihexiao",
          title: "待核销",
          // tourl: 'user/sale/waitSaleList'
          tourl: 'user/sale/storeEvalute?sendType=2'
        },
        {
          iconName: "icondaipingjia",
          title: "待评价",
          tourl: 'user/sale/storeEvalute?status=5&sendType=2'
        }
      ]
    },

    serviceList: [{
      iconName: "serv-icon sficon sf-road-map-line",
      bgcolor: "#68a4f2",
      title: "收货地址",
      tourl: 'h5user/customer/receiveAddress'
    }, {
      iconName: "serv-icon sficon sf-heart-2-line1",
      bgcolor: "#9A87F8",
      title: "商品收藏",
      tourl: 'h5user/customer/myCollection'
    },{
      iconName: "mall-icon f7 icondate",
      title: "历史足迹",
      tourl: 'h5user/customer/myHistory'
    }, {
      iconName: "serv-icon sficon sf-store-3-line",
      bgcolor: "#f32971",
      title: "附近门店",
      tourl: 'h5user/nearbyStore'
    }, {
      iconName: "serv-icon sficon sf-lightbulb-flash-line",
      bgcolor: "#adf489",
      title: "活动预约",
      tourl: 'h5shopActivity'
    }, {
      iconName: "serv-icon sficon sf-chat-smile-2-line",
      bgcolor: "#39C594",
      title: "我的评价",
      tourl: 'h5goods/commentDynamic?status=0'
    }, {
      iconName: "serv-icon sficon sf-questionnaire-line",
      bgcolor: "#FDCF48",
      title: "帮助中心",
      tourl: 'h5secondPage?sp_id=102'
    }, {
      iconName: "serv-icon sficon sf-customer-service-2-line",
      bgcolor: "#F57F8D",
      title: "客户服务",
      tourl: 'h5https://sanfu.s2.udesk.cn/im_client/?web_plugin_id=13237'
    }, {
      iconName: "serv-icon sficon sf-settings-line",
      bgcolor: "#c5b9f5",
      title: "设置",
      tourl: '/pages/user/setting/setting'
    }],
    birthImg: '',
    adList: [],
    isPageTop: true, //页面是否处于最顶部
    topBgOpt: 0 //背景色透明值
  },

  onLoad() {
    this.data.failtimes = 0
    this.data.isloading = false
  },
  async onShow() {
    await app.waitSid()
    this.init(true)
    const limitShop = await app.util.checkLimitShop()
    if (limitShop) {
      let index = this.data.serviceList.findIndex(item => item.title === '附近门店')
      index !== -1 && this.data.serviceList.splice(index, 1)
      index = this.data.serviceList.findIndex(item => item.title === '帮助中心')
      index !== -1 && this.data.serviceList.splice(index, 1)
      index = this.data.serviceList.findIndex(item => item.title === '导购分销')
      index !== -1 && this.data.serviceList.splice(index, 1)
      this.setData({
        serviceList: this.data.serviceList,
        limitShop: true
      })
    }
  },
  onHide: function() {},
  onPullDownRefresh: async function() {
    wx.stopPullDownRefresh();
    await app.waitSid()
    this.setData({
      showLoading: true
    })
    this.init(true)

  },

  init: function(type) {
    console.log(type)
    if (type != 'loading') {
      if (this.data.has_login && !type) return;
    }
    if (this.data.isloading) return; //加载中不再执行init
    if (type == undefined || type == 'loading') {
      this.setData({
        showLoading: true
      })
    }
    this.data.isloading = true
    this.getAd()
    this.getUserData();
    if (!this.data.limitShop) {
      this.vetify();
    }
    this.getOrderNum()
    this.getBirthPopup()
    this.getFubi()
  },
  async getOrderNum() {
    //获取订单未读
    let that = this
    let res = await app.reqGet('ms-sanfu-wap-sale/mySanfu', {
      sid: wx.getStorageSync('sid')
    }, res => {})
    if (res.success) {
      that.setData({
        'orderList.online[1].info': res.data.online_payNum,
        'orderList.online[2].info': res.data.online_sendNum,
        'orderList.online[3].info': res.data.online_receiveNum,
        'orderList.online[4].info': res.data.online_evaluateNum,
        'orderList.online[5].info': res.data.online_isReturnNum,
        'checkList[0].num': res.data.online_payNum + res.data.online_sendNum + res.data.online_receiveNum + res.data.online_evaluateNum + res.data.online_isReturnNum,
        'orderList.store[1].info': res.data.shop_cancelNum,
        'orderList.store[2].info': res.data.shop_evaluateNum,
        'checkList[1].num': res.data.shop_cancelNum + res.data.shop_evaluateNum,
      })
    }
    if (!this.data.limitShop) {
      this.getOrderInfo()
    }
  },
  async getOrderInfo() {
    const res = await app.reqGet('ms-sanfu-wap-sale/listOrderInforTip', {
      sid: wx.getStorageSync('sid')
    })
    if (res.success) {
      this.setData({
        orderInfo: res.data
      })
    }
  },
  opencard: function(e) {
    //卡片点击
    wx.navigateToMiniProgram({
      appId: 'wxfe13a2a5df88b058', // 要跳转的小程序的appid
      path: '/pages/showCard_index/showCard_index', // 跳转的目标页面
      extarData: {
        // open: 'auth'
      },
      envVersion: 'release',
    })
  },
  getFubi() {
    app.reqGet('ms-sanfu-wechat-customer/customer/getPoint', {
      sid: wx.getStorageSync('sid')
    }, res => {
      if (res.success) {
        this.setData({
          fbhistoryPoint: res.data.historyPoint,
          fbnextYear: res.data.nextYear
        })
      }
      // console.log(el, '我的福币')
    })
  },
  change_mall_type: function(e) {
    let index = e.currentTarget.dataset.index
    this.setData({
      orderCheckIndex: index
    })
  },
  navigator: async function(e) {
    if (wx.getStorageSync("login")) {
      if (e.currentTarget.dataset.open == true) {
        wx.showToast({
          title: '当前暂无生日礼包',
          icon: 'none',
          duration: 1800
        })
        return
      }
      let moduleTitle = e.currentTarget.dataset.module
      let url = e.currentTarget.dataset.url
      let title = e.currentTarget.dataset.title
      if (moduleTitle) {
        app.sf.track('member_click', {
          url: url,
          text: title
        })
      }
      if (!url) return;
      if (url.indexOf('h5') == 0) {
        let h5web = url.substr(2, url.length - 1)
        if (h5web.indexOf('https://sanfu.s2.udesk.cn/im_client/?web_plugin_id=13237') != -1) {
          // app.util.to_kefu()
          app.toH5('/user/selfHelpService/index')
          return
        }
        app.toH5(h5web)
      } else if (url.indexOf('mini') == 0) {
        return
      } else {
        app.toH5(url)
      }
    } else {
      Auth();
    }
  },
  //跳转订单  商城H5
  to_order: function(e) {
    if (wx.getStorageSync("login")) {
      let url = e.currentTarget.dataset.url
      let title = e.currentTarget.dataset.title
      let path = app.toH5(url)
    } else {
      Auth();
    }
  },
  getUserData: async function() {
    await app.reqGet('ms-sanfu-wap-customer/index/baseInfo', {
      sid: wx.getStorageSync('sid')
    }, successRes => {
      if (successRes.success) {
        let upgradeDate = successRes.data.upgradeDate
        if (upgradeDate) {
          upgradeDate = upgradeDate.replace('年', '.')
          upgradeDate = upgradeDate.replace('月', '.')
          upgradeDate = upgradeDate.replace('日', '.')
          upgradeDate = upgradeDate.split('.')
          upgradeDate[1] = upgradeDate[1] < 10 ? '0' + upgradeDate[1] : upgradeDate[1]
          upgradeDate[2] = upgradeDate[2] < 10 ? '0' + upgradeDate[2] : upgradeDate[2]
          upgradeDate = upgradeDate[0] + '.' + upgradeDate[1] + '.' + upgradeDate[2]
        }
        for (let i in this.data.cardInfo) {
          this.data.cardInfo[i].needcost = (this.data.cardInfo[i].cost - successRes.data.totalMoney).toFixed(
            2)
        }
        for (let i = 0; i < 4; i++) {
          let t;
          if (successRes.data.cardLevelId != this.data.cardInfo[0].cardLevel) {
            t = this.data.cardInfo[3]
            this.data.cardInfo[3] = this.data.cardInfo[2]
            this.data.cardInfo[2] = this.data.cardInfo[1]
            this.data.cardInfo[1] = this.data.cardInfo[0]
            this.data.cardInfo[0] = t
          } else {
            break;
          }

        }
        this.setData({
          'topInfo.headImgUrl': successRes.data.headImgUrl, //头像
          'topInfo.nickName': successRes.data.nickname, //昵称
          'topInfo.cusId': successRes.data.curCusId, //卡号
          'topInfo.sfCardEncryptMsg': successRes.data.sfCardEncryptMsg, //加密串
          'topInfo.cardLevelId': successRes.data.cardLevelId, //卡等级
          'topInfo.discount': successRes.data.discount, //折扣
          'topInfo.fubi': successRes.data.fubi, //福币数
          'topInfo.disendDate': successRes.data.validDate, //卡到期时间
          'topInfo.upgradeDate': upgradeDate, //升级到期时间
          'topInfo.totalMoney': successRes.data.totalMoney, //总消费数
          cardInfo: this.data.cardInfo,
          has_login: true,
        })
      } else {
        app.apploading(app)
        return
      }
    })
    this.setData({
      showLoading: false,
    })
    if (this.data.has_login) {

      this.data.failtimes = 0
    } else {
      if (this.data.failtimes > 3) return
      setTimeout(() => {
        app.apploading(app)
      }, 300)
      this.data.failtimes++
    }
    await app.reqGet('ms-sanfu-wechat-coupon/coupon/countNotUsedCoupons', {
      sid: wx.getStorageSync('sid')
    }, res => {
      if (res.success)
        this.setData({
          'topInfo.countCoupon': res.data,
        })
    })
    let userdata = await app.reqGet('ms-sanfu-wap-customer/index/equity', {
      sid: wx.getStorageSync('sid')
    }, res => {})
    if (userdata.data) {
      this.data.vip_qy[0].info = userdata.data.prizeNum
      this.data.vip_qy[3].info = userdata.data.freeShippingNum
      this.data.vip_qy[4].info = userdata.data.showBirthday == 0 ? -1 : 0
      this.data.vip_qy[5].info = userdata.data.returnGoodsNum
      this.setData({
        'topInfo.existOccupation': userdata.data.existOccupation == 0 ? 0 : 1,
        'topInfo.has_sign': userdata.data.signIn == 1 ? true : false,
        vip_qy: this.data.vip_qy,
      })
    }

    this.data.isloading = false
  },

  //********* */
  // 验证是不是内部员工相关
  //************ */
  vetify: async function() {
    // 检测导购分销按钮
    if (this.data.serviceList[0].title == "导购分销") {
      this.data.serviceList.shift();
    }
    let isStaff
    if (wx.getStorageSync('isStaff')) {
      isStaff = true
    } else {
      let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/staff/verify', {
        sid: wx.getStorageSync('sid')
      }, res => {})
      if (res.success) {
        app.local.set('isStaff', true)
        isStaff = true
      }
    }
    if (isStaff) {
      this.data.serviceList.unshift({
        iconName: "serv-icon sficon sf-a-shopping-bag-2-line1",
        bgcolor: "#FD73A7",
        title: "导购分销",
        tourl: `/pages/distributershop/index/index?sid=${wx.getStorageSync('sid')}&cardid=${wx.getStorageSync('cardid')}`
      })
    }
    this.setData({
      serviceList: this.data.serviceList
    })
  },
  getBirthPopup() {
    if (wx.getStorageSync('hasShowBirth')) return
    app.reqGet('ms-sanfu-wap-customer/showBirthdayPicture', {
      sid: wx.getStorageSync('sid')
    }, res => {
      if (res.success && res.data.isShow) {
        this.setData({
          birthImg: res.data.monthUrl
        })

      }
    })
  },
  toBirth() {
    app.toH5('user/coupon/showBirthGift')
    this.closeBirth()
  },
  closeBirth() {
    wx.setStorageSync('hasShowBirth', true)
    this.setData({
      birthImg: ''
    })
  },
  /* 广告轮播传图点击跳转 */
  getAd() {
    app.reqGet('ms-sanfu-spi-common/listStaticAdDetail', {
      sid: wx.getStorageSync('sid'),
      shoId: wx.getStorageSync('dsho_id'),
      orgId: wx.getStorageSync('orgId'), // 主体标识
      adGroupId: 2021070600
    }, res => {
      if (res.success) {
        this.setData({
          adList: res.data
        })
      } else {
        // app.util.reqFail(res)
      }
    })
  },
  toad: function(e) {
    let url = e.currentTarget.dataset.url
    app.toH5(url)
  },
  toOrderDetail(e) {
    let item = this.data.orderInfo[e.currentTarget.dataset.i]
    app.toH5(`user/customer/orderDetail?tb_ord_id=${item.tbOrdId}&oneceOrgId=${item.orgId}`)
  },
  toTakingCode(e) {
    let item = this.data.orderInfo[e.currentTarget.dataset.i]
    app.toH5(`user/sale/takingCode?tb_ord_id=${item.tbOrdId}&oneceOrgId=${item.orgId}`)
  }

});
