<view class="rate type{{type}}" bind:touchmove="onTouchMove">
  <view wx:if="{{type==1}}" class="rate-item" wx:for="{{ innerCountArray }}" wx:key="index" style="padding-right: {{ index !== count - 1 ? gutter : '' }}">
    <text class="icon {{ index + 1 <= innerValue ? iconClass : voidIconClass }}" style="font-size: {{size}};color: {{ disabled ? disabledColor : index + 1 <= innerValue ? color : voidColor }};" data-score="{{ index }}" bindtap="onSelect"></text>
    <text wx:if="{{ allowHalf }}" class="icon half {{ index +0.5 <= innerValue ? iconClass : voidIconClass }}" style="font-size: {{size}};color: {{ disabled ? disabledColor : index + 0.5 <= innerValue ? color : voidColor }};" data-score="{{ index-0.5 }}" bindtap="onSelect"></text>
  </view>

  <view wx:if="{{type==2}}" class="icon rate-item" wx:for="{{ innerCountArray }}" wx:key="index" style="font-size: {{size}};background: {{ disabled ? disabledColor : index + 1 <= innerValue ? color : '' }};color: {{ disabled ? disabledColor : index + 1 <= innerValue ? '#fff' : '' }};" data-score="{{ index }}" bindtap="onSelect">
    {{index+1}}
  </view>
</view>
