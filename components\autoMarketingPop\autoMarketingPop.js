const app = getApp()
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  /**
   * **自动化营销弹窗组件**
   * 
   * @property {boolean} show 是否展示
   * @property {Number} pageId 页面入口ID
   * @property {String} value 页面入口传参
   * @event {Function} getPopup 获取是否有可展示弹窗后触发
   */
  properties: {
    c: {
      type: String,
      value: ''
    },
    show: {
      type: Boolean,
      value: false
    },
    pageId: {
      type: Number,
      value: ''
    },
    value: {
      type: String,
      value: ''
    }
  },
  data: {
    popupViewType: 0, //1 顶部  2居中
    popupPic: '',
    popupTitle: '',
    configId: '',
    couponList: [],
    popupUrl: '', //跳转链接
    opened: false
  },
  attached() {},
  watch: {
    'show': function(newVal) {
      if (newVal) {
        if (this.data.popupViewType != 0) return
        // this.triggerEvent('getPopup', false);
        this.getCfg()
      }
    },
    'c': function(newVal) {
      if (this.data.show) {
        if (this.data.popupViewType != 0) return
        // this.triggerEvent('getPopup', false);
        this.getCfg()
      }
    },
  },
  detached() {},
  methods: {
    wait(e) {
      return new Promise(res => {
        setTimeout(() => {
          res()
        }, e * 1000)
      })
    },
    // 获取自动化营销方案
    async getCfg() {
      if (this.data.opened) return
      this.data.opened = true
      // 会员 ms-sanfu-wechat-common/automation/getMarketingAutomationCfg
      // 商城 'ms-sanfu-wap-customer-distribution/automation/getMarketingAutomationCfg
      const res = await app.reqGet('ms-sanfu-wap-customer-distribution/automation/getCombinationAutomationCfg', {
        sid: wx.getStorageSync('sid'),
        shoId: wx.getStorageSync('dsho_id'),
        seqNo: this.data.pageId,
        keyValue: this.data.value
      })
      if (res.success) {
        app.sf.track('automation_marketing_popup', {
          track_seqno: this.data.pageId,
          track_cfg_id: res.data.configId
        })
        this.data.configId = res.data.configId // 方案id
        if (res.data.distributionType != 3) { // 优惠券方案过滤
          this.data.marketingId = res.data.marketingId // 营销（优惠券方案)id
          this.data.leastMoney = res.data.leastMoney // 营销最低消费
        } else if (!res.data.popupUrl) return
        this.setData({
          popupViewType: res.data.popupViewType, //1 顶部  2居中
          popupPic: res.data.popupPic,
          popupTitle: res.data.popupTitle,
          popupUrl: res.data.popupUrl,
          couponList: res.data.automationCouponResDtoList
        })
        this.triggerEvent('getPopup', true);
      } else {
        this.data.opened = false
        this.triggerEvent('getPopup', false);
      }
    },
    // 弹窗点击记录
    tapRecord() {
      // 会员 ms-sanfu-wechat-common/
      // 商城 'ms-sanfu-wap-customer-distribution/
      app.reqPost('ms-sanfu-wap-customer-distribution/automation/saveMarketingAutomationReceive', {
        sid: wx.getStorageSync('sid'),
        configId: this.data.configId,
        seqNo: this.data.pageId,
      })
    },
    // 关闭
    close() {
      this.setData({
        popupViewType: -1
      })
    },
    // 一键领取-根据发券方案id
    async receiveCoupon(e) {
      if (this.loading) return
      this.loading = true
      const res = await app.reqPost('ms-sanfu-wechat-coupon/coupon/batchLinkSendCouponBySolutionId', {
        sid: wx.getStorageSync('sid'),
        solutionId: this.data.marketingId,
        shopId: wx.getStorageSync('dsho_id'),
        leastMoney: this.data.leastMoney
      })
      this.loading = false
      if (res.success) {
        wx.showToast({
          title: '优惠券已发送到您的账户',
          icon: 'none',
          duration: 1500
        })
        if (res.data && res.data.length) {
          setTimeout(() => {
            app.subscribeMsg(2010, res.data[0].code)
          }, 1500)
        }
      } else {
        app.util.reqFail(res)
      }
    },
    tapbox() {
      this.tapRecord()
      this.close()
      if (this.data.couponList.length > 0) {
        this.receiveCoupon()
      } else {
        console.log(this.data.popupUrl);
        app.toH5(this.data.popupUrl)
      }
      app.sf.track('automation_marketing_popup_click', {
        track_seqno: this.data.pageId,
        track_cfg_id: this.data.configId
      })
    }
  }
});
