<!--pages/distributershop/services/index/index.wxml-->
<view class="top">
  <view class="search">
    <view class="search-item">
      <view class="uni-icon uni-icon-search"></view>
      <input type="text" placeholder="输入昵称、卡号或货号搜索" value="{{searchkey}}" bindinput="input" data-key="searchkey" bindconfirm="toSearch" confirm-type="search"/>
      <view class="sear-btn" hover-class="hover" hover-start-time="20" hover-stay-time="100" bindtap="toSearch">搜索</view>
    </view>
  </view>
  <view class="tab-box">
    <tabs list="{{list}}" key="name" index="{{currentTab}}" bindchange="changeTab" color="#409eff" tabStyle="color:#909399;min-width:170rpx;" styles="width: 660rpx;"></tabs>
    <view class="tab-box-more" style="{{currentTab==-1?'color:#409eff':''}}" bindtap="toFilter">
      标签筛选
    </view>
  </view>
  <view class="tips">
    <text>客户总数</text>
    <text class="num">{{list[currentTab].data.length!=0 ?list[currentTab].total:0}}</text>
    <view class="add" wx:if="{{qyEntry&&currentTab!=1}}" hover-class="hover" hover-start-time="20" hover-stay-time="100" bindtap="getExternal">添加联系人</view>
    <view class="add" wx:if="{{qyEntry&&currentTab==2}}" bindtap="syncExternal">同步好友</view>
    <!-- <view> {{qyEntry}}</view> -->
  </view>
</view>

<!-- <view bindtap="testqy" style="width: 100%;height: 120rpx;background: #5f4;line-height: 120rpx;text-align: center;">企业微信登录（{{qyLogin}}）</view>
<view bindtap="testqy2" style="width: 100%;height: 120rpx;background: #975aff;line-height: 120rpx;text-align: center;">打开通讯录选人功能</view>
<view bindtap="testqy3" style="width: 100%;height: 120rpx;background: #3ed6ff;line-height: 120rpx;text-align: center;">外部联系人选人接口</view> -->

<view class="member-box">
  <view class="member-item1" wx:for="{{list[currentTab].data}}" wx:key="index" hover-class="hover2" hover-start-time="20" hover-stay-time="100" bindtap="openUser" data-cusid="{{item.cusId}}" bindlongpress="openOptions" data-id="{{item.externalUserId}}">
    <view class="member-item">
      <view wx:if="{{true}}" catchtap="setStar" data-index="{{index}}" class="uni-icon {{item.isStar?'uni-icon-star-filled':'uni-icon-star'}} member-star"></view>
      <image class="member-img" src="{{item.headImgUrl}}"></image>
      <view class="member-content">
        <view class="member-nick">
          <view class="member-q" wx:if="{{item.externalUserId}}">企</view>
          <text style="margin-right: 30rpx;word-break: break-all;">{{item.nickName}}</text>
          <view class="tag1" wx:if="{{item.hasBirthbag}}">顾客生日</view>
          <view class="tag2" wx:if="{{item.hasCoupon}}">优惠券即将过期</view>
        </view>
        <view class="member-behavior">
          <text wx:if="{{item.usersMovingTime}}" style="margin-right: 20rpx;">{{item.usersMovingTime}}</text>
          <text>{{item.usersMovingStr}}</text></view>
      </view>
      <view class="uni-icon uni-icon-arrowright"></view>
    </view>
  </view>
</view>


<view wx:if="{{list[currentTab].has_more != 4}}" class="show_end" bindtap="onReachBottom">
  <view wx:if="{{list[currentTab].has_more == 1}}" class="loadmore-icon"></view>
  <text>{{ loadingText[list[currentTab].has_more] }}</text>
</view>



<uni-popup show="{{filterShow}}" closeName="会员服务-过滤弹框" type="top" zIndex="999" styles="height:80%;" bindclose="closeFilter" maskStyles="background:#000;" duration="260">
  <view class="filter-box">
    <view class="filter-content-box">
      <scroll-view scroll-y class="fillter-content">
        <view class="fillter-item">
          <view>时间</view>
          <view style="font-weight: 500;margin-right: 30rpx;"></view>
        </view>
        <view class="fillter-item" style="font-weight: 500;justify-content: space-evenly;">
          <picker mode="date" bindchange="input" data-key="form.startTime">
            <view style="border: 2rpx solid #ddd;border-radius: 12rpx;padding: 16rpx 30rpx;{{form.startTime?'':'color:#666;'}}">{{form.startTime?form.startTime:'请选择开始时间'}}</view>
          </picker>
          <text>至</text>
          <picker mode="date" bindchange="input" data-key="form.endTime">
            <view style="border: 2rpx solid #ddd;border-radius: 12rpx;padding: 16rpx 30rpx;{{form.endTime?'':'color:#666;'}}">{{form.endTime?form.endTime:'请选择结束时间'}}</view>
          </picker>
        </view>
        <block wx:for="{{labelList}}" wx:key="index">
          <view class="fillter-item">
            <view>{{item.condTypeName}}</view>
          </view>
          <checkbox-group bindchange="checkChange" data-index="{{index}}">
            <label wx:for="{{item.distributionLabelList}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2" style="margin-right: 20rpx;margin-bottom: 10rpx;font-size: 28rpx;display: inline-block;" >
              <checkbox style="transform: scale(0.7);margin-right: -10rpx;" value="{{index2}}" checked="{{item2.check}}"></checkbox>{{item2.condName}}
            </label>
          </checkbox-group>
        </block>
      </scroll-view>
    </view>
    <view class="filter-btn">
      <button class="filter-btn-item" style="background:#ccc;" bindtap="formReset">重置</button>
      <button class="filter-btn-item" bindtap="formSubmit">确定</button>
    </view>
    <sf-loading wx:if="{{filterLoading}}"></sf-loading>
  </view>
</uni-popup>
