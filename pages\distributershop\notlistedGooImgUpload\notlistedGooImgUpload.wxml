<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<view class="share-title">
  <text>商品主图</text>
  <view class="edit" bindtap="toEdit">{{edit?'退出':''}}编辑</view>
</view>
<view lass="upload">
  <view class="reFont"><text style="color: #f00;">*</text>最多上传5张</view>
  <view class="upload-imgs">
    <image bindtap="exchangeGoods" data-type="main" data-i="{{index}}" wx:for="{{gooimg}}" wx:key="index" class="upImg" src="{{utils.jpg2jpeg(item)}}">
      <view wx:if="{{edit&&mainIndex==index}}" class="select f7 iconqiehuan"></view>
      <view wx:if='{{!edit}}' class="uni-icon uni-icon-closeempty delete" bindtap="removeImg" data-i="{{index}}" data-type="gooimg"></view>
    </image>
    <view wx:if="{{gooimg.length < 5}}" class="upImg-icon" bindtap="chooseCropImage" data-type="gooimg">+</view>
  </view>
</view>
<view class="share-title">
  <text>商品详情图</text>
</view>
<view lass="upload">
  <view class="reFont"><text style="color: #f00;">*</text>最多上传8张</view>
  <view class="upload-imgs">
    <image bindtap="exchangeGoods" data-type="content" data-i="{{index}}" wx:for="{{otherimg}}" wx:key="index" class="upImg" src="{{utils.jpg2jpeg(item)}}">
      <view wx:if="{{edit&&othIndex==index}}" class="select f7 iconqiehuan"></view>
      <view wx:if='{{!edit}}' class="uni-icon uni-icon-closeempty delete" bindtap="removeImg" data-i="{{index}}" data-type="otherimg"></view>
    </image>
    <view wx:if="{{otherimg.length < 8}}" class="upImg-icon" bindtap="chooseCropImage" data-type="otherimg">+</view>
  </view>
</view>
<!-- 图片上传时等待的样式 -->
<view class="upImgLoad" wx:if="{{isUploading}}">
  <view class="imgOut1">
    <view>{{uploadtext}}</view>
    <image class="upLoadImg" src="https://img.sanfu.com/img/index_boy/uploading.gif" />
  </view>
</view>


<view class="share-bottom" catchtap="save">提交审核</view>
