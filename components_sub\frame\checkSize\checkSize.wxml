<!--色码弹窗的组件-->
<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<uni-popup new="{{new}}" show="{{ skuShow }}" closeName="色码弹框" zIndex="9999" type="bottom" duration="300" bindclose="close" styles="height:85%;min-height:1000rpx">
  <view class="select-wrap">
    <view class="select-top">
      <image mode="aspectFill" class="goodImg" src="{{utils.jpg2jpeg(imgUrl||tImgUrl)}}" bindtap="openImg"></image>
      <view class="select-top-content">
        <view class="money-wrap">
          <text class="money-tag">¥</text>
          <text class="money">{{selectType==1?groupSalePrice:selectType==2&&showType!=1?qiangPrice:commonPrice}}</text>
          <!--   selectType//0普通  1拼团   2秒杀
            showType 0 加购+下单  1 加购  2下单 -->
        </view>
        <view class="barcode" wx:if="{{barCode}}">商品编码：{{barCode}}</view>
        <view class="select-info">
          {{selColor?"已选：":"请选择"}}
          <block wx:if="{{selColor}}">{{selColor}} {{selSize}}　{{buyNum||0}}件</block>
        </view>
      </view>
      <view class="closeView uni-icon uni-icon-closeempty" bindtap="close">
      </view>
    </view>

    <!-- 促销 -->
    <block wx:if="{{hasPromo&&selColor&&selSize}}">
      <block wx:if="{{promoList.length != 0}}">
        <view wx:for="{{promoList}}" wx:key="index" class="promo-wrap">
          该色码参与{{item.promoName}}
        </view>
      </block>
      <view wx:else class="promo-wrap disable">
        <text class="uni-icon uni-icon-info"> </text>
        该色码暂无促销
      </view>
    </block>
    <view wx:elif="{{tip}}" class="promo-wrap">{{tip}}</view>
    <view class="select-main">
      <sf-loading wx:if="{{!showColorList.length}}"></sf-loading>
      <scroll-view scroll-y class="scroll">
        <view style="height: 10rpx;"></view>
        <!-- 服务保障 -->
        <view wx:if="{{showAfterLimit}}" class="service-wrap">
          <text class="uni-icon uni-icon-info"></text>
          友情提醒：本商品不支持15天无理由退换货
        </view>
        <view wx:else class="service-wrap">
          <image class="img" src="https://img.sanfu.com/sf_access/uploads/PWPYCj1qchGKnWBjqskO24NDfo0soTy6.png"></image>
          服务保障
          <text class="right-text">正品保证，门店退换</text>
        </view>
        <!-- 用户添加的拼团商品列表 -->
        <view class="group-wrap" wx:if="{{groupList.length > 0}}">
          <view class="groupItem" wx:for="{{groupList}}" wx:key="index" wx:if="{{item.selectnum>0}}">
            商品{{index+1}}：{{item.colorName}}，{{item.sizeName}}，x{{item.selectnum}}
            <view class="del uni-icon uni-icon-closeempty" data-index="{{index}}" catchtap="delGroupItem"></view>
          </view>
        </view>
        <block wx:if="{{showPreSaleList.length}}">
          <view class="main-title">现货/预售</view>
          <view class="select-item-wrap">
            <view wx:for="{{showPreSaleList}}" wx:key="index" data-index="{{index}}" class="select-item mini {{selPresale == index? 'active':''}}" bindtap="selectPresale">
              {{item.title}}
            </view>
          </view>
        </block>
        <view class="main-title">{{classId==9801998?'口味':'颜色'}}</view>
        <!-- 小图模式 -->
        <view wx:if="{{showColorList.length>6||isBack}}" class="select-item-wrap">
          <view class="select-item {{selColor == item.color ? 'active':''}} {{item.disable?'disable':''}}" wx:for="{{showColorList}}" wx:key="index" bindtap="selectColor" data-index="{{index}}">
            <image wx:if="{{item.bImg}}" src="{{utils.jpg2jpeg(item.bImg)}}" class="img"></image>
            <text>{{item.color||'默认'}}</text>
            <text wx:if="{{item.preSale}}" class="tag {{item.disable?'disable':'green'}}">预售</text>
            <text wx:elif="{{item.showPromo}}" class="tag {{item.disable?'disable':''}}">促销</text>
            <text wx:elif="{{item.disable}}" class="tag disable">缺货</text>
          </view>
        </view>
        <!-- 大图模式 -->
        <view class="select-item-wrap" wx:else>
          <view wx:for="{{showColorList}}" class="select-item max {{selColor == item.color ? 'active':''}} {{item.disable?'disable':''}}" wx:key="index" bindtap="selectColor" data-index="{{index}}">
            <image class="img" src="{{utils.jpg2jpeg(item.bImg|| 'https://img.sanfu.com/sf_access/uploads/GmFvcfsdLb2KmHQnW1aF3HG9dyk8zGxF.png')}}"></image>
            <text wx:if="{{item.preSale}}" class="tag {{item.disable?'disable':'green'}}">预售</text>
            <text wx:elif="{{item.showPromo}}" class="tag {{item.disable?'disable':''}}">促销</text>
            <text wx:elif="{{item.disable}}" class="tag disable">缺货</text>
            <text wx:if="{{item.bImg}}" catchtap="openImg" data-img="{{item.bImg}}" class="f7 iconfangda"></text>
            <view class="goo-color-item-titlebox">
              {{item.color||'默认'}}
            </view>
          </view>
        </view>
        <block wx:if="{{!(classId==9801998&&showSizeList.length==1&&selSize == showSizeList[0].sizeName)}}">
          <view class="main-title">{{classId==9801998?'大小':'尺码'}}</view>
          <view class="select-item-wrap">
            <view wx:for="{{showSizeList}}" wx:key="index" data-index="{{index}}" class="select-item mini {{selSize == item.sizeName? 'active':''}} {{item.disable?'disable':''}}" bindtap="selectSize">
              <text>{{item.sizeName}}</text>
              <text wx:if="{{item.preSale}}" class="tag {{item.disable?'disable':'green'}}">预售</text>
              <text wx:elif="{{item.showPromo}}" class="tag {{item.disable?'disable':''}}">促销</text>
              <text wx:elif="{{item.disable}}" class="tag disable">缺货</text>
            </view>
          </view>
        </block>
        <view class="amount-wrap">
          <view class="main-title" style="margin-top: 0;">
            <text>购买数量</text>
            <text class="sAmount" wx:if="{{selColor&&selSize&&!disable}}">库存{{(stockAmount > 10)?'充足':stockAmount}}</text>
          </view>
          <stepper wx:if="{{multiple}}" disabled="{{showSizeList[sizeIdx].disable||!selColor}}" value="{{showSizeList[sizeIdx].selectnum}}" integer min="0" max="{{showSizeList[sizeIdx].sAmount}}" step="1" bind:change="stepperNumChange" data-barcode="{{showSizeList[sizeIdx].barcode}}" long-press></stepper>
          <stepper wx:else value="{{ buyNum }}" integer min="1" max="{{showSizeList&&showSizeList[sizeIdx].sAmount||99}}" step="1" bind:change="onNumChange" data-key="buyNum" long-press />
        </view>
        <view wx:if="{{canMultiple}}" class="multiple-wrap" bindtap="openMultiple"><text class="sficon sf-shopping-cart-line"></text>{{multiple?'取消多选':'我要选多款'}}<text class="uni-icon uni-icon-arrowright"></text></view>
        <view style="height: 70rpx;"></view>
      </scroll-view>
    </view>
    <view class="confirm" wx:if="{{!multiple&&(stockAmount==0||disable||(selColor&&selSize&&!barCode))}}">
      <view class="confirm-item">商品缺货</view>
    </view>
    <view class="confirm" wx:elif="{{isBack}}">
      <view bindtap="confirm" class="confirm-item rightBuy">确定</view>
    </view>
    <view class="confirm" wx:else>
      <view wx:if="{{showType == 0||showType == 1}}" bindtap="confirm" class="confirm-item" data-type="addCart">{{selectType==1?'原价加购':'加入购物车'}}</view>
      <view wx:if="{{showType == 0||showType == 2}}" bindtap="confirm" class="confirm-item rightBuy" data-type="buyNow">立即{{selectType==2?'秒杀':selectType==1?'拼团':'购买'}}</view>
    </view>
  </view>
</uni-popup>
<auth id="sanfu-auth"></auth>