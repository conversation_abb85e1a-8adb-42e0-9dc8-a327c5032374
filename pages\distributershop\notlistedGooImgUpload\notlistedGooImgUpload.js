const app = getApp()
Page({
  data: {
    showLoading: false,
    gooimg: [],
    otherimg: [],
    target: '',
    isUploading: false,
    edit: false,
    mainIndex: -1,
    othIndex: -1
  },

  onLoad(options) {
    console.log(__wxConfig);
    this.gooid = options.gooid
  },
  /* 选取剪裁图片 */
  chooseCropImage: function(e) {
    this.data.target = e.currentTarget.dataset.type
    wx.chooseImage({
      count: 1, //总数 1-9
      success: res => {
        if (res.tempFilePaths && res.tempFilePaths[0]) {
          if (!wx.canIUse('cropImage')) {
            // 企微兼容
            this.data[this.data.target].push(res.tempFilePaths[0])
            this.setData({
              [this.data.target]: this.data[this.data.target]
            })
            return
          }
          wx.cropImage({
            cropScale: "1:1",
            src: res.tempFilePaths[0],
            success: res1 => {
              this.data[this.data.target].push(res1.tempFilePath)
              this.setData({
                [this.data.target]: this.data[this.data.target]
              })
            },
            complete: e => {
              console.log(e);
            }
          })
        } else {
          wx.showToast({
            icon: 'none',
            title: '图片获取失败'
          })
        }

      }
    });
  },
  preview: function(e) {
    if (e)
      wx.previewImage({
        current: e, // 当前显示图片的http链接
        urls: [e] // 需要预览的图片http链接列表
      })
  },
  save() {
    if (this.data.gooimg.length == 0) {
      wx.showToast({
        title: '请上传商品主图',
        icon: 'none'
      })
      return
    }
    if (this.data.otherimg.length == 0) {
      wx.showToast({
        title: '请上传商品详情图',
        icon: 'none'
      })
      return
    }
    wx.showModal({
      content: '图片上传后不支持修改，需等待店长审核通过后发布，是否确认提交(24小时内店长未审核默认审核未通过)',
      success: async r1 => {
        if (r1.confirm) {
          // uploadtext
          let allnum = this.data.gooimg.length + this.data.otherimg.length
          let num = 0
          this.mainImgs = []
          this.contentImgs = []
          this.setData({
            isUploading: true
          })
          for (const j of this.data.gooimg) {
            num++
            this.setData({
              uploadtext: `图片上传中...(${num}/${allnum})`
            })
            let url = await this.upLoadImg(j)
            if (url) {
              this.mainImgs.push(url)
            }
          }
          for (const j of this.data.otherimg) {
            num++
            this.setData({
              uploadtext: `图片上传中...(${num}/${allnum})`
            })
            let url = await this.upLoadImg(j)
            if (url) {
              this.contentImgs.push(url)
            }
          }
          this.setData({
            uploadtext: `正在提交...`
          })
          // 提交
          const res = await app.reqPost('ms-sanfu-wap-customer-distribution/goods/uploadImgInfor', {
            sid: wx.getStorageSync('sid'),
            goodsSn: this.gooid,
            mainImgs: this.mainImgs.join(),
            contentImgs: this.contentImgs.join(),
            shoId: wx.getStorageSync('sho_id')
          })
          this.setData({
            isUploading: false
          })
          if (res.success) {
            wx.showToast({
              title: '提交成功',
              icon: 'success',
              duration: 2000
            })
            setTimeout(() => {
              wx.navigateBack({
                delta: 1
              })
            }, 2000)
          } else {
            app.util.reqFail(res)
          }
        }
      }
    })
  },
  /* 传图功能方法 上传*/
  async upLoadImg(e) {
    console.log('upLoadImg', e);
    e = await app.util.compressImage(e, 800)
    let res = await app.reqUpload('ms-sanfu-wap-common/file/image/upload', {
      filePath: e,
      header: {},
      name: 'file',
      formData: {
        moduleCode: 205
      },
      useHighPerformanceMode: true
    })
    if (res.success && res.data) {
      if (!await app.util.getImgSecCheck(res.data)) return false
      return res.data // 获取到的图片URL 
    } else {
      if (await this.showModal('上传失败，是否重试？')) {
        return await this.upLoadImg(e)
      } else {
        return false
      }
    }
  },
  showModal(title) {
    return new Promise(res => {
      wx.showModal({
        title: title,
        success: async r1 => {
          if (r1.confirm) {
            res(true)
          } else {
            res(false)
          }
        },
        fail: () => {
          res(false)
        }
      })
    })
  },
  // 删除已上传图片
  removeImg(e) {
    const i = e.currentTarget.dataset.i
    const type = e.currentTarget.dataset.type
    this.data[type].splice(i, 1)
    this.setData({
      [type]: this.data[type]
    })
  },
  toEdit() {
    // 打开编辑
    if (this.data.edit) {
      this.setData({
        mainIndex: -1,
        othIndex: -1
      })
    }
    this.setData({
      edit: !this.data.edit
    })
  },
  exchangeGoods(e) {
    const i = e.currentTarget.dataset.i
    const type = e.currentTarget.dataset.type
    if (!this.data.edit) {
      if (type == 'main')
        this.preview(this.data.gooimg[i])
      if (type == 'content')
        this.preview(this.data.otherimg[i])
      return
    }
    if (type == 'main') {
      if (this.data.mainIndex == i) {
        this.setData({
          mainIndex: -1
        })
        return

      }
      if (this.data.mainIndex >= 0) {
        let t = this.data.gooimg[i]
        this.data.gooimg[i] = this.data.gooimg[this.data.mainIndex]
        this.data.gooimg[this.data.mainIndex] = t
        this.setData({
          gooimg: this.data.gooimg,
          mainIndex: -1
        })
      } else {
        this.setData({
          mainIndex: i
        })
      }
    } else if (type == 'content') {
      if (this.data.othIndex == i) {
        this.setData({
          othIndex: -1
        })
        return
      }
      if (this.data.othIndex >= 0) {
        let t = this.data.otherimg[i]
        this.data.otherimg[i] = this.data.otherimg[this.data.othIndex]
        this.data.otherimg[this.data.othIndex] = t
        this.setData({
          otherimg: this.data.otherimg,
          othIndex: -1

        })
      } else {
        this.setData({
          othIndex: i
        })
      }
    }
  }
})
