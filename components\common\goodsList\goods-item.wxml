<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<wxs src="./goods-item.wxs" module="goo"></wxs>
<view class="goods goo-item{{theme}} big-{{big}} rowNum-{{rowNum}}" hover-class="hover" hover-start-time="20" hover-stay-time="100" style="{{styles}}" num="{{theme==2&&rowNum}}" change:num="{{goo.numChange}}" h="{{h}}" change:h="{{goo.hChange}}" margin="{{theme==2&&margin}}" change:margin="{{goo.marginChange}}" theme="{{theme}}" change:theme="{{goo.themeChange}}">
  <view style="font-size:0;{{imgboxStyle}};" class="img">
    <img lazy-load mode="aspectFill" class="goo-img" src="{{utils.jpg2jpeg(img)}}" style="{{imgStyle}}">
      <view>
        <slot name="img"></slot>
      </view>
    </img>
  </view>
  <view class="goo-msgs" style="{{gooMainStyle}}">
    <slot name="beforeTitle"></slot>
    <view class="goo-name {{titleLine==1?'title-line1':''}}">
      <text class="title-line2">
        <template wx:if="{{(theme==2&&rowNum==3)}}" is="name-tag" data="{{groupBuyPrice,showTag,isQiangPresell,qiangPrice,memberPrice}}"></template>
        <slot name="beforeTitle2"></slot>{{utils.replaceg(title,'三福', '')}}
      </text>
      <slot name="goodAttr"></slot>
    </view>

    <view class="txtCon">
      <slot name="afterTitle"></slot>
      <view class="goo-price-box" style="{{priceStyle}}">
        <!-- 商品活动类型 -->
        <template wx:if="{{!(theme==2&&rowNum==3)}}" is="name-tag" data="{{groupBuyPrice,showTag,isQiangPresell,qiangPrice,memberPrice}}"></template>
        <!-- 价格标签 -->
        <view class="goo-price-con">
          <text class="rmbTxt">¥</text>
          <text class="price">{{!dsho_id?scPrice: groupBuyPrice>0?groupBuyPrice:qiangPrice>0?qiangPrice :memberPrice>0?memberPrice:salePrice>0?salePrice:scPrice}}</text>
        </view>
        <block wx:if="{{dsho_id}}">
          <text wx:if="{{groupBuyPrice>0&&groupBuyPrice<salePrice}}" class="del-price ">¥{{groupBuyPrice<salePrice?salePrice:''}}</text>
          <text wx:elif="{{qiangPrice>0&&qiangPrice<salePrice}}" class="del-price ">¥{{qiangPrice<salePrice?salePrice:''}}</text>
          <text wx:elif="{{memberPrice>0&&memberPrice<scPrice}}" class="del-price ">¥{{memberPrice<scPrice?scPrice:''}}</text>
          <text wx:elif="{{!qiangPrice&&!groupBuyPrice&&!memberPrice&&scPrice>salePrice&&salePrice>0}}" class="del-price">¥{{scPrice}}</text>
        </block>
        <slot name="afterPrice"></slot>
      </view>
      <slot name="bottom"></slot>
      <slot name="addBtn" style="{{addBtnStyle}}"></slot>
    </view>

  </view>
  <slot></slot>
  <observer start="{{isObserver}}" bindobserver="observer"></observer>
</view>
<template name='name-tag'>
  <text wx:if="{{groupBuyPrice > 0&&showTag}}" class="goodTitleTag">拼团</text>
  <text wx:elif="{{!isQiangPresell&&qiangPrice > 0&&showTag}}" class="goodTitleTag">秒杀</text>
  <!-- 一口价标签 -->
  <text wx:elif="{{memberPrice>0&&!qiangPrice &&!groupBuyPrice}}" class="goodTitleTag">会员价</text>
</template>
<!-- <template name='yikoujia'>
  <view style="transform: scale(0.85);position:absolute;left:-8rpx;bottom:-10rpx;z-index:99;width: 178rpx;height: 180rpx;overflow: hidden;padding-left: 4rpx;padding-top: 2rpx;">
    <view style="width: 160rpx;height: 160rpx;background: #fdc6db;border-radius: 50%;box-shadow: 0 0 6rpx 2rpx #bbb;display: flex;">
      <view style="width: 156rpx;height: 156rpx;background: #FB8CB7;border-radius: 50%;line-height: 1;z-index: 666;margin-left: 2rpx;;margin-top: 2rpx;border: 6rpx solid #fdc6db;">
        <view style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;flex-direction: column;">
          <view style="color: #f6f6f6;font-size: 22rpx;margin-top: -2rpx;margin-bottom: 18rpx;font-family: 'Microsoft YaHei', '黑体', '宋体', sans-serif;font-weight: 700;">会员专享</view>
          <view style="font-size: 50rpx; font-weight: 700; color: #F6F6F6;margin-top: -4rpx; font-family: 'Microsoft YaHei', '黑体', '宋体', sans-serif;">{{memberPrice}}</view>
        </view>
      </view>
    </view>
    <view style="position: absolute;width: 46rpx;height: 46rpx;background: #fdc6db;transform: rotate(35deg) skew(-20deg);left: 62rpx;bottom: 6rpx;z-index: 2;box-shadow: 0 0 6rpx 2rpx #d5d5d5;"></view>
  </view>
</template> -->
