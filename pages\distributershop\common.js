const app = getApp()

/** 关闭二维码弹窗 */
const closeQR = function() {
  let that = this
  that.setData({
    showqrcode: !that.data.showqrcode
  })
  this.setData({
    showLoading: false
  })
}
/**
 * 工作台埋点接口
 * @param flag 模块标识[1单品推荐/2组合推荐/3活动推荐/4分销微课堂/5]
 */
const workTrace = async function(flag) {
  let res = await app.reqPost('ms-sanfu-wap-customer-distribution/distribution/work/trace', {
    sid: wx.getStorageSync('sid'),
    flag
  })
}
/**
 * 用户分享点赞收藏接口
 * @param operateType 操作标识[1分享/2查看/3收藏/4点赞]
 * @param scene 场景[1单品推荐/2组合推荐/3活动推荐/4分销微课堂5首页]
 * @param id 场景对应方案ID
 * @param fromCusId 方案来源作者卡号
 */
const traceRecord = async function(data = {}) {
  let res = await app.reqPost('ms-sanfu-wap-customer-distribution/distribution/trace/record', {
    sid: wx.getStorageSync('sid'),
    fromCusId: wx.getStorageSync('discard'),
    ...data
  })
  return res
}
// 查看统计
const viewer = async function(scene, id, card) {
  let that = this
  let res = await that.traceRecord({
    fromCusId: card,
    id: id,
    operateType: 2,
    scene: scene
  })
  res = res.data
  if (res && !res.success) {
    wx.showToast({
      title: res.msg,
      icon: 'none'
    })
  }
}
// 转发分享统计
const transfer = async function(id, scene, gooId) {
  let that = this
  console.log({
    id: id,
    operateType: 1,
    scene: scene,
    gooId: gooId
  })
  let res = await that.traceRecord({
    id: id,
    operateType: 1,
    scene: scene,
    gooId: gooId
  })
  if (!res.success) {
    wx.showToast({
      title: res.msg,
      icon: 'none'
    })
  }
}

// 转发分享统计
const transferDetail = async function(id, scene, gooId) {
  let that = this
  console.log({
    id: id,
    operateType: 1,
    scene: scene,
    gooId: gooId
  })
  let res = await that.traceRecord({
    id: id,
    operateType: 1,
    scene: scene,
    gooId: gooId
  })
  if (!res.success) {
    wx.showToast({
      title: res.msg,
      icon: 'none'
    })
  }
}
const saveImage = function() {
  // console.log(1111)
  let that = this
  wx.saveImageToPhotosAlbum({
    filePath: that.data.tmppath,
    success: function(data) {
      wx.showToast({
        title: "保存成功"
      })
    },
    fail(err) {
      wx.hideLoading()
      wx.hideToast()
      if (err.errMsg === "saveImageToPhotosAlbum:fail:auth denied" || err.errMsg === "saveImageToPhotosAlbum:fail auth deny" || err.errMsg === "saveImageToPhotosAlbum:fail authorize no response") {
        // 这边微信做过调整，必须要在按钮中触发，因此需要在弹框回调中进行调用

        wx.showModal({
          title: '提示',
          content: '需要您授权保存相册',
          success: res => {
            if (res.confirm)
              wx.openSetting({
                success(settingdata) {
                  console.log("settingdata", settingdata)
                  if (settingdata.authSetting['scope.writePhotosAlbum']) {
                    wx.showToast({
                      title: '获取权限成功,再次点击图片即可保存',
                      icon: 'none',
                      duration: 3000

                    })
                  } else {
                    wx.showToast({
                      title: '获取权限失败，将无法保存到相册哦~',
                      icon: 'none',
                      duration: 3000

                    })

                  }
                },

              })
          }
        })
      } else if (err.errMsg == "saveImageToPhotosAlbum:fail cancel") {
        wx.showToast({
          title: '已取消保存~',
          icon: 'none',
          duration: 2000

        })
      } else {
        wx.showToast({
          title: '保存失败：' + err.errMsg,
          icon: 'none',
          duration: 5000
        })
      }
      console.log(err, that.data.tmppath)
    }
  })
}
const updateShareCount = function(key, id, startCount) {
  console.log(getCurrentPages()[getCurrentPages().length - 1])
  let that = this
  let share = wx.getStorageSync(key) || []
  let item = share.filter(el => el.id === id)[0] || {}
  let noItem = share.filter(el => el.id !== id)
  let count = 0
  if (item.id) {
    item.count = item.count + 1
    wx.setStorageSync(key, [...noItem, item])
    count = item.count
  } else {
    wx.setStorageSync(key, [...noItem, {
      id,
      count: startCount + 1
    }])
    count = startCount + 1
  }
  let list = []
  if (key === 'share_single') {
    that.data.singles.map(el => {
      if (el.recommendId === id) {
        el.shareCnt = count
      }
    })
    that.setData({
      singles: that.data.singles
    })
  }
  if (key === 'share_group') {
    if (that.data.currentTab === 0) {
      that.data.recomonds.map(el => {
        if (el.recommendId === id) {
          el.shareCnt = count
        }
      })
      that.setData({
        recomonds: that.data.recomonds
      })
    } else {
      that.data.recomonds_my.map(el => {
        if (el.recommendId === id) {
          el.shareCnt = count
        }
      })
      that.setData({
        recomonds_my: that.data.recomonds_my
      })
    }

  }
  if (key === 'share_act') {
    that.data.activitys.map(el => {
      if (el.actId === id) {
        el.shareCnt = count
      }
    })
    that.setData({
      activitys: that.data.activitys
    })
  }
}
const commos = {
  saveImage,
  workTrace,
  closeQR,
  transfer,
  viewer,
  traceRecord,
  transferDetail,
  updateShareCount
}
module.exports = commos
