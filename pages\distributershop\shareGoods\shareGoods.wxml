<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<view class="share-title">
  <text style="min-width: 80rpx;">商品</text>
  <view class="searchbox">
    <text class="uni-icon uni-icon-search" style="font-size:36rpx;color:#999;margin-top:6rpx;margin-right:6rpx;"></text>
    <input placeholder-style="color:#999;" bindinput="input" bindconfirm="search" type="text" data-name="goodsId" value="{{goodsId}}" placeholder="请输入6位货号" confirm-type="go" />
    <text wx:if="{{goodsId}}" class="uni-icon uni-icon-closeempty" style="font-size: 40rpx;padding: 0 12rpx;" bindtap="clearinput" data-name="goodsId"></text>
    <input placeholder-style="color:#999;" bindinput="input" bindconfirm="search" type="text" data-name="searchShoId" value="{{searchShoId}}" placeholder="区域号" style="flex: 0 1 auto;width: 120rpx;background: #f9d7fe;text-align: center;" confirm-type="go" maxlength="6" disabled/>
    <view class="confirm" bindtap="search" style="font-weight: 700;">打开商品</view>
  </view>
</view>
<view class="goods-item" wx:if="{{viewGoods}}">
  <view class="imgs" wx:if="{{viewGoods.goodsPhotoList.length> 0}}">
    <swiper circular duration="500" bindchange="imgSwipe">
      <swiper-item wx:for="{{viewGoods.goodsPhotoList}}" wx:key="index">
        <image src="{{utils.jpg2jpeg(item.bigImg)}}" bindtap="openImg" data-index="{{index}}" mode="aspectFill" />
      </swiper-item>
    </swiper>
    <view class="imgsCount">
      {{imgIndex+1}}/{{viewGoods.goodsPhotoList.length}}
    </view>
  </view>
  <view class="right">
    <text>
      {{viewGoods.goodsName}}
    </text>
  </view>
</view>
<!-- 预览 -->
<view class="share-title top">
  <text>预览</text>
  <view class="toshare" bindtap="toShare" data-t="{{true}}">
    <view>顺序分享</view>
  </view>
  <button open-type="share" class="toshare" data-type="qy" style="padding:16rpx 24rpx;">社群分享</button>
  <view class="reset" bindtap="toReset">
    <view>重置</view>
  </view>
  <view class="reset" style="background: #74c3ff;margin-top: 10rpx;" bindtap="changePreview" data-type="path">
    <view>{{preView.path?'修改':'新增'}}链接</view>
  </view>
</view>
<view style="padding: 20rpx;">分享链接：<text style="word-break: break-all;" user-select>{{preView.path}}</text></view>
<view class="previewBox">
  <view class="title" bindtap="changePreview" data-type="title">{{preView.title||'暂无标题，点此修改'}}</view>
  <view class="imgbox {{preView.imageUrl?'':'imgbox1'}}" bindtap="upLoadImg">
    <image wx:if="{{preView.imageUrl}}" src="{{utils.jpg2jpeg(preView.imageUrl)}}" mode="widthFix"></image>
  </view>
</view>
<view class="share-title top" style="top:110rpx">
  <text>分享门店</text>
  <view class="shopInput">
    <input type="text" bindinput="input" bindconfirm="addShop" data-name="inputShopId" value="{{inputShopId}}" confirm-type="go" />
    <view bindtap="addShop">添加</view>
  </view>
</view>
<view class="shop" wx:for="{{shareShops}}" wx:key="index">
  <view class="title">{{item}}</view>
  <view wx:if="{{index!=0}}" class="uni-icon uni-icon-arrowthinup" style="width: 70rpx;height: 70rpx;line-height: 70rpx;" bindtap="shopUp" data-i="{{index}}"></view>
  <view style="width: 70rpx;height: 70rpx;line-height: 70rpx;text-align: center;">
    <view wx:if="{{index!=shareShops.length-1}}" class="uni-icon uni-icon-arrowthindown" bindtap="shopDown" data-i="{{index}}"></view>
  </view>
  <button open-type="share" class="share" wx:if="{{preView.path}}" data-i="{{index}}">分享</button>
  <view class="delete" bindtap="deleteShop" data-i="{{index}}">删除</view>
</view>
<uni-popup maskClick="{{false}}" closeName="分享-修改{{preType=='title'?'标题':preType=='path'?'链接(小程序链接)':'链接(H5链接)'}}" style="z-index: 999;" type="center" show="{{showPrePop}}">
  <view class="popup-box">
    <view class="title">
      <text>修改{{preType=='title'?'标题':preType=='path'?'链接(小程序链接)':'链接(H5链接)'}}</text>
      <view wx:if="{{preType=='path'}}" class="reset" style="background: #ff9ce8;display: inline;" bindtap="changePreview" data-type="H5path">输入H5链接</view>
    </view>
    <textarea type="text" bindinput="input" bindconfirm="savePre" data-name="preInput" value="{{preInput}}" confirm-type="go" maxlength="500"></textarea>
    <view class="btns">
      <view bindtap="savePre" data-t="{{true}}">关闭</view>
      <view style="background: #74c3ff;" bindtap="savePre">保存</view>
    </view>
  </view>
</uni-popup>
<uni-popup maskClick="{{false}}" closeName="分享-门店分享弹框" style="z-index: 999;" type="center" show="{{showSharePop}}">
  <view class="popup-box">
    <view class="title">当前分享店号：<text style="color: #f00;font-weight: 700;">{{shareShops[shareIndex]}}</text>（{{shareIndex+1}}/{{shareShops.length}}）</view>
    <view class="btns">
      <view bindtap="toShare" data-t="{{false}}">关闭</view>
      <view wx:if="{{shareIndex<shareShops.length-1}}" bindtap="jumpShop">跳过</view>
      <button open-type="share" class="share" data-i="{{shareIndex}}" data-type="{{2}}">分享</button>
    </view>
  </view>
</uni-popup>
<text style="padding: 20rpx;font-size: 26rpx;color: #adadad;">说明：
  1. 粉色GZW为商品上架门店，仅商品搜索用，与分享门店无关
  2. 添加门店可以任意字符隔开添加多个
  3. 显示图片长宽比是 5:4
  4. 点击预览位置可修改相应内容
  5. H5链接禁止使用首页，首页请用小程序链接【/pages/index/index】
  
  
</text>
<sf-loading full wx:if="{{showLoading}}"></sf-loading>
