@import "/font/icon_f7.wxss";
@import "/font/uni-icon.wxss";

.tigger {
  position: absolute;
  top: 60vh;
  left: 0;
  width: 1px;
  height: 1px;
}

.top-btn-box {
  width: 72rpx;
  height: 72rpx;
  position: fixed;
  z-index: 99;
  right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 0 30rpx #c1c1c1;
  background: rgba(255, 255, 255, 0.5); 
  backdrop-filter: blur(2px); /* 背景模糊效果 */
}

.top-btn-box .top-btn-item {
  overflow: hidden;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
}

.top-btn-box .top-btn {
  font-size: 38rpx;

}

.dis-btn-box {
  width: 90rpx;
  height: 90rpx;
  position: fixed;
  z-index: 99;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #999;
  opacity: 0.7;
  font-size: 28rpx;
  color: #fff;
  margin-bottom: env(safe-area-inset-bottom);
}

.dis-btn-box>.dis-item {
  display: flex;
  line-height: 1.2;
}

.close {
  position: absolute;
  right: -16rpx;
  top: -20rpx;
  color: #333;
  font-size: 40rpx;
  opacity: 0.6;
}

.close2 {
  color: #666;
  font-size: 40rpx;
  /* font-weight: 700; */
  position: fixed;
  right: 4rpx;
  z-index: 101;
  opacity: 0.6;
}

.qy-btn-box {
  width: 100rpx;
  height: 100rpx;
  position: fixed;
  z-index: 100;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #fff;
  margin-bottom: env(safe-area-inset-bottom);
}

.sph-box {
  position: fixed;
  width: 96rpx;
  height: 106rpx;
  background: linear-gradient(180deg, #ff7c9b, #f02e72);
  z-index: 100;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #fff;
  margin-bottom: env(safe-area-inset-bottom);
  border-radius: 20rpx;
  padding: 12rpx 0;
  opacity: 0.75;
  margin-left: -48rpx;
  margin-top: -50rpx;
}

.sph-box .title {
  font-size: 20rpx;

}

.sph-box .icon1 {
  background: #fff;
  width: 50rpx;
  height: 50rpx;
  border-radius: 100rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10rpx;
}

@keyframes ani {
  0% {
    height: 8rpx;
  }

  50% {
    height: 30rpx;
  }

  100% {
    height: 8rpx;
  }
}

.sph-box .icon1 view {
  width: 6rpx;
  height: 30rpx;
  background: linear-gradient(180deg, #fd6c90, #f02e72);
  border-radius: 6rpx;
}

.sph-box .icon1 view:nth-of-type(1) {
  animation: ani 0.65s infinite 0s;
}

.sph-box .icon1 view:nth-of-type(2) {
  animation: ani 0.5s infinite 0s;
}

.sph-box .icon1 view:nth-of-type(3) {
  animation: ani 0.8s infinite 0s;
}

.sph-box .icon2 {
  font-size: 56rpx;
  border-radius: 100rpx;
  color: #fff;
  transform: rotateY(180deg) rotateZ(-45deg);
  margin: -4rpx 0;
}
