<view class='alet_container' wx:if="{{show}}" catchtouchmove="preventD">
  <view class='tip_text_container'>
    <view class='tip_text'>
      <text>{{tip[tip_index]}}</text>

      <view  wx:if="{{tip_index==6}}" style="text-align: left;font-size: 28rpx;line-height: 1.3;word-break: break-all;"><text style="color: blue;" bindtap="toH5" data-url="user/rule/registrationAgreement">《三福会员注册协议》</text>、<text style="color: blue;" bindtap="toH5" data-url="user/rule/privacyPolicy">《三福隐私政策》</text></view>
    </view>
    <view></view>
    <view class='btn-box'>
      <block wx:if="{{tip_index==0}}">
        <button wx:if="{{showCancel}}" class='button' plain hover-class="none" bindtap="cancel">
          <view class="left">暂不登录</view>
        </button>
        <button class='button' bindgetuserinfo="confirm" open-type="getUserInfo" plain hover-class="none">
          <view class='right'>立即登录</view>
        </button>
      </block>
      <block wx:elif="{{tip_index==2}}">
        <button class='button' plain hover-class="none" bindtap="cancel">
          <view class="left">取消</view>
        </button>
        <button class='button' bindgetuserinfo="confirm" open-type="getUserInfo" plain hover-class="none">
          <view class='right'>确定</view>
        </button>
      </block>
      <block wx:elif="{{tip_index==4}}">
        <button wx:if="{{showCancel}}" class='button' plain hover-class="none" bindtap="cancel">
          <view class="left">取消</view>
        </button>
        <button class='button' bindgetphonenumber="confirm" open-type="getPhoneNumber" plain hover-class="none">
          <view class='right'>确定</view>
        </button>
      </block>
      <block wx:elif="{{tip_index==6}}">
        <button wx:if="{{showCancel}}" class='button' plain hover-class="none" bindtap="cancel">
          <view class="left">取消</view>
        </button>
        <button class='button' bindtap="confirm" plain hover-class="none">
          <view class='right'>同意并继续</view>
        </button>
      </block>
      <block wx:else>
        <button class='button' plain hover-class="none" bindtap="cancel">
          <view class="left">取消</view>
        </button>
        <button class='button' catchtap="confirm" plain hover-class="none">
          <view class='right'>确定</view>
        </button>
      </block>
    </view>
    <view wx:if="{{tip_index==1}}" style="text-decoration: underline;margin-bottom: -36rpx;margin-top: 20rpx;color: #606060;" catchtap="toUserInfo">已绑定请点此补充会员信息</view>
  </view>
</view>