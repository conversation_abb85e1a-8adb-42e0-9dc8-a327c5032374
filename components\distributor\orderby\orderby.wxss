/* components/distributor/orderby.wxss */
.order-by {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70rpx;
  line-height: 70rpx;
  box-shadow: 0px 3px 3px #aaa;
  position: relative;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background: white;
}

.order-by .item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.order-content {
  position: absolute;
  top: 70rpx;
  left: 0;
  width: 100%;
  height: 750rpx;
  z-index: 999;
  background: white;
  padding-top: 10rpx;
  box-shadow: 0px 3px 3px #aaa;
  animation: .3s tipMove;
  border-top: #ececec 2rpx solid;
}

.order-content .scroll{
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding-bottom: 180rpx;

}

.item-branch {
  border-radius: 5px;
  color: white;
  margin: 0px 5px;
  margin-bottom: 5px;
  padding: 2px 8px;
  font-size: 13px;
  background: linear-gradient(to right, #FF87A5, #FF6E93);
  height: 20px;
  line-height: 20px;
}

@keyframes tipMove {
  0% {
    opacity: 0;
    max-height: 0;
  }

  100% {
    opacity: 1;
    max-height: 999px;
  }
}