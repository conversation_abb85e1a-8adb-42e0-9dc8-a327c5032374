<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<wxs src="./moreCart.wxs" module="cart"></wxs>
<view wx:if="{{viewList.length>0}}" class="more-btn" hover-class="hover" hover-start-time="20" hover-stay-time="100" bindtap="openPop">查看更多商品</view>
<uni-popup type="center" show="{{showPop}}" closeName="购物车-更多商品弹框" zIndex="999" maskStyles="" bind:close="closePop">
  <view class="popup-container">
    <view class="top">
      <view class="title">添加更多商品</view>
      <view hover-class="hover3" hover-start-time="20" hover-stay-time="100" class="uni-icon uni-icon-closeempty"  bindtap="closePop"></view>
    </view>
    <scroll-view scroll-y class="scroll-wrap">
      <block wx:for="{{viewList}}" wx:for-item="goods" wx:for-index="index5" wx:key="index5" wx:if="{{!goods.isInvalid}}">
        <view class="yd-flexbox" style="margin-bottom: 20rpx;">
          <template data="{{goods,index5,isShare,type}}" id="{{index5}}" is="goodsItem" />
          <view wx:if="{{goods.disable!=2}}" class="add-goods" hover-class="hover" hover-start-time="20" hover-stay-time="100" bindtap="addCart" data-i="{{index5}}">
            <text class="uni-icon uni-icon-plusempty"></text>
          </view>
        </view>
      </block>
      <view style="height: 20rpx;"></view>
    </scroll-view>
  </view>
</uni-popup>



<template name="goodsItem">
  <view class="goods">
    <view wx:if="{{goods.promoName&&goods.disable==0}}" class="promotionTag">促销：{{ goods.promoName }}</view>
    <view class="radio">
      <text wx:if="{{goods.disable==2}}" style="color: #b5b5b5; white-space: nowrap">缺货</text>
      <text wx:elif="{{goods.disable==1}}" style="color: #b5b5b5; white-space: nowrap">限制</text>
    </view>
    <image lazy-load style="width: 160rpx; height: 160rpx; position: relative; background: #eee; border-radius: 12rpx" src="{{ goods.sImg ? utils.jpg2jpeg(goods.sImg) : 'https://img.sanfu.com/sf_access/uploads/GmFvcfsdLb2KmHQnW1aF3HG9dyk8zGxF.png'}}">
      <view wx:if="{{goods.isQiang&&index5>=0}}" style="position: absolute; font-size: 24rpx; color: white; opacity: 0.6; background: #f00; text-align: center; width: 160rpx; height: 160rpx; left: -80rpx; bottom: -80rpx; padding: 30rpx 0 80rpx 70rpx; border-radius: 0 50% 0 0">秒杀</view>
    </image>
    <view class="goods-right">
      <view class="title">{{ goods.goodsName }} {{ goods.goodsId }}</view>
      <view class="goods-price-wrap">
        <view class="main-price">
          <text class="price-icon">¥</text>
          <text>{{cart.getInt(goods.salePrice)}}</text>
          <text wx:if="{{cart.getDecimal(goods.salePrice)}}" class="decimal">{{cart.getDecimal(goods.salePrice)}}</text>
        </view>
      </view>
    </view>
  </view>
</template>