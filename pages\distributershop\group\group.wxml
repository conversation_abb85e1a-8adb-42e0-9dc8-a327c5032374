<!--pages/distributershop/group/group.wxml-->
<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<sf-loading wx:if="{{showLoading}}" full />
<view class='main'>
  <view class="swiper-tab">
    <view class="swiper-tab-list {{currentTab==1 ? 'on' : ''}}" data-current="1" bindtap="swichNav">我的组合商品</view>
    <view class="swiper-tab-list {{currentTab==0 ? 'on' : ''}}" data-current="0" bindtap="swichNav">推荐组合商品</view>
  </view>
  <view class="swiper-box">
    <swiper current="{{currentTab}}" duration="300" style="width: 100%;height: 100%;position: absolute;left: 0;top:0;bottom: 0;right: 0;" bindchange="bindChange">

      <swiper-item wx:for="{{viewList}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2">
        <view class="view-types">
          <view wx:for="{{item2.combType}}" wx:key="index" style="{{index==item2.type_index?'background:#fe6b8f':''}}" bindtap="changeCombType" data-index="{{index}}">{{item.name}}</view>
        </view>
        <order-by id="order{{index2}}" groups="{{item2.groups}}" bind:orderBy="orderBy" />
        <scroll-view style="height:100%;display:flex;align-item:center;justify-content:center;padding-bottom:140rpx;box-sizing:border-box;background: #F8F8F8;" scroll-y="true" scroll-into-view="bottom" scroll-with-animation="true" bindscrolltolower="loadData">
          <view class='coupon-list'>
            <block wx:for="{{item2.data}}" wx:key="index">
              <!-- 组合商品列表item模板 -->
              <view class="group-goods">
                <image lazy-load src="{{utils.jpg2jpeg(item.coverUrl)}}" bindtap="toDetail" data-item="{{item}}" class="bg"></image>
                <view class="autor">
                  <view class="autor-name">{{item.nickName || '三福君'}}</view>
                  <view class="date">{{item.publishTime}}</view>
                </view>
                <view class="article-title">
                  {{item.recommendTitle}}
                </view>
                <view class="viewer">
                  <!-- <view class="left"> -->
                  <view class="viewer-count">
                    <text class="f7 iconDG-dianzankong"></text>
                    <text>{{item.likeCnt|| item.liked || 0}}</text>
                  </view>
                  <view class="viewer-count">
                    <text class="f7 iconDG-shoucangkong"></text>
                    <text>{{item.collectCnt ||item.collected || 0}}</text>
                  </view>
                  <view class="viewer-count">
                    <text class="f7 iconDG-liulan"></text>
                    <text>{{item.viewCnt || 0}}</text>
                  </view>
                  <!-- </view> -->

                  <view class="viewer-count" bindtap="showShareCode2" data-shareItem="{{item}}">
                    <text class="f7 iconDG-fenxiang" style="color:#F63979"></text>
                    <text>{{item.shareCnt || 0}}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>
          <view wx:if="{{item2.has_more != 4&&!showLoading}}" class="show_end" bindtap="loadData" style="margin:0;">
            <view wx:if="{{item2.has_more == 1}}" class="loadmore-icon"></view>
            <text>{{ loadingText[item2.has_more] }}</text>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</view>

<block wx:if="{{currentTab==1}}">
  <view class="add-group-goods" bindtap="toEdit">
    <text class="uni-icon uni-icon-plusempty" style="font-size:40px;color:#FF6E93;padding:0 2px;"></text>
  </view>
  <view class="caogaoxiang" bindtap="showCaogao">
    <text class="f7 iconDG-caogaoxiang" style="font-size:25px;color:#EE1137;padding:0 2px;"></text>草稿箱
    <text class="caogao-num">{{caogaoNum}}</text>
  </view>
</block>

<uni-popup show="{{ isShowCaogao }}" closeName="组合商品-编辑弹框" zIndex="999" type="bottom" bind:close="closeCaogao">
  <view class="caogao-pop" style="background: #fff;">
    <scroll-view scroll-y>
      <block wx:for="{{caogaolist}}" wx:key="index">
        <view class="form-row">
          <view class="column">
            <text>{{item.recommendTitle}}</text>
          </view>
          <view class="operators">
            <text class="preview" bindtap="previewCaogao" data-item="{{item}}">预览</text>
            <text class="edit" bindtap="editCaogao" data-item="{{item}}">编辑</text>
            <text class="delete" bindtap="delCaogao" data-item="{{item}}">删除</text>
          </view>
        </view>
      </block>
    </scroll-view>

    <view class="go-back" bindtap="closeCaogao">
      返回
    </view>
  </view>
</uni-popup>



<share id="sanfu-share" />
<sanfu-alert id="sanfu-alert" />
