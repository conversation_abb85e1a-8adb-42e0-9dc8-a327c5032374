/* components/common/share.wxss */
@import "/font/icon_f7.wxss";
@import "/font/uni-icon.wxss";

.button {
  padding: 0;
  margin: 0;
  line-height: 1;
  background: white;
}

.button::after {
  border: 0px solid;
}

.alert {
  height: 230rpx;
  background: white;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.alert .top {
  flex: 1;
}

.alert .top .item {
  height: 100%;
  flex: 1;
  justify-content: center;
  flex-direction: column;
  display: flex;
  align-items: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}


.share-ways {
  font-size: 24rpx;
  margin: 10rpx 0;
}

.share-icon {
  height: 50px;
  width: 50px;
  line-height: 50px;
  border-radius: 30px;
  font-size: 25px;
  color: white;
  background: rgba(0, 0, 0, 0.5)
}

.alert .bottom {
  border-top: 1px solid #eee;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  color: #555;
  font-size: 26rpx;
}

