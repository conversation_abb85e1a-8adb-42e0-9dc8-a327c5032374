/* pages/couponcenter/memo/memo.wxss */
button {
  border: 0 solid !important;
  padding: 0;
  border-radius: 0px;
}

.to-use {
  width: 100%;
  text-align: center;
  color: #666;
  font-size: 13px;
  flex: 1;
}

.to-use text {
  display: block;
  width: 90%;
  margin: 0 auto;
  height: 35px;
  line-height: 35px;
  text-align: center;
  background: rgba(255, 255, 255, .1);
  box-shadow: 0 0 1px #999;
  border-radius: 1rem;

}



.tip_text_container {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 15px;
}

.tip_text_container .title {
  font-size: 18px;
  font-weight: 700;
}

text {
  font-family: Helvetica Neue, Tahoma, Arial;
}

.tip_text_container .content {
  padding: 10px 0;
  font-size: 15px;
}

.change_button {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  color: white;
  font-size: 30rpx;
  letter-spacing: 2rpx;
  background: linear-gradient(270deg, #E60012 0%, #FF7956 100%);
  box-sizing: content-box;
  padding-bottom: calc(env(safe-area-inset-bottom)/2);
}
