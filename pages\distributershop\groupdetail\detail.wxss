/* pages/distributershop/groupdetail/detail.wxss */
@import "/font/icon_f7.wxss";
@import '../qrcode.wxss';

.detail-page {
  position: relative;
  padding: 20rpx;
  padding-bottom: 110rpx;
  overflow: scroll;
}

.title {
  font-weight: 700;
  font-size: 46rpx;
  margin-bottom: 10rpx;
  color: #202020;
}

.subtitle {
  font-size: 30rpx;
  color: #606266;
}

.autor {
  font-size: 26rpx;
  color: #909399;
  padding: 20rpx 0;
}

.article {
  display: flex;
  margin: 16rpx 0;
  /* padding-bottom: 20rpx; */
  font-size: 30rpx;
  line-height: 1.5;
}

.bottom {
  position: fixed;
  height: 90rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 -6rpx 6rpx #eee;
  width: 100%;
  bottom: 0;
  left: 0;
  background: white;
  box-sizing: content-box;
  padding-bottom: env(safe-area-inset-bottom);
}

.viewer-count {
  display: flex;
  align-items: center;
  position: relative;
  margin-right: 10rpx;
  padding: 0 30rpx;
  color: #FF7956;
  height: 90rpx;
}

.viewer-count text {
  color: #555;
  font-size: 18rpx;
  position: absolute;
  height: 20rpx;
  line-height: 20rpx;
  right: 10rpx;
  top: 10rpx;
}

.goods-body {
  flex: 1;
  text-align: right;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}

.goods-btn {
  background:linear-gradient(270deg, #E60012 0%, #FF7956 100%);
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
  margin: auto 16rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  width: 140rpx;
}

.line1 {
  display: flex;
  background: white;
  width: 95%;
  margin: 16rpx auto;
  box-shadow: 0 0 6rpx #ddd;
  border-radius: 10rpx;
  height: 260rpx;
}

.line1 .image {
  height: 260rpx;
  width: 260rpx;
  border-radius: 10rpx;
  margin-right: 6rpx;
}

.item-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10rpx;
}

.good-sn {
  font-size: 26rpx;
  color: #aaa;
  margin: 10rpx 0;
}

.price {
  color: #e60012;
}

.price text {
  font-size: 40rpx;
  font-weight: 700;
}

.item-detail .title {
  width: 100%;
  font-size: 26rpx;
  color: #000;
  text-overflow: ellipsis;
  overflow: hidden;
  text-overflow: -o-ellipsis-lastline;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 80rpx;
  line-height: 40rpx;
}

.caogao-pop {
  height: 800rpx;
  background: rgba(245, 245, 245, 1);
  padding: 10rpx 0;
  position: relative;
}

.viewer {
  display: flex;
  font-size: 24rpx;
  justify-content: space-between;
  padding-right: 20rpx;
  align-items: center;
}

.viewer-cart {
  color: #FF7956;
  font-size: 60rpx;
  padding: 0 10rpx;
}

.group-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  background: white;
  line-height: 80rpx;
  height: 80rpx;
  box-shadow: 0 6rpx 6rpx #eee;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.group-top text {
  margin: 0 16rpx;
  border-radius: 10rpx;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 16rpx;
  text-align: center;
  min-width: 90rpx;
}

.group-top text:nth-child(1) {
  color: white;
 background:linear-gradient(270deg, #E60012 0%, #FF7956 100%);
}

.group-top text:nth-child(3) {
  background: white;
}

.checked-bg {
  color: #FF7956 !important;
}

.bottom-text {
  color: #000;
  font-size: 24rpx;
}

/* 图片组合模板 */

image {
  background: #fff;
  box-sizing: border-box;
}

/* 模板1 */

.imgType1 {
  display: flex;
  border-radius: 2rpx;
  overflow: hidden;
}

.imgType1>image {
  width: 100%;
  position: relative;
  border-radius: 2rpx;
  border: 2rpx solid #ececec;
}

/*模板2  */

.imgType2 {
  overflow: hidden;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.imgType2>image {
  width: 350rpx;
  border-radius: 2rpx;
  border: 2rpx solid #ececec;
  margin-bottom: 10rpx;
  position: relative;
}

.imgType2>image:nth-of-type(2n+1) {
  margin-right: 10rpx;
}

/* 模板3 */

.imgType3 {
  overflow: hidden;
  width: 100%;
  display: flex;
  align-items: center;
}

.imgType3>.part1 {
  width: 392rpx;
  height: 610rpx;
  border-radius: 2rpx;
  border: 2rpx solid #ececec;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  position: relative;
  overflow: hidden;
}

.imgType3>.part2 {
  width: 300rpx;
  display: flex;
  flex-direction: column;
}

.imgType3>.part2>image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 2rpx;
  border: 2rpx solid #ececec;
  position: relative;
  overflow: hidden;
}

/*模板4 */

.imgType4 {
  overflow: hidden;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.imgType4>image {
  height: 350rpx;
  width: 350rpx;
  border-radius: 2rpx;
  border: 2rpx solid #ececec;
  margin-bottom: 10rpx;
  position: relative;
}

.imgType4>image:nth-of-type(2n+1) {
  margin-right: 10rpx;
}

.imgnav {
  position: absolute;
  bottom: 0;
  right: 10rpx;
  color: #828bc6;
  font-size: 28rpx;
  height: 60rpx;
  line-height: 60rpx;
}
