/* pages/distributershop/services/index/index.wxss */
page {
  background-color: #f3f3f3;
}

.hover {
  opacity: 0.6;
}

.hover2 {
  background: #f9f9f9;
}


.top {
  position: sticky;
  top: 0;
  display: flex;
  flex-direction: column;
  z-index: 100;
}

.search {
  height: 90rpx;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background: #ECF5FF;
}

.search-item {
  width: 710rpx;
  height: 70rpx;
  background: #fff;
  border-radius: 200rpx;
  display: flex;
  align-items: center;
  padding-left: 20rpx;
  box-shadow: 0 0 2rpx #f1f1f1;
}

.search-item .uni-icon-search {
  font-size: 40rpx;
  color: #888;
  margin-right: 6rpx;
}

.search-item input {
  height: 100%;
  flex: 1;
}

.search-item .sear-btn {
  background: #409EFF;
  color: #fff;
  height: 100%;
  padding: 0 36rpx;
  display: flex;
  align-items: center;
  border-radius: 200rpx;
  font-size: 30rpx;
}

.tab-box {
  display: flex;
  align-items: center;
  background: #fff;
}

.tab-box .tab-box-more {
  text-align: center;
  flex: 1;
  height: 80rpx;
  color: #909399;
  display: flex;
  align-items: center;
  border-left: 4rpx solid #a0cfff;
  column-count: 1;
  font-size: 32rpx;
  padding: 0 10rpx;
}

.tips {
  height: 60rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #555;
  background: #f3f3f3;
}

.tips .num {
  margin: 0 10rpx;
  font-size: 34rpx;
  color: #222;
  font-weight: 700;
}

.tips .add {
  color: #409EFF;
  font-size: 30rpx;
  padding: 0 20rpx;
  height: 100%;
  line-height: 60rpx;
  text-decoration: underline;
}

.member-box {
  display: flex;
  flex-direction: column;
  background: #fff;
}

.member-box .member-item1 {
  padding: 0 30rpx;
}

.member-box .member-item {
  padding: 0 0 0 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #f1f1f1;
  height: 160rpx;
  position: relative;
}

.member-box .member-item1:last-of-type .member-item {
  border: none;
}


.member-item .member-star {
  position: absolute;
  left: -30rpx;
  width: 60rpx;
  height: 100%;
  color: #ffc42e;
  font-size: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}



.member-item .member-img {
  background: #eee;
  height: 100rpx;
  width: 100rpx;
  border-radius: 50%;
  box-shadow: 0 0 6rpx #ddd;
}

.member-item .uni-icon-arrowright {
  font-size: 42rpx;
  color: #999;
}



.member-item .member-content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16rpx 30rpx;
  padding-right: 0;
  justify-content: space-evenly;
  font-size: 30rpx;
}

.member-item .member-content .member-nick {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.member-item .member-q {
  /*  position: absolute;
  top: 16rpx;
  left: 100rpx; */
  margin-right: 6rpx;
  width: 30rpx;
  height: 30rpx;
  background: #409EFF;
  color: #fff;
  text-align: center;
  line-height: 28rpx;
  border-radius: 50%;
  font-size: 26rpx;
}

.member-item .member-content .member-nick .tag1 {
  padding: 2rpx 8rpx;
  color: #ff9e40;
  border: 2rpx solid #ff9e40;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.member-item .member-content .member-nick .tag2 {
  color: #ff9e40;
  font-size: 24rpx;
}

.member-item .member-content .member-behavior {
  font-size: 26rpx;
  color: #555;
}

.filter-box {
  height: 100%;
  width: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
}

.filter-box>.filter-btn {
  border-top: 2rpx solid #ddd;
  height: 120rpx;
  padding: 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

.filter-box>.filter-btn>.filter-btn-item {
  background: #409eff;
  color: #fff;
  border-radius: 12rpx;
  padding: 0 40rpx;
  height: 80rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-box>.filter-content-box {
  flex: 1;
  position: relative;
  padding: 0 30rpx;
}

.filter-box>.filter-content-box>.fillter-content {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  padding: 0 20rpx;
}

.fillter-item {
  padding: 20rpx 0;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  font-weight: 700;
}
