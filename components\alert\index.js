let queue = [];

function getContext() {
  const pages = getCurrentPages();
  return pages[pages.length - 1];
}
const Alert = options => {
  options = Object.assign({}, Alert.defaultOptions, {
    alertText: '',
    img: '',
    content: '',
    left: '',
    right: '',
    cancel: '',
    confirm: ''
  }, options);
  console.log('AlertAlert')
  console.log(Alert.defaultOptions, options)
  const context = options.context || getContext();
  const alert = context.selectComponent(options.selector);
  delete options.selector;
  if (alert) {
    alert.setData(options)
  } else {
    console.warn('未找到 alert 节点，请确认 selector 及 context 是否正确');
  }
};

Alert.defaultOptions = {
  show: true,
  selector: '#sanfu-alert'
};

Alert.close = (options) => {
  options = Object.assign({}, Alert.defaultOptions, options);
  const context = options.context ||getContext();
  const alert = context.selectComponent(options.selector);
  if (alert) {
    alert.setData({
      show: false
    })
  } else {
    console.warn('未找到 alert 节点，请确认 selector 及 context 是否正确');
  }
}
export default Alert;
