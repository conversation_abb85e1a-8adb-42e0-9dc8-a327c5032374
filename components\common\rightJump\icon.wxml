<!--pages/component/tocoupon/icon.wxml-->
<wxs module="f">
function touchMove(e, ins) {
  var top = e.detail.scrollTop
  top = top - 65
  var h = e.currentTarget.dataset.h
  ins.selectComponent('#my_coupon').setStyle({
    left: e.changedTouches[0].clientX - 25 + 'px',
    top: e.changedTouches[0].clientY - 25 + 'px'
  })
}
module.exports.touchMove = touchMove;
</wxs>

<view id="my_coupon" class="my_coupon" style='left: 620rpx;top: 80%;{{styles}}' catchtap='toJump' catchtouchmove='{{f.touchMove}}'>{{title}}</view>