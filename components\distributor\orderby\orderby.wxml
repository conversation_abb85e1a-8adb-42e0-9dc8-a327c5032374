<!--components/distributor/orderby.wxml-->
<view class="order-by">
	<block wx:for="{{groups}}" wx:key="index">
		<view class="item" bindtap="changeOrder" data-item="{{item}}">
			<view>{{item.name}}</view>
			<view class="uni-icon uni-icon-arrow{{item.order === 0 ? 'down' : 'up'}}">
			</view>
		</view>
	</block>
	<view class="order-content" wx:if="{{current.type === 1 && current.order === 1}}">
		<scroll-view scroll-y style="width: 100%;height: 100%;">
			<view class="scroll">
				<block wx:for="{{current.type_array}}" wx:key="index">
					<view class="item-branch">
						<view bindtap="checkBranch" data-item="{{item}}">{{item.name}}</view>
					</view>
				</block>
			</view>
		</scroll-view>
	</view>
</view>