// pages/distributershop/shareGoods/shareGoods.js
const app = getApp()
Page({
  data: {
    goodsId: '', // 搜索用货号
    searchShoId: '', // 搜索用店号
    canReset: false, // 重置
    showLoading: false, // 加载
    viewGoods: '', // 商品信息
    imgIndex: 0, // 商品图片位置
    shareShops: [], //分享门店数组
    inputShopId: '',
    shareIndex: 0, // 分享门店索引
    showSharePop: false, //分享弹窗
    preView: {
      title: '',
      path: '',
      imageUrl: ''
    }, // 预览
    showPrePop: false, //预览修改弹窗
    preType: '',
    preInput: ''
  },
  onShow() {
    this.setData({
      searchShoId: wx.getStorageSync('dsho_id'),
      shareShops: wx.getStorageSync('shareShops') || []
    })
  },
  //******输入绑定*********
  input: function(e) {
    this.setData({
      [e.currentTarget.dataset.name]: e.detail.value
    })
  },
  //******快速清除输入*********
  clearinput: function(e) {
    this.setData({
      [e.currentTarget.dataset.name]: ''
    })
  },
  // *****保存输入店号**********
  addShop: async function() {
    let val = this.data.inputShopId.trim() // 去空格
    if (val.length <= 0) {
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      })
      return;
    }
    val = val.toUpperCase() // 转大写
    let ele = ''
    let arr = []
    for (let i = 0; i < val.length; i++) {
      if (!/^[A-Za-z0-9]*$/.test(val[i])) {
        if (ele.length > 0) {
          if (await this.checkShop(ele))
            arr.push(ele)
        }
        ele = ''
      } else {
        ele += val[i]
        if (i === val.length - 1) {
          if (await this.checkShop(ele))
            arr.push(ele)
        }
      }
    }
    if (arr.length <= 0) {
      return;
    }
    this.data.shareShops = this.data.shareShops.concat(arr)
    wx.setStorageSync('shareShops', this.data.shareShops)
    this.setData({
      inputShopId: '',
      shareShops: this.data.shareShops
    })
  },
  async checkShop(name) {
    this.setLoading(1)
    const res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/agentShop/page', {
      sid: wx.getStorageSync('sid'),
      inputName: name,
      page: 1,
      pageSize: 12
    })
    this.setLoading(0)
    if (res.success) {
      if (res.data.result.length > 0) {
        return 1
      } else {
        wx.showToast({
          title: name + '添加失败',
          icon: 'none'
        })
        return 0
      }
    } else return 1
  },
  // 删除店号
  deleteShop(e) {
    const i = e.currentTarget.dataset.i
    this.data.shareShops.splice(i, 1)
    wx.setStorageSync('shareShops', this.data.shareShops)
    this.setData({
      shareShops: this.data.shareShops
    })
  },
  shopUp(e) {
    const i = e.currentTarget.dataset.i
    let item = this.data.shareShops.splice(i, 1)
    this.data.shareShops.splice(i - 1, 0, item)
    wx.setStorageSync('shareShops', this.data.shareShops)
    this.setData({
      shareShops: this.data.shareShops
    })
  },
  shopDown(e) {
    const i = e.currentTarget.dataset.i
    let item = this.data.shareShops.splice(i, 1)
    this.data.shareShops.splice(i + 1, 0, item)
    wx.setStorageSync('shareShops', this.data.shareShops)
    this.setData({
      shareShops: this.data.shareShops
    })
  },
  //******搜索*********
  search: async function() {
    if (this.data.goodsId.length <= 0) {
      wx.showToast({
        title: '请输入6位货号',
        icon: 'none'
      })
      return;
    }
    this.getGoods()
  },
  // ******图片滑动变化*********
  imgSwipe: function(e) {
    this.setData({
      imgIndex: e.detail.current,
      ['preView.imageUrl']: this.data.viewGoods.goodsPhotoList[e.detail.current].bigImg
    })
  },
  /* 点击放大商品主图 */
  openImg: function(e) {
    let index = e.currentTarget.dataset.index
    let urls = []
    for (let i in this.data.viewGoods.goodsPhotoList)
      urls.push(this.data.viewGoods.goodsPhotoList[i].bigImg)
    wx.previewImage({
      current: this.data.viewGoods.goodsPhotoList[index].bigImg, // 当前显示图片的http链接
      urls: urls // 需要预览的图片http链接列表
    })
  },
  // *****获取商品（商品详情接口)*********
  getGoods: async function(e) {
    this.setLoading(true)
    const res = await app.reqGet('ms-sanfu-wap-goods-item/itemNew', {
      goodsSn: this.data.goodsId,
      sid: wx.getStorageSync('sid'),
      shoId: this.data.searchShoId,
      localShoId: this.data.searchShoId
    })
    this.setLoading(false)
    if (res.success) {
      this.data.viewGoods = res.msg.goods
      this.setData({
        imgIndex: 0,
        viewGoods: this.data.viewGoods,
        preView: {
          title: this.data.viewGoods.goodsName,
          imageUrl: this.data.viewGoods.goodsPhotoList && this.data.viewGoods.goodsPhotoList[0].bigImg,
          path: `/pages/goodsDisplay/goodsDisplay?goods_sn=${this.data.goodsId}&type=SHOP`
        }
      })
    } else {
      app.util.reqFail(res)
    }
  },
  onShareAppMessage: function(e) {
    if (e.from == 'button' && this.data.preView.path) {
      const index = e.target.dataset.i
      const type = e.target.dataset.type
      let shopid = this.data.shareShops[index]
      if (type == 2) {
        this.jumpShop()
      } else if (type == 'qy') {
        shopid = 'qy'
      }
      let path = this.data.preView.path
      if (/\/pages\/pcweb/.test(path)) {
        path = path + encodeURIComponent(`&discard=${wx.getStorageSync('discard')}&disshop=${shopid}`)
      } else {
        path = path + `&discard=${wx.getStorageSync('discard')}&disshop=${shopid}`
      }
      return {
        title: this.data.preView.title || '三福优选',
        path: path,
        imageUrl: this.data.preView.imageUrl || '/static/logo.jpg'
      };
    } else if (e.from == "menu") {
      //你的分享配置
      return {
        title: this.data.preView.title || '三福优选',
        path: this.data.preView.path || '/pages/index/index',
        imageUrl: this.data.preView.imageUrl || '/static/logo.jpg'
      };
    } else {
      return {
        path: '/pages/index/index',
        imageUrl: '/static/logo.jpg'
      };
    }
  },
  //打开分享弹窗
  toShare: function(e) {
    const t = e.currentTarget.dataset.t
    if (t && this.data.shareShops.length == 0) {
      wx.showToast({
        title: '分享门店为空',
        icon: 'none'
      })
      return
    }
    this.setData({
      showSharePop: t,
      shareIndex: t == true ? 0 : this.data.shareIndex
    })
  },
  //跳过分享
  jumpShop: function(e) {
    if (this.data.shareIndex == this.data.shareShops.length - 1) {
      this.setData({
        showSharePop: false
      })
      return
    }
    this.setData({
      shareIndex: this.data.shareIndex + 1
    })
  },
  /* 传图功能方法 选择+上传*/
  upLoadImg(e) {
    const index = e.currentTarget.dataset.index
    wx.chooseImage({
      count: 1, //总数 1-9
      sizeType: ['compressed'], //Array original	原图	 compressed	压缩图
      sourceType: ['album', 'camera'], //Array 图片的来源
      success: async res1 => {
        for (const i in res1.tempFilePaths) {
          this.setData({
            ['preView.imageUrl']: res1.tempFilePaths[i]
          })
        }
      },
      fail: res => {
        wx.showToast({
          title: '已取消选择图片',
          icon: 'none'
        })
      }
    })
  },
  changePreview: function(e) {
    let type = e.currentTarget.dataset.type
    this.setData({
      preInput: this.data.preView[type] || '',
      preType: type,
      showPrePop: true
    })
  },
  savePre: async function(e) {
    let type = e.currentTarget.dataset.type
    if (type || this.data.preInput == '') {
      this.setData({
        showPrePop: false
      })
      return
    }
    // 处理H5链接
    if (this.data.preType == 'H5path') {
      let url = await app.toH5(this.data.preInput, 2)
      console.log(url);
      this.setData({
        [`preView.path`]: url,
        showPrePop: false
      })
      return
    }
    this.setData({
      [`preView.${this.data.preType}`]: this.data.preInput,
      showPrePop: false
    })
  },
  toReset: function() {
    this.setData({
      goodsId: '', // 搜索用货号
      searchShoId: '', // 搜索用店号
      viewGoods: '', // 商品信息
      preView: {
        title: '',
        path: '',
        imageUrl: ''
      }, // 预览
    })
  }
})
