const app = getApp()
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
    addGlobalClass: true,
    multipleSlots: true,
    virtualHost: true
  },
  /**
   * 折叠组件（包含文字折叠)
   * @properties{Boolean} dynamic  false 是否动态（每次点击都重新获取高度)
   * @properties{Boolean} showTitle 展示标题
   * @properties{String} title  标题（文字)
   * @properties{Boolean} slotTitle  是否标题（slot)
   * @properties show {Boolean} 是否展开折叠
   * @properties border {Boolean} 展示边框
   * @properties itemStyle {String} 
   * @properties duration {Number} 
   * @properties minHeight {Number} 
   * @properties showBtn
   * @properties showClass
   * @properties hideClass
   * @properties styles
   * @properties moreStyle
   * @event {Function} change 改变状态时触发
   */
  properties: {
    dynamic: {
      type: Boolean,
      value: false
    },
    tag: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    showTitle: {
      type: Boolean,
      value: true
    },
    slotTitle: {
      type: Boolean,
      value: false
    },
    show: {
      type: Boolean,
      value: false
    },
    border: {
      type: Boolean,
      value: true
    },
    itemStyle: {
      type: String,
      value: ''
    },
    titleStyle: {
      type: String,
      value: ''
    },
    // 动画持续时长
    duration: {
      type: Number,
      value: 200
    },
    // 内容最少展示高度
    minHeight: {
      type: Number,
      value: 0
    },
    // 不展示标题时使用，且内容高度大于最少高度时展示
    showBtn: {
      type: Boolean,
      value: false
    },
    // 展示时slot内置样式
    showClass: {
      type: String,
      value: ''
    },
    hideClass: {
      type: String,
      value: ''
    },
    // 顶层样式
    styles: {
      type: String,
      value: ''
    },
    moreStyle: { // 展开按钮样式
      type: String,
      value: ''
    },
    iconStyle: {
      type: String,
      value: ''
    },
    hover: {
      type: Boolean,
      value: true
    },
    tapUnfold: {
      // 点击内容展开(开关)
      type: Boolean,
      value: false,
    },
    tapfold: {
      // 点击收起 是否可关
      type: Boolean,
      value: true,
    },
    loadHeight: {
      //未加载完成高度
      type: String,
      value: ''
    },
    icon: {
      type: String,
      value: ''
    },
    iconStyle2: {
      type: String,
      value: ''
    },
  },
  data: {
    show1: false,
    height: 0,
    itemHeight: 0,
    showItemHeight: 0,
    showOpen: false,
    lasttag: null
  },
  async attached() {
    // 初始化，有展开按钮时判断内容是否大于预设最低
    wx.nextTick(async () => {
      if (!this.data.showTitle && this.data.showBtn) {
        this.time = Date.now()
        await this.getItemHeight()
        let mh = this.data.minHeight
        console.log(mh,this.data.itemHeigh);
        this.setData({
          showOpen: this.data.itemHeight > mh ? true : false
        })
      }
    })
  },
  detached() {},
  watch: {
    'show': function(newVal) {
      this.change(1)
    },
    'minHeight': function(minHeight, itemHeight) {
      // 监测minHeight值的改变来控制收起
      this.checkMinHeight()
    },
    'tag': function(e) {
      if (e != this.data.lasttag) {
        this.getItemHeight()
      }
    }
  },
  methods: {
    async getItemHeight() {
      this.data.lasttag = this.data.tag
      let data = await this.getRect('.collapse-item') || {}
      this.data.itemHeight = data.height
      this.checkMinHeight()
      this.setData({
        itemHeight: this.data.itemHeight,
        showItemHeight: app.util.px2rpx(this.data.itemHeight)
      })
    },
    checkMinHeight() {
      if (!this.data.minHeight) return
      // 初始化最低高度
      if (this.data.loadHeight && !this.data.itemHeight) return
      let mh = this.data.minHeight
      // console.log(mh, this.data.itemHeight, this.data.height);
      // console.log(parseInt(this.data.height));
      if (parseInt(this.data.height) > this.data.itemHeight && this.data.itemHeight > 0) {
        this.setData({
          height: `${this.data.itemHeight}px`,
          show1: false
        })
      }
      this.setData({
        showMinHeight: (this.data.itemHeight < mh && this.data.itemHeight > 0 ? this.data.itemHeight : mh) + 'px',
        showOpen: this.data.itemHeight > mh ? true : false
      })
      // console.log('checkMinHeight', this.data.height);
      // if (this.data.height > (this.data.itemHeight < mh && this.data.itemHeight > 0 ? this.data.itemHeight : mh)) {
      //   this.setData({
      //     height: this.data.showMinHeight
      //   })
      // }
    },
    async change(e) {
      if (e == 1 && this.data.show1 == this.data.show) return
      this.data.show1 = !this.data.show1
      let height
      if (!this.data.itemHeight) {
        await this.getItemHeight()
        height = this.data.itemHeight || 0
      } else {
        height = this.data.itemHeight || 0
      }
      if (this.data.show1) {
        this.setData({
          show1: this.data.show1,
          height: height ? `${height}px` : 0
        })
        // setTimeout(() => {
        //   this.setData({
        //     height: 'auto'
        //   })
        // }, this.properties.duration)
      } else {
        this.setData({
          height: height ? `${height}px` : 'auto'
        })
        wx.nextTick(() => {
          if (this.properties.dynamic) {
            this.data.itemHeight = 0
            this.setData({
              height: 0
            })
          } else {
            this.setData({
              height: 0
            })
          }
          // console.log('change', this.data.height);
          setTimeout(() => {
            this.setData({
              show1: this.data.show1
            })
          }, this.properties.duration)
        })
      }
      // console.log('change', this.data.height);
      if (e != 1) {
        this.triggerEvent('change', this.data.show1)
      }
    },
    getRect(selector, all) {
      return new Promise(resolve => {
        wx.createSelectorQuery()
          .in(this)[all ? 'selectAll' : 'select'](selector)
          .boundingClientRect(rect => {
            if (all && Array.isArray(rect) && rect.length) {
              resolve(rect);
            }
            if (!all && rect) {
              resolve(rect);
            }
          })
          .exec();
      });
    }

  }
});
