// pages/couponcenter/history/history.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // tab切换
    currentTab: 0,
    tabs: [
      {
        data: [],
        page: 1,
        has_more: 4
      },
      {
        data: [],
        page: 1,
        has_more: 4
      }
    ],
    useDetail: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getCouponsHistory()
  },
  async onShow() {
    const limitShop = await app.util.checkLimitShop()
    this.setData({
      limitShop
    })
  },
  onPullDownRefresh() {
    wx.stopPullDownRefresh()
    this.setData({
      tabs: [
        {
          data: [],
          page: 1,
          has_more: 4
        },
        {
          data: [],
          page: 1,
          has_more: 4
        }
      ]
    })
    this.getCouponsHistory()
  },
  /** 获取个人历史优惠券记录列表 */
  getCouponsHistory() {
    wx.showLoading()
    app.reqGet(
      'ms-sanfu-wechat-coupon/coupon/listHistoryOfCoupons',
      {
        sid: wx.getStorageSync('sid')
      },
      res => {
        wx.hideLoading()
        if (res.success) {
          this.list = [res.data.usedList, res.data.expiredList]
          this.dealList()
        } else {
          app.util.reqFail(res)
        }
      }
    )
  },
  dealList() {
    const i = this.data.currentTab
    if (this.data.tabs[i].has_more != 0 && this.data.tabs[i].has_more != 4) return
    this.setData({
      [`tabs[${i}].has_more`]: 1
    })
    if (this.list && this.list[i] && this.list[i].length > 0) {
      let list = this.list[i].splice(0, 10)
      this.setData({
        [`tabs[${i}].data`]: [...this.data.tabs[i].data, ...list],
        [`tabs[${i}].has_more`]: this.list[i].length > 0 ? 0 : 2
      })
    } else {
      this.setData({
        [`tabs[${i}].has_more`]: 2
      })
    }
  },
  // 滑动切换tab
  bindChange: function (e) {
    this.setData({
      currentTab: e.detail.current
    })
    if (this.data.tabs[e.detail.current].data.length == 0) this.dealList()
  },
  // 点击tab切换
  swichNav: function (e) {
    if (this.data.currentTab === e.target.dataset.current) {
      return
    } else {
      this.setData({
        currentTab: e.target.dataset.current
      })
      if (this.data.tabs[this.data.currentTab].data.length == 0) this.dealList()
    }
  },
  showUse: function (e) {
    const item = e.detail
    app.reqGet(
      'ms-sanfu-wechat-coupon/coupon/getCouponUseDtl',
      {
        couponCode: item.code
      },
      res => {
        if (res.success && res.data) {
          let memo = item.memo
          if (this.data.limitShop) {
            memo = item.memo
              .split('\n')
              .filter(line => !line.includes('店'))
              .join('\n')
          }
          this.setData({
            useDetail: {
              ...res.data,
              memo: memo
            }
          })
        } else {
          res.msg = (res.msg != 'ok' && res.msg) || '订单时间比较久远，无法为您展示优惠券使用明细，如需详细信息请联系客服~'
          app.util.reqFail(res)
        }
      }
    )
  },
  showUseClose() {
    this.setData({
      useDetail: ''
    })
  },
  /* 取消订单接口方法 */
  cancleOrder(e) {
    if (!wx.getStorageSync('orgId')) {
      // 会员跳转至商城处理 240326 lx
      this.toDetail()
      return
    }
    let orderId = this.data.useDetail.ordId
    wx.showModal({
      content: '您确认要取消订单吗？',
      success: res1 => {
        if (res1.confirm) {
          wx.showLoading()
          app.globalData.oneceOrgId = this.data.useDetail.orgId
          app.reqPost(
            'ms-sanfu-wap-sale/saleCancel',
            {
              sid: wx.getStorageSync('sid'),
              tb_ord_id: orderId
            },
            res => {
              wx.hideLoading()
              if (res.success) {
                wx.showToast({
                  title: '已取消订单~',
                  icon: 'success'
                })
                setTimeout(() => {
                  this.showUseClose()
                  this.onPullDownRefresh()
                }, 1500)
              } else {
                app.util.reqFail(res)
              }
            }
          )
        }
      }
    })
  },
  toDetail(e) {
    app.toH5(`user/customer/orderDetail?tb_ord_id=${this.data.useDetail.ordId}&oneceOrgId=${this.data.useDetail.orgId}`)
  }
})
