// pages/couponcenter/index/index.js
const app = getApp()
import Auth from '../../../auth/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    tabstyle: 0, // 0优惠券 1 异业
    couponTypeList: ['全部'], // 优惠券类型
    couponTypeIndex: 0,
    couponList: [],
    // tab切换
    externallist: [], //外部券
    extractShow: false, // 异业
    info: {
      couponCode: '',
      secretKey: ''
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function(options) {
    this.data.options = options
    // 设置全局缓存
    this.setData({
      tabstyle: options.tabstyle ? Number(options.tabstyle) : 0,
      hideTab: options.hideTab || ''
    })
    if (options.hideTab) {
      wx.setNavigationBarTitle({
        title: '我的优惠券'
      })
    }
    if (options.p == 1) {
      this.toCouponList()
    }
  },
  async onShow() {
    await app.waitSid()
    this.getUnUseCoupons()
    this.getlistExchangeHistory()
    const limitShop = await app.util.checkLimitShop()
    this.setData({
      limitShop
    })
  },
  // 获取异业列表
  async getlistExchangeHistory() {
    let that = this
    let res = await app.reqGet('ms-sanfu-wechat-coupon/externalCoupon/listExchangeHistory', {
      sid: wx.getStorageSync('sid')
    }, res => {})
    if (res.success) {
      that.setData({
        externallist: res.data
      })
    }
  },
  /** 获取未使用的优惠券列表 */
  async getUnUseCoupons() {
    this.setData({
      loading: true
    })
    const res = await app.reqGet('ms-sanfu-wechat-coupon/coupon/listNotUsedCoupons', {
      sid: wx.getStorageSync("sid")
    }, async res => {
      if (res.success) {
        let list = res.data || []
        let cTime = Date.now() + await app.util.getTimeDiff()
        let typeStr = ''
        for (let i = list.length - 1; i >= 0; i--) {
          typeStr += list[i].useMethodNew
          let startTime = new Date(list[i].validBegin.replace(/\./g, "/"))
          if (startTime > cTime) {
            list[i].isPre = true
            list.push(list[i])
            list.splice(i, 1)
          }
        }
        const allTypes = ['通用', '线上', '门店']
        const types = []
        for (const i of allTypes) {
          if (typeStr.includes(i)) {
            types.push(i)
          }
        }
        this.data.couponTypeList = ['全部', ...types]
        if (this.data.limitShop) {
          this.data.couponTypeList = ['全部']
        }
        this.setData({
          couponTypeList: this.data.couponTypeList,
          couponList: list,
        })
      } else {
        app.util.reqFail(res)
      }
    })
    this.setData({
      loading: false
    })
  },
  // 点击tab切换
  tabNav: function(e) {
    if (this.data.tabstyle != e.detail) {
      this.setData({
        tabstyle: Number(e.detail)
      })
    }
  },
  // 切换券类型
  checkCoupon(e) {
    const i = e.currentTarget.dataset.i
    if (this.data.couponTypeIndex != i) {
      this.setData({
        couponTypeIndex: i
      })
    }
  },


  toHistory() {
    wx.navigateTo({
      url: '/pages/couponcenter/history/history',
    })
  },
 
  copyContent(e) {
    wx.setClipboardData({
      data: e.currentTarget.dataset.val,
      success: function(res) {
        wx.showToast({
          title: '复制成功',
          icon: 'none'
        })
      }
    })
  },
  onChangeStatus() {
    this.setData({
      extractShow: false,
      'info.couponCode': '',
      'info.secretKey': '',
    })
  },
  toCoupon(e) {
    const that = this
    if (e.currentTarget.dataset.yyexchangetype === 'H5兑换') {
      wx.showModal({
        title: '提示',
        content: '请前往sanfu微信公众号领取福利',
        showCancel: false
      })
      return
    }
    this.setData({
      extractShow: true,
      'info.couponCode': e.currentTarget.dataset.couponcode,
      'info.secretKey': e.currentTarget.dataset.secretkey,
    })
  },
  // 优惠券详情
  toDetail(e) {
    console.log('e', this.data.externallist[e.currentTarget.dataset.index])
    wx.setStorageSync('coupon', this.data.externallist[e.currentTarget.dataset.index])
    wx.navigateTo({
      url: '/pages/couponcenter/coupondetail/detail',
    })
  },
  toFubi(e) {
    let id = e.currentTarget.dataset.id
    app.toH5('/pages/fubishop/index/index')
  },
  toCouponList() {
    app.toH5("/pages/couponcenter/couponList/couponList")
  }
})
