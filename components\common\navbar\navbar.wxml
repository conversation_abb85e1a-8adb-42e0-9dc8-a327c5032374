<view class="actionBtn" style="top:{{statusBarHeight+(navBarHeight-menuHeight)/2}}px;height:{{menuHeight}}px;{{type=='transparent'&&opacity?'color:'+defaultColor:''}};{{btnColor}};">
  <block wx:if="{{showHome&&isBack}}">
    <view wx:if="{{!hideHome}}" bindtap="toHome" class="iconshouye2 f7 baricon" hover-class="hover-nav" style="padding-left: 28rpx;" hover-start-time="20" hover-stay-time="100">
      <!-- {{icon1}} -->
    </view>
  </block>
  <view wx:elif="{{isBack}}" bindtap="BackPage" class="uni-icon uni-icon-arrowleft baricon" hover-class="hover-nav" hover-start-time="20" hover-stay-time="100"></view>
  <slot name="icon"></slot>
</view>
<view class='nav-box' style="{{type=='transparent'&&!opacity?navStyleTop:(type=='transparent'?'background:transparent;':'')+navStyle}};height:auto;">
  <view class="statusbar" style="{{statusStyle}};height: {{statusBarHeight}}px;">
    <slot name="status"></slot>
  </view>
  <view class="titlebar" style="{{autoColor?'mix-blend-mode: difference;':''}};height: {{navBarHeight}}px;{{type=='transparent'&&opacity?'color:'+(defaultTitleColor||defaultColor):''}};font-size:{{title.length<=6?'34rpx':''}};{{titleStyle}}" wx:if="{{showTitle}}">
    <text wx:if="{{title}}" class="title" style="padding:0 {{menuWidth}}px;text-align:{{titleAlign}};padding-left:{{titleAlign!='center'?showHome?'112rpx':'92rpx':''}};">{{title}}</text>
    <view wx:if="{{slotTitle}}" class="slot-title" style="padding-right: {{menuWidth}}px;padding-left: {{isBack?'112rpx':''}};{{fullTitle?'padding:0;':''}}">
      <slot name="title"></slot>
    </view>
  </view>
  <view style="position: unset;transition: 0.2s all;opacity:{{opacity}}">
    <slot></slot>
  </view>
</view>
<view class="place-wrap {{hidetop?'hidetop':''}}" style="{{placeholderStyle}};">
  <observer wx:if="{{type=='transparent'}}" start dafaultNav bindeach="observereach" bindnov="observernov" options="{{options}}" styles="top: {{navBarHeight+statusBarHeight}}px;height: 1px;"></observer>
  <view style="height: {{statusBarHeight}}px;"></view>
  <view wx:if="{{showTitle}}" style="height: {{navBarHeight}}px;"></view>
  <slot name="placeholder"></slot>
</view>
