
<view class="stepper">
  <view
    wx:if="{{ showMinus }}"
    data-type="minus"
    class="plus-class f7 iconminus-empty {{(disabled || disableMinus || currentValue<= min)?'disabled':''}}"
    hover-class="minus--hover"
    hover-stay-time="70"
    bind:tap="onTap"
    bind:touchstart="onTouchStart"
    bind:touchend="onTouchEnd"
  />
  <input
    type="{{ integer ? 'number' : 'digit' }}"
    class="input-class input {{ (disabled || disableInput)?'disabled':'' }}"
    style="width: {{ utils.addUnit(inputWidth) }}; height: 56rpx"
    value="{{ currentValue }}"
    focus="{{ focus }}"
    disabled="{{ disabled || disableInput }}"
    bindinput="onInput"
    bind:focus="onFocus"
    bind:blur="onBlur"
  />
  <view
    wx:if="{{ showPlus }}"
    data-type="plus"
    class="minus-class f7 iconplus-empty {{(disabled || disablePlus || currentValue >= max)?'disabled':''}}"
    hover-class="plus--hover"
    hover-stay-time="70"
    bind:tap="onTap"
    bind:touchstart="onTouchStart"
    bind:touchend="onTouchEnd"
  />
</view>
