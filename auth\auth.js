// pages/auth/auth.js
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    showCancel: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // type 0 登录  1 跳转办卡   2重新获取会员信息  3补充资料  4。验证手机号  5.返回手机号  6.会员协议更新
    //tip_index ：0 登录   1 进入卡包跳转  2
    tip: ['获取授权进行会员登录', '点此进行会员卡绑定', '重新获取会员信息', '请先补充会员信息', '请先完善手机号', '', '会员协议已更新，请阅读并同意']
  },
  ready() {
    // this.setData({
    //   tip_index: 0
    // })
  },
  /**
   * 组件的方法列表
   */
  methods: {
    cancel(e) {
      if (!this.properties.showCancel && this.data.tip_index == 0) return
      console.log(this.properties.showCancel)
      this.setData({
        show: !1
      })
      // typeof this.data.cancel == 'function' && this.data.cancel(e)

    },
    confirm(e) {
      this.data.confirm(e)
      if (!this.properties.showCancel && this.data.tip_index == 0) return
      this.setData({
        show: !1
      })

    },
    toUserInfo(e) {
      wx.navigateTo({
        url: '/pages/user/userInfo/userInfo?type=1'
      })
      this.setData({
        show: !1
      })
    },
    preventD(e) {
      //阻止屏幕滚动
      return;
    },
    toH5(e) {
      let url = e.currentTarget.dataset.url
      console.log(url);
      app.toH5(url)
    }
  }
})
