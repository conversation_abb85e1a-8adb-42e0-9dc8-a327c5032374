<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<block wx:if="{{contentImgarr.length>0}}">
  <view class="share-title">
    <text>文章素材</text>
  </view>
  <checkbox-group bindchange="checkChange" data-type="choose_contentImg_index_arr">
    <view class="share-img-box">
      <label class="share-img-item" wx:for="{{contentImgarr}}" wx:key="index" bindtap="customSelect" data-type="contentImgarr" data-index="{{index}}">
        <image  lazy-load src="{{utils.jpg2jpeg(item)}}"></image>
        <checkbox value="{{index}}" checked="{{choose_contentImg_index_arr[index].bool}}"></checkbox>
        <view class="select-no" wx:if="{{choose_contentImg_index_arr[index].no&&customCanvas}}">
          <text>{{choose_contentImg_index_arr[index].no}}</text>
        </view>
      </label>
    </view>
  </checkbox-group>
</block>
<view class="share-title">
  <text>商品素材</text>
</view>
<checkbox-group bindchange="checkChange" data-type="choose_goodsImg_index_arr">
  <view class="share-img-box">
    <label class="share-img-item" wx:for="{{goodsImgarr}}" wx:key="index" bindtap="customSelect" data-type="goodsImgarr" data-index="{{index}}">
      <block wx:if="{{index<goodsDelayLoadIndex}}">
        <image  lazy-load src="{{utils.jpg2jpeg(item)}}"></image>
        <checkbox value="{{index}}" checked="{{choose_goodsImg_index_arr[index].bool}}"></checkbox>
        <view class="select-no" wx:if="{{choose_goodsImg_index_arr[index].no&&customCanvas}}">
          <text>{{choose_goodsImg_index_arr[index].no}}</text>
        </view>
      </block>
    </label>
  </view>
</checkbox-group>

<view class="share-title">
  <text bindtap="preview">小程序码分享图</text>
  <text wx:if="{{tmppath&&!customCanvas&&canvasTypeName[canvasType]=='自定义'&&!showLoading}}" bindtap="createShare" style="color:blue;font-size:30rpx;">（重新选择）</text>
</view>
<block>
  <radio-group class="radio-box" bindchange="canvasChange">
    <label class="radio-item" style="max-width:300rpx;">
      <text style="font-size:30rpx;color:#333;">生成来源：</text>
    </label>
    <label wx:for="{{canvasTypeName}}" wx:key="index" class="radio-item">
      <radio value="{{index}}" checked="{{index==canvasType}}" disabled="{{showLoading||customCanvas}}" color="#8F6AFF">
      </radio>
      <text>{{item}}</text>
    </label>
  </radio-group>
</block>
<!-- 分享朋友圈二维码 -->
<canvas type="2d" id="shareCanvas" class="shareCanvas" style="width:{{canvasWidth}}px;height:{{canvasHeight}}px;position:fixed;top:-{{canvasHeight||9999}}px;left:-{{canvasHeight}}rpx;margin-top:-300px;margin-left:-300px;"></canvas>
<view wx:if="{{!customCanvas}}" style="width:630rpx;margin:20rpx 60rpx;min-height:300rpx;">
  <sf-loading wx:if="{{showLoading}}" />
  <image wx:if="{{tmppath}}" mode="widthFix" src="{{tmppath}} " style="width:630rpx;border-radius: 20rpx;overflow: hidden;" />
</view>
<view class="share-bottom" style="{{customCanvas?'background:#8F6AFF':''}}" catchtap="controlSave">{{customCanvas?"确认完成(退出)":"一键保存"}}</view>
