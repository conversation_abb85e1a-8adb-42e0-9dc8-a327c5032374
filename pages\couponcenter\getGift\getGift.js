// pages/couponcenter/getCoupon/getCoupon.js
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    opt: '',
    id: '',
    showLoading: false,
    exchange: 0,
    name: '',
    des: '',
    bgUrl: '',
    showStatus: -1, //0 已领取 1 可领取  2 不可领取 3 未定位
    statusStr: '',
    postCardList: [],
    couponList: [],
    memo: '',
    showMemo: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    wx.hideShareMenu()
    this.reqId = options.reqId || '' // 企微活动校验id
    this.data.id = options.id || ''
    this.shoid = options.s || ''
    this.data.exchange = options.exchange || 0
  },
  onShow: async function () {
    await app.waitSid()
    if (wx.getStorageSync('sid')) this.init()
    else app.apploading(app)
    const limitShop = await app.util.checkLimitShop()
    this.setData({
      limitShop
    })
  },
  init: function () {
    if (this.data.loaded) return
    this.data.loaded = true
    this.getdshoid()
  },
  getdshoid() {
    if (this.shoid) {
      if (this.data.exchange) this.receiveGift()
      else this.getGift()
      return
    }
    app.getLocation('', () => {
      if (!this.shoid && !wx.getStorageSync('dsho_id')) {
        this.setData({
          showStatus: 3
        })
        return
      }
      if (this.data.exchange) this.receiveGift()
      else this.getGift()
    })
  },
  getGift: async function () {
    this.setLoading(true)
    const res = await app.reqGet('ms-sanfu-wap-common/gift/getGift', {
      giftBagId: this.data.id,
      sid: wx.getStorageSync('sid'),
      shoId: this.shoid || wx.getStorageSync('dsho_id')
    })
    this.setLoading(false)
    if (res.success) {
      wx.setNavigationBarTitle({
        title: res.data.giftBagName || '礼包领取'
      })
      this.needLocation = res.data.needLocation
      this.setData({
        postCardList: res.data.giftBagCardResDtoList,
        couponList: res.data.giftBagCouponResDtoList,
        bgUrl: res.data.bgpUrl,
        name: res.data.giftBagName,
        des: res.data.giftBagDes,
        showStatus: res.data.status,
        statusStr: res.data.statusStr
      })
    } else {
      app.util.reqFail(res)
    }
  },
  receiveGift: async function () {
    this.setLoading(true)
    const res = await app.reqPost('ms-sanfu-wap-common/gift/receiveGift', {
      giftBagId: this.data.id,
      sid: wx.getStorageSync('sid'),
      shoId: this.shoid || wx.getStorageSync('dsho_id')
    })
    this.setLoading(false)
    if (!res.success && res.msg == '请完善资料') {
      wx.showModal({
        title: '温馨提示',
        content: '请完善会员信息，以便参与更多活动',
        success: res => {
          if (res.confirm) {
            app.sf.track('click_complete_member_info')
            app.toH5('wechat/user/showUseredit.htm?type=1')
          }
        }
      })
      return
    }
    if (res.success) {
      wx.setNavigationBarTitle({
        title: res.data.giftBagName || '礼包领取'
      })
      this.setData({
        postCardList: res.data.giftBagCardResDtoList,
        couponList: res.data.giftBagCouponResDtoList,
        bgUrl: res.data.bgpUrl,
        name: res.data.giftBagName,
        des: res.data.giftBagDes,
        showStatus: 0
      })
    } else {
      app.util.reqFail(res)
    }
  },
  sendGift: async function () {
    this.setLoading(true)
    if (this.needLocation) {
      let location = await app.getWxLocation(null, this.data.id)
      if (!location && typeof location != 'number') {
        return
      }
    }
    const res = await app.reqPost('ms-sanfu-wap-common/gift/toGift', {
      giftBagId: this.data.id,
      sid: wx.getStorageSync('sid'),
      shoId: this.shoid || wx.getStorageSync('dsho_id')
    })
    this.setLoading(false)
    if (!res.success && res.msg == '请完善资料') {
      wx.showModal({
        title: '温馨提示',
        content: '请完善会员信息，以便参与更多活动',
        success: res => {
          if (res.confirm) {
            app.sf.track('click_complete_member_info')
            app.toH5('wechat/user/showUseredit.htm?type=1')
          }
        }
      })
      return
    }
    if (res.success) {
      this.setData({
        showStatus: 0
      })
      if (res.data.couponCode) app.subscribeMsg(2013, res.data.couponCode)
      else if (res.data.freeCode) app.subscribeMsg(2014, res.data.freeCode)
      freeCode: ''
      typeof app.globalData.wxSaveEnterpriseActivityLog == 'function' && app.globalData.wxSaveEnterpriseActivityLog(this.reqId) // 企微活动校验
    } else {
      app.util.reqFail(res)
    }
  },
  toShowMemo: function (e) {
    if (e.currentTarget.dataset.memo) {
      let memo = e.currentTarget.dataset.memo
      if (this.data.limitShop) {
        memo = memo
          .split('\n')
          .filter(line => !line.includes('店'))
          .join('\n')
      }
      this.setData({
        showMemo: true,
        memo: memo
      })
    }
  }
})
