.more-btn {
  border-radius: 24rpx;
  overflow: hidden;
  background: #fff;
  width: 710rpx;
  margin: 0 auto;
  margin-bottom: 32rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #303133;
}

.popup-container {
  width: 690rpx;
  padding: 0 20rpx;
  background: #f6f6f8f5;
  border-radius: 12rpx;
}

.popup-container .top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #202022;
  font-weight: 700;
}

.popup-container .top .uni-icon-closeempty {
  align-self: stretch;
  display: flex;
  align-items: center;
  padding: 0 4rpx 0 20rpx;
}

.popup-container .top .title {
  font-size: 32rpx;
  padding: 24rpx 0;
  padding-left: 4rpx;
}

.scroll-wrap {
  height: 65vh;
}

.goods {

  position: relative;
  background: #fff;
  flex: 1;
  padding: 20rpx 0;
  border-radius: 16rpx;
  font-size: 26rpx;
}

.goods-right {
  flex: 1;
  align-self: stretch;
  padding: 0 20rpx;
  display: flex;
  flex-direction: column;
  color: #303133;
  min-height: unset;
  height: unset;
}

.goods-right .title {
  padding-right: 10rpx;
  word-break: break-all;
  font-weight: bold;
  font-size: 24rpx;
  line-height: 1.3;
  color: #202020;
  padding-top: 16rpx;
}

.radio {
  position: relative;
  padding: 0 16rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  align-self: stretch;
}

.goods-price-wrap {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  line-height: 1;
  padding-top: 12rpx;
  align-items: center;
}

.goods-price-wrap .main-price {
  font-size: 34rpx;
  color: #E60012;
  font-weight: bold;
  margin-right: 12rpx;
  display: inline-block;
}

.goods-price-wrap .main-price .decimal {
  font-size: 22rpx;
}

.goods-price-wrap .main-price .price-icon {
  font-size: 24rpx;
  margin-right: 2rpx;
}

.goods-price-wrap .price-tag {
  font-size: 20rpx;
  font-weight: 400;
  /* margin: 0 12rpx 0 6rpx; */
  font-weight: bold;
  margin-right: 2rpx;
}

.goods-price-wrap .sub-price {
  color: #909090;
  font-size: 20rpx;
  display: inline-flex;
  align-items: flex-end;
  margin-top: 8rpx;
  color: #E60012;
  padding: 4rpx 14rpx;
  border-radius: 12rpx;
  line-height: 1;
  font-weight: 700;
  background: #fff1f0;
}

.add-goods {
  border-radius: 16rpx;
  margin-left: 8rpx;
  align-self: stretch;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}

.add-goods>text {
  background: linear-gradient(to right, #FF7956, #E60012);
  /* 从左到右渐变 */
  -webkit-background-clip: text;
  /* Safari/Chrome */
  -webkit-text-fill-color: transparent;
  /* Safari/Chrome */
  color: transparent;
  /* Firefox/Opera */
  font-weight: 700;
  font-size: 36rpx;
}
