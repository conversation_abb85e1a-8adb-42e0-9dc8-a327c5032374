/* pages/couponcenter/showPostFreeCards/showPostFreeCards.wxss */
@import "/font/icon_f7.wxss";
page {
  width: 100%;
  background: #f3f3f3;
  font-size: 30rpx;
}

/* 左上 */
.raidal1 {
  height: 0.8em;
  width: 0.8em;
  background: radial-gradient(1.6em at left top, #E3E3E1 50%, transparent 50%);
}

/* 右上 */
.raidal2 {
  height: 0.8em;
  width: 0.8em;
  background: radial-gradient(1.6em at right top, #E3E3E1 50%, transparent 50%);
}

/* 右下 */
.raidal3 {
  height: 0.8em;
  width: 0.8em;
  background: radial-gradient(1.6em at right bottom, #E3E3E1 50%, transparent 50%);
}

/* 左下 */
.raidal4 {
  height: 0.8em;
  width: 0.8em;
  background: radial-gradient(1.6em at left bottom, #E3E3E1 50%, transparent 50%);
}

.b0 {
  background: #FF6B6D;
}

.b1 {
  background: #FE5052;
}

.b2 {
  background: #FD0000;
}

.send {
  position: relative;
  width: 100%;
  background: #EEEEEE;
  border-radius: 5px;
  /* 圆角 */
  margin: 0 0.8em !important;
  padding: 0.5em;
  height: 3.6em;
  line-height: 3.6em;
}

.send .arrow {
  position: absolute;
  top: 15px;
  left: -16px;
  width: 0;
  height: 0;
  font-size: 0;
  border: solid 8px !important;
  border-color: #FFFFFF #EEEEEE #FFFFFF #FFFFFF !important;
}

.send2 {
  position: relative;
  width: 100%;
  background: #9fe65a;
  border-radius: 5px;
  /* 圆角 */
  margin: 0 0em !important;
  padding: 0.5em;
  min-height: 4em;
}

.send2 .arrow2 {
  position: absolute;
  top: 15px;
  right: -16px;
  width: 0;
  height: 0;
  font-size: 0;
  border: solid 8px !important;
  border-color: #FFFFFF #FFFFFF #FFFFFF #9fe65a !important;
}

.ub {
  display: flex;
  align-items: center;
  font-family: ff-meta-serif-web-pro, serif;
}

.ub-f1 {
  flex: 1;
}