// pages/distributershop/staffCode/staffCode.js
const app = getApp()
const CV = require('../../../utils/sf-canvas.js')
const shareCanvas = require('../../../utils/shareCanvas.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    qrcodeUrl: '', // 小程序码图片URL
    loading: true, // 加载状态
    staffInfo: {
      name: '', // 员工姓名
      userId: '', // 工号
      shopName: '' // 店名
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getStaffQrcode()
  },

  /**
   * 获取导购员工小程序码
   */
  async getStaffQrcode() {
    const res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/qrcode/getDistributionQrcodeStaff', {
      sceneType: 1, // 导购企微拉新二维码
      sid: wx.getStorageSync('sid')
    })

    if (res.success && res.data) {
      this.setData({
        qrcodeUrl: res.data.imageUrl,
        staffInfo: res.data,
        loading: false
      })
      // 渲染名片
      this.renderStaffCard()
    } else {
      app.util.reqFail(res)
    }
  },

  /**
   * 渲染员工名片
   */
  async renderStaffCard() {
    await shareCanvas.clearImgTemp.call(this)

    const sys = wx.getSystemInfoSync()
    const rpx = (sys.windowWidth || 375) / 750 // 画布单位转换
    // 画布尺寸适当加大，保证内容完整
    const canvasWidth = 600 * rpx
    const canvasHeight = 980 * rpx
    const qrcodeSize = 380 * rpx
    const logoWidth = 340 * rpx
    const logoHeight = 110 * rpx

    // 创建画布 - 使用 Canvas 2.0
    await CV.createCanvas('staffCanvas', canvasWidth, canvasHeight)
    let ctx = CV.getCtx()
    let canvas = CV.getCanvas()

    ctx.fillStyle = '#ffffff'
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)
    // 1. 顶部红色横幅（用canvas原生API）
    ctx.fillStyle = '#E53935'
    ctx.fillRect(0, 0, canvasWidth, 120 * rpx)

    // 横幅主标题
    CV.text({
      x: 40 * rpx,
      y: 30 * rpx,
      width: canvasWidth,
      color: '#fff',
      size: 40 * rpx,
      align: 'left',
      baseline: 'top',
      bold: true,
      text: '快来加入三福官方社群'
    })
    // 横幅副标题
    CV.text({
      x: 40 * rpx,
      y: 80 * rpx,
      width: canvasWidth,
      color: '#fff',
      size: 28 * rpx,
      align: 'left',
      baseline: 'top',
      text: '超多福利等你来拿'
    })

    // 2. 绘制LOGO - 使用优化的图片加载方式
    try {
      const logoImg = await shareCanvas.loadImg.call(this, 'https://img.sanfu.com/sf_access/uploads/gb0OjTyoUEmQoRHfVK7wJ8LnGlIcoi6j.png', canvas)
      if (logoImg) {
        // LOGO居中
        const logoX = (canvasWidth - logoWidth) / 2
        const logoY = 140 * rpx
        ctx.drawImage(logoImg, logoX, logoY, logoWidth, logoHeight)
      }
    } catch (e) {
      console.error('LOGO加载失败', e)
    }

    // 3. 绘制小程序码（LOGO下方居中）
    if (this.data.qrcodeUrl) {
      try {
        const qrcodeImg = await shareCanvas.loadImg.call(this, this.data.qrcodeUrl, canvas)
        if (qrcodeImg) {
          // 小程序码居中
          const qrcodeX = (canvasWidth - qrcodeSize) / 2
          const qrcodeY = 280 * rpx
          ctx.drawImage(qrcodeImg, qrcodeX, qrcodeY, qrcodeSize, qrcodeSize)
        }
      } catch (error) {
        console.error('绘制小程序码失败:', error)
      }
    }
    // 3.5 绘制底部文字
    CV.text({
      x: canvasWidth / 2,
      y: 670 * rpx,
      width: canvasWidth,
      color: '#999999',
      size: 24 * rpx,
      align: 'center',
      baseline: 'top',
      text: '长按识别小程序码'
    })

    CV.text({
      x: canvasWidth / 2,
      y: 700 * rpx,
      width: canvasWidth,
      color: '#999999',
      size: 22 * rpx,
      align: 'center',
      baseline: 'top',
      text: '添加我为好友'
    })
    // 4. 绘制员工信息（底部三行，左对齐，字段标题小且淡色，内容大且深色）
    const infoStartY = 760 * rpx
    const infoGap = 55 * rpx
    const labelFontSize = 26 * rpx
    const labelColor = '#999'
    const valueFontSize = 34 * rpx
    const valueColor = '#333'
    // 姓名
    CV.text({
      x: 80 * rpx,
      y: infoStartY + 4 * rpx,
      width: 100 * rpx,
      color: labelColor,
      size: labelFontSize,
      align: 'left',
      baseline: 'top',
      text: '姓名：'
    })
    CV.text({
      x: 80 * rpx + 90 * rpx,
      y: infoStartY,
      width: canvasWidth - 2 * 80 * rpx - 90 * rpx,
      color: valueColor,
      size: valueFontSize,
      align: 'left',
      baseline: 'top',
      text: this.data.staffInfo.userName || this.data.staffInfo.name || ''
    })
    // 工号
    CV.text({
      x: 80 * rpx,
      y: infoStartY + infoGap + 4 * rpx,
      width: 100 * rpx,
      color: labelColor,
      size: labelFontSize,
      align: 'left',
      baseline: 'top',
      text: '工号：'
    })
    CV.text({
      x: 80 * rpx + 90 * rpx,
      y: infoStartY + infoGap,
      width: canvasWidth - 2 * 80 * rpx - 90 * rpx,
      color: valueColor,
      size: valueFontSize,
      align: 'left',
      baseline: 'top',
      text: this.data.staffInfo.userId || ''
    })
    // 门店
    CV.text({
      x: 80 * rpx,
      y: infoStartY + infoGap * 2 + 4 * rpx,
      width: 100 * rpx,
      color: labelColor,
      size: labelFontSize,
      align: 'left',
      baseline: 'top',
      text: '门店：'
    })
    CV.text({
      x: 80 * rpx + 90 * rpx,
      y: infoStartY + infoGap * 2,
      width: canvasWidth - 2 * 80 * rpx - 90 * rpx,
      color: valueColor,
      size: valueFontSize,
      align: 'left',
      baseline: 'top',
      text: this.data.staffInfo.shoName || this.data.staffInfo.shopName || ''
    })

    // 5. 生成临时图片 - Canvas 2.0 方式
    wx.canvasToTempFilePath({
      canvas,
      width: Math.round(canvasWidth),
      height: Math.round(canvasHeight),
      success: res => {
        this.setData({
          qrcodeUrl: res.tempFilePath
        })
      },
      fail: error => {
        console.error('生成图片失败:', error)
      }
    })
  },

  /**
   * 保存名片到相册
   */
  async saveQrcode() {
    if (!this.data.qrcodeUrl) {
      return
    }

    try {
      // 获取用户授权
      const setting = await wx.getSetting()
      if (!setting.authSetting['scope.writePhotosAlbum']) {
        await wx.authorize({
          scope: 'scope.writePhotosAlbum'
        })
      }

      // 保存到相册
      await wx.saveImageToPhotosAlbum({
        filePath: this.data.qrcodeUrl
      })

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('保存名片失败:', error)
      wx.showToast({
        title: '保存失败，请检查相册权限',
        icon: 'none'
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})
