var COS = require('cos-wx-sdk-v5');
var config = {
    stsUrl: 'https://sns.sanfu.com/apis/cos/',
    Bucket: 'sanfu-1255324977',
    Region: 'ap-shanghai',
  };
var cos = new COS({
    getAuthorization: function (options, callback) {
        wx.request({
            method: 'GET',
            url: config.stsUrl, // 服务端签名，参考 server 目录下的两个签名例子
            dataType: 'json',
            success: function (result) {
                var data = result.data;
                callback({
                  Authorization: data && data.authorization,
                    TmpSecretId: data && data.tmpSecretId,
                    TmpSecretKey: data && data.tmpSecretKey,
                  XCosSecurityToken: data && data.xCosSecurityToken,
                  ExpiredTime: data && data.expiredTime,
                });
            }
        });
    },
});

module.exports = cos;