const app = getApp()
import Share from '../../../../components/common/share/index.js'
import shareCanvas from '../../../../utils/shareCanvas.js'
Page({
  data: {
    cusid: '',
    current: 0,
    discard: '',
    list: [{
      data: [],
      has_more: 0,
      page: 1,
    }, {
      data: [],
      has_more: 0,
      page: 1
    }],
    loadingText: ['点击加载更多', '正在加载...', '没有更多了~'],
    shareItem: '',
    showqrcode: true,
  },
  onLoad: function(options) {
    if (options.cusid) {
      this.data.cusid = options.cusid
      this.setData({
        discard: options.discard,
      })
    }
  },
  onReady() {
    this.getList()
  },
  onPullDownRefresh: function() {
    wx.stopPullDownRefresh()
    this.setData({
      list: [{
        data: [],
        has_more: 0,
        page: 1,
      }, {
        data: [],
        has_more: 0,
        page: 1
      }],
    })
    this.getList()
  },
  ...shareCanvas,
  changeList: function(e) {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 200
    })
    let i = e.currentTarget.dataset.i
    this.setData({
      current: i
    })
    if (this.data.list[i].data.length == 0)
      this.getList()
  },
  getList: function() {
    let i = this.data.current
    if (i == 0) {
      this.getGoods()
    } else {
      this.getComb()
    }
  },
  getGoods: function() {
    if (this.data.list[0].data.length > 0 || this.data.list[0].has_more != 0) return
    this.setData({
      ['list[0].has_more']: 1
    })
    app.reqGet('ms-sanfu-wap-goods/listRecommendGoods', {
      sid: wx.getStorageSync('sid'),
      cusId: this.data.cusid,
      shoId: app.local.get('sho_id') || wx.getStorageSync('sho_id') || 'GZW'
    }, res => {
      this.setData({
        ['list[0].has_more']: 2
      })
      if (res.success) {
        let list = [],
          list1 = [],
          list2 = []
        for (let i in res.data) {
          if (res.data[i].type == 2) {
            list2.push(res.data[i])
          } else {
            list1.push(res.data[i])
          }
        }
        let j1 = 0
        for (let i in list2) {
          if (i > 0 && i % 3 == 0 && j1 < list1.length) {
            list.push(list1[j1])
            j1++
          }
          list.push(list2[i])
        }
        for (j1; j1 < list1.length; j1++) {
          list.push(list1[j1])
        }
        if (list.length % 2 != 0) list.pop()
        this.setData({
          ['list[0].data']: list
        })
      } else {
        this.setData({
          ['list[0].has_more']: 0
        })
        app.util.reqFail(res)
      }
    })
  },
  getComb: async function() {
    if (this.data.list[1].has_more != 0) return
    this.setData({
      ['list[1].has_more']: 1
    })
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/combination/page', {
      sid: wx.getStorageSync('sid'),
      shoId: app.local.get('sho_id') || wx.getStorageSync('sho_id') || 'GZW',
      cusId: this.data.cusid,
      type: 1,
      page: this.data.list[1].page,
      pageSize: 12,
    })
    if (res.success) {
      let data = res.data.length > 0 && res.data || []
      this.data.list[1].data = [...this.data.list[1].data, ...data]
      if (data.length < 12) {
        this.data.list[1].has_more = 2
      } else {
        this.data.list[1].has_more = 0
      }

      this.setData({
        ['list[1]']: this.data.list[1]
      })
    } else {
      this.setData({
        ['list[1].has_more']: 0
      })
      app.util.reqFail(res)
    }
  },
  closeQR() {
    this.setData({
      showqrcode: true
    })
  },
  showShare(e) {
    console.log('showShare')
    let i = e.currentTarget.dataset.index
    let current = this.data.current
    let item = this.data.list[current].data[i]
    let shareObj
    let isQy = 0
    if (wx.getSystemInfoSync().environment == 'wxwork') {
      isQy = 1
    }
    if (current == 0) {
      shareObj = {
        title1: item.goodsName + item.goodsSn,
        salePrice: item.salePrice,
        imgList: [item.lImg || ''],
        show_nav: true,
      }

      this.data.shareItem = {
        title: item.goodsName,
        path: `/pages/goodsDisplay/goodsDisplay?goods_sn=${item.goodsSn}`,
        imageUrl: item.lImg,
      }
    } else {
      this.data.shareItem = {
        title: item.combTitle,
        path: '/pages/goods/conbinedGoods/detail/detail?combId=' + item.combId,
        imageUrl: item.coverUrl,
      }

      shareObj = {
        title1: item.combTitle,
        title2: item.combDesc,
        wxpath: '/pages/goods/conbinedGoods/detail/detail?combId=' + item.combId,
        imgList: [item.coverUrl || ''],
      }
    }

    if (isQy) {
      this.data.shareItem.path += `&disshop=${app.local.get('sho_id')||''}&discard=${wx.getStorageSync('discard')||''}&from=qymall`
    } else {
      this.data.shareItem.path += `&disshop=${app.local.get('sho_id')||''}&discard=${wx.getStorageSync('discard')||''}`
    }


    Share().then(el => {
      console.log('微信好友')

    }).catch(el => {
      this.sharecanvas(shareObj)
    })
  },
  onShareAppMessage: function(options) {
    let shareObj = this.data.shareItem
    if (!shareObj || options.from == 'menu') {
      let pages = getCurrentPages()
      pages = pages[pages.length - 1]
      let route = pages.route + `?cusid=${this.data.cusid}&disshop=${app.local.get('sho_id')||''}&discard=${wx.getStorageSync('discard')||''}`
      if (wx.getSystemInfoSync().environment == 'wxwork')
        route += '&from=qymall'
      shareObj = {
        title: '商品推荐',
        path: route
      }
    }
    // 返回shareObj
    return shareObj;
  },
  saveImage: function() {
    // console.log(1111)
    let that = this
    wx.saveImageToPhotosAlbum({
      filePath: that.data.tmppath,
      success: function(data) {
        wx.showToast({
          title: "保存成功"
        })
      },
      fail(err) {
        wx.hideLoading()
        wx.hideToast()
        if (err.errMsg === "saveImageToPhotosAlbum:fail:auth denied" || err.errMsg === "saveImageToPhotosAlbum:fail auth deny" || err.errMsg === "saveImageToPhotosAlbum:fail authorize no response") {
          // 这边微信做过调整，必须要在按钮中触发，因此需要在弹框回调中进行调用

          wx.showModal({
            title: '提示',
            content: '需要您授权保存相册',
            success: res => {
              if (res.confirm)
                wx.openSetting({
                  success(settingdata) {
                    console.log("settingdata", settingdata)
                    if (settingdata.authSetting['scope.writePhotosAlbum']) {
                      wx.showToast({
                        title: '获取权限成功,再次点击图片即可保存',
                        icon: 'none',
                        duration: 3000

                      })
                    } else {
                      wx.showToast({
                        title: '获取权限失败，将无法保存到相册哦~',
                        icon: 'none',
                        duration: 3000

                      })

                    }
                  },

                })
            }
          })
        } else if (err.errMsg == "saveImageToPhotosAlbum:fail cancel") {
          wx.showToast({
            title: '已取消保存~',
            icon: 'none',
            duration: 2000

          })
        } else {
          wx.showToast({
            title: '保存失败：' + err.errMsg,
            icon: 'none',
            duration: 5000
          })
        }
        console.log(err, that.data.tmppath)
      }
    })
  },
  openComb(e) {
    let id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/goods/conbinedGoods/detail/detail?combId=' + id
    })
  },
  openGoods(e) {
    let id = e.currentTarget.dataset.id
    app.toH5(`goods/goodsDisplay?goods_sn=${id}`);
  }
})
