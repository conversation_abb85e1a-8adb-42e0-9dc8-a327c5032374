@import "/font/icon_f7.wxss";
page {
  background: #f3f3f3;
}

.top {
  background: #f3f3f3;
  z-index: 100;
  padding: 20rpx;
}

.nav{
  background: #fff;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
}

.nav view {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  font-size: 30rpx;
  color: #888;
}

.nav .active{
  position: relative;
  color: #fd6c90;
}

.nav .active::after{
  width: 20%;
  height: 6rpx;
  content: ' ';
  left: 40%;
  bottom: 10rpx;
  position: absolute;
  background: #fd6c90;
  border-radius: 50rpx;
}




.tabnav {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 10;
}

.tabs {
  display: flex;
  height: 100rpx;
  align-items: center;
  position: relative;
  padding-right: 50rpx;
}

.tab-item {
  position: relative;
  display: inline-flex;
  flex-basis: 10%;
  flex: 0 0 20%;
  text-align: center;
  padding: 0 20rpx;
  height: 60rpx;
  align-items: center;
  justify-content: center;
  color: #999;
  border: 2rpx solid #bbb;
  border-radius: 36rpx;
  margin: 0 20rpx;
}

.tab-active {
  padding: 2rpx 20rpx;
  border-color: transparent;
  color: #fff;
}

.tab-active:after {
  content: '';
  position: absolute;
  top: -2rpx;
  bottom: -2rpx;
  left: -2rpx;
  right: -2rpx;
  background: linear-gradient(90deg, #fd6c90, #f02e72);
  border-radius: 36px;
  content: '';
  z-index: -1;
}


.goo-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 18rpx;
}

.goo-item {
  background: #fff;
  border-radius: 12rpx;
  width: 348rpx;
  margin-left: 18rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-bottom: 12rpx;
  margin-bottom: 18rpx;
  box-shadow: 2rpx 2rpx 12rpx #eee;
}

.goo-item .goo-img {
  height: 410rpx;
  background: #f1f1f1;
}

.goo-name {
  color: #3b3b3b;
  padding: 0 10rpx;
  margin: 10rpx 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  text-overflow: ellipsis;
  font-size: 24rpx;
  line-height: 1.35;
}

.goo-price {
  font-weight: 700;
  color: #f02e72;
  font-size: 32rpx;
  padding: 0 10rpx;
}


.comb-container {
  overflow: hidden;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 20rpx;
}


.comb-item {
  background: #fff;
  border-radius: 12rpx;
  width: 100%;
  min-height: 240rpx;
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.comb-item .comb-img {
  width: 240rpx;
  height: 240rpx;
  border-radius: 12rpx;
}

.comb-content {
  flex: 1;
  min-height: 220rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  padding: 18rpx;
}

.comb-title {
  color: #3b3b3b;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  white-space: normal;
  text-overflow: ellipsis;
  font-size: 28rpx;
  line-height: 1.5;
  font-weight: 700;
}

.comb-subtitle {
  color: #6b6b6b;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
  white-space: normal;
  text-overflow: ellipsis;
  font-size: 24rpx;
  line-height: 1.3;
  margin: 10rpx 0;
}

.comb-other {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #222;
  font-size: 28rpx;
}

.iconDG-fenxiang{
  font-weight: 700;
  color: #888;
  padding: 20rpx;
  margin: -10rpx 0;
}

/* 二维码相关 */

.qrcode {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.shareCanvas {
  background: none;
}

.dis-top {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
}

.dis-top view {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  margin: 0 !important;
  padding: 10px 0;
  color: #555; 
  border-radius: 50px;
  box-shadow: 1px 1px 3px #999;
  flex:1;
}

.dis-top button {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  font-size: 13px;
  margin-left: 10px;
  padding: 12px 0;
  color: #555; 
  border-radius: 50px;
  box-shadow: 1px 1px 3px #999;
  line-height: 1 !important;
  background: #fff;
}

.hover {
  background: #f3f3f3;
}

