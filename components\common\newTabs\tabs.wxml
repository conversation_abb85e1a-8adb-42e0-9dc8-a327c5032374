<view class="con" style="top:{{top}};{{styles}}">
  <scroll-view scroll-left="{{ scrollLeft }}" scroll-x scroll-with-animation class="tabnav">
    <view wx:if="{{themeType == 1}}" style="display: flex;align-items: flex-end;position: relative;padding-bottom: 11px;padding-top: 10px;">
      <view class="tab tab-item {{index == active?'navSelect':''}}" wx:for="{{list}}" wx:key="index"  bindtap="onChange" data-index="{{index}}" 
      wx:if="{{key?item[key]:item}}">
        <view class="bgCircle" wx:if="{{index == active}}"></view>
        <text class="navSelectTxt">{{key?item[key]:item}}</text>
      </view>
    </view>

    <view wx:if="{{themeType == 2}}" style="display: flex;align-items: center;position: relative;">
      <view class="tab tab-item-Ordinary {{index == active?'navSelectOrdinary':''}}" wx:for="{{list}}" wx:key="index" bindtap="onChange" data-index="{{index}}" wx:if="{{item.menu_name}}">
        <text class="navSelectTxt">{{item.menu_name}}</text>
        <view class="bgLine" style="background: {{index == active?'#E60012':'transparent'}};"></view>
      </view>
    </view>

  </scroll-view>
</view>