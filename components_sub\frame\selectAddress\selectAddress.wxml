<view class="selectAddress" id="animation">
  <view class="serDes">
    请选择收货地址
    <text class="sficon sf-a-close-line11 closeIcon" bindtap="close"></text>
  </view>
  <view style="clear:both"></view>
  <view class="description">
    <scroll-view scroll-y class="scroll-view">
      <block wx:if="{{customerAddressList.length > 0}}" wx:for="{{customerAddressList}}" wx:key="index">
        <view class="act-item {{index + 1 != customerAddressList.length ?'borderBottom':''}}" bindtap="selAdd" data-no="{{item.seqNo}}">
          <view class="act-item-1">
            <view class="contacts">
              <text class="defaultAdr" wx:if="{{item.isdefault}}">默认</text>
              <text class="contactsName">{{item.consignee}}</text>
              <text class="mobile">{{item.mobile}}</text>
            </view>
            <view class="act-adress">{{item.province}} {{item.city}} {{item.county}} {{item.vill}} {{item.street}} {{item.address}}</view>
          </view>
          <view class="act-item-3">
            <view class="uni-icon uni-icon-checkbox-filled {{seqNo == item.seqNo?'':'transparent'}}"></view>
          </view>
        </view>
      </block>
      <block wx:if="{{addressList.length > 0}}" wx:for="{{addressList}}" wx:key="index">
        <view class="act-item" bindtap="selAdd2" data-i="{{index}}" style="width:100%;color:#434343;margin:15rpx 0;border-bottom: 2rpx solid #F6F6F6;">
          <view class="act-item-1">
            <view style="display: flex;align-items: center;justify-content: center;">
              <text style="font-weight: 700;">{{item.consignee}}</text>
              <text>({{item.mobile}})</text>
            </view>
            <view style="margin-top:10rpx;width:100%;overflow:hidden;white-space: nowrap;color: #949494;font-size:22rpx;display: flex;align-items: center;max-width: 640rpx;">
              <text class="sficon sf-map-pin-time-line" style="vertical-align: middle;font-size: 34rpx;"></text>
              <text style="overflow: hidden;text-overflow: ellipsis;">{{item.province}} {{item.city}} {{item.county}}  {{item.street}} {{item.address}}</text>
            </view>
          </view>
          <view class="act-item-3 uni-icon uni-icon-arrowright" style="font-size: 40rpx;"></view>
        </view>
      </block>
    </scroll-view>
  </view>
  <view class="confirm">
    <view class="addAddress" bindtap="goAddAddress">添加收货地址</view>
    <view class="manageAddress" bindtap="goAdd">管理收货地址</view>
  </view>
</view>
<sf-loading full wx:if="{{showLoading}}" />
