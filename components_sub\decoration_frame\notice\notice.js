const app = getApp()
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    config: {
      type: null,
      value: ''
    },
    unitData: {
      type: null,
      value: ''
    },
    pid: {
      type: null,
      value: ''
    },
    index: {
      type: null,
      value: ''
    }
  },
  data: {},
  attached() {},
  detached() {},
  pageLifetimes: {
    hide: function() {}
  },
  methods: {
    open() {
      const item = this.data.unitData
      console.log(item);
      try {
        app.sf.track('mallpage_click', {
          track_cfg_id: this.data.pid,
          track_title: this.data.config.unitName || '公告',
          text: item.trackMemo
        })
      } catch (e) {}
      if (item.link) {
        app.toH5(item.link)
      }
    }
  }
});
