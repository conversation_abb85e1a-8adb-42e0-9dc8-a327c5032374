<wxs src="../../../../utils/utils.wxs" module="utils"></wxs>
<view class="member-item" bindtap="toInfo">
  <image class="member-img" src="{{user.headImgUrl}}"></image>
  <view class="member-content">
    <view class="member-nick">
      <text style="margin-right: 30rpx;">{{user.nickname}}</text>
      <view wx:if="{{user.isStar!=-1}}" catchtap="setStar" class="tag1" style="margin-right: 30rpx;">{{user.isStar?'取消置顶':'设为置顶'}}</view>
      <view class="tag1" wx:if="{{user.hasBirthBag}}">顾客生日</view>
    </view>
    <view class="member-other">
      <text>{{user.curCusId}} </text>
      <text>{{user.cardLevelName}}</text>
      <text wx:if="{{user.upgradeStr}}" style="color: #ff3d3d;">({{user.upgradeStr}})</text>
    </view>
    <view class="member-other">会员来源：{{user.userSource>=0&&(user.userSource==1?'导购分销':'企业微信')||''}}</view>
  </view>
  <view class="uni-icon uni-icon-arrowright"></view>
</view>

<view class="consume-list">
  <view class="consume-item" style="font-size: 26rpx;color: #777;">
    <view class="consume-one" style="width: 100rpx;"></view>
    <view class="consume-one" style="flex:1;" bindtap="showtip" data-i="1">购买订单数<text class="uni-icon uni-icon-info" style="margin-top: -20rpx;"></text></view>
    <view class="consume-one" style="flex:1;">消费金额</view>
    <view class="consume-one" style="flex:1;">最近购买时间</view>
  </view>
  <view class="consume-item" wx:for="{{consume}}" wx:key="index">
    <view class="consume-one" style="width: 100rpx;font-size: 26rpx;color: #777;">{{item.isOnline?'线上':'线下'}}</view>
    <view class="consume-one" style="flex:1;">{{item.dealCnt||0}}</view>
    <view class="consume-one" style="flex:1;">￥{{item.saleMoney||0}}</view>
    <view class="consume-one" style="flex:1;">{{item.lastBuyDate||'暂无'}}</view>
  </view>
</view>

<view class="tags-container" wx:if="{{user.customerLabelInfoResDtoList&&user.customerLabelInfoResDtoList.length>0}}">
  <!-- <view class="title">会员标签</view> -->
  <block wx:for="{{user.customerLabelInfoResDtoList}}" wx:key="index">
    <view class="title">{{item.labelGroupName}}</view>
    <collapse border="{{false}}" showTitle="{{false}}" minHeight="50" dynamic showBtn itemStyle="padding:0" moreStyle="justify-content: center;" tapUnfold>
      <view class="tags-item">
        <view wx:for="{{item.labelNameList}}" wx:key="index">{{item}}</view>
      </view>
    </collapse>
  </block>
  <!--  <view wx:else class="tags-item">
    <view>暂无标签</view>
  </view> -->
  <!-- <view bindtap="changeTags" style="font-size: 28rpx;color: #0705ed;display: flex;align-items: center;justify-content: center;padding: 10rpx 0;">{{showTagsMore?'收起':'展开'}}
    <text class="uni-icon uni-icon-arrowdown" style="{{showTagsMore?'transform: rotate(-180deg);':''}};transition: all .5s;"></text>
  </view> -->
</view>

<view class="btn-box">
  <view hover-class="hover" hover-start-time="20" hover-stay-time="100" bindtap="toGoods">智能推荐商品</view>
  <view bindtap="toActive" hover-class="hover" hover-start-time="20" hover-stay-time="100">智能推荐活动</view>
  <view wx:if="{{user.externalUserId}}" bindtap="toChat" hover-class="hover" hover-start-time="20" hover-stay-time="100">打开会话</view>
  <view bindtap="toSale" hover-class="hover" hover-start-time="20" hover-stay-time="100">查看订单</view>
</view>

<view class="behavior-top" id="top">
  <view style="border-right: 2rpx solid #ddd;color:{{behaviorIndex==0?'#222':'#aaa'}};" bindtap="changeBehave" data-i="0">浏览动态<text class="uni-icon uni-icon-info" catchtap="showtip" data-i="2"></text></view>
  <view style="color:{{behaviorIndex==1?'#222':'#aaa'}};" bindtap="changeBehave" data-i="1">交易动态<text class="uni-icon uni-icon-info" catchtap="showtip" data-i="3"></text></view>
</view>
<view wx:for="{{behaviorList}}" wx:key="index" style="display:{{behaviorIndex==index?'block':'none'}};">
  <view class="behavior-box" wx:if="{{item.data.length>0}}" bindtap="changeShow">
    <view class="time" style="padding-bottom: 0;"></view>
    <view class="behavior-item" style="padding-bottom: 0;">
      <view style="color: #dd000d;">全部{{item.show?'收起':'展开'}}</view>
    </view>
  </view>
  <view class="behavior-box" wx:for="{{item.data}}" wx:key="index2" wx:for-item="item2" wx:for-index="index2" wx:key="index2">
    <view class="time">{{item2.createTime||item2.dayTime}}</view>
    <view class="behavior-item">
      <view wx:if="{{item2.data.length==0}}" style="font-size: 28rpx;display: flex;align-items: flex-end;height: 100%;color: #aaa;">暂无动态</view>
      <block wx:for="{{item2.data}}" wx:for-item="item3" wx:for-index="index3" wx:key="index3">
        <collapse show="{{item.show}}" slotTitle border="{{false}}" duration="200" itemStyle="padding:0" titleStyle="padding:0;padding-top:20rpx">
          <view slot="title" class="item-title">
            <view class="title-dot"></view>
            <view>
              <block wx:if="{{item3.key=='addCartGoodsList'}}">加购了商品</block>
              <block wx:if="{{item3.key=='browseGoodsList'}}">浏览了商品</block>
              <block wx:if="{{item3.key=='collectGoodsList'}}">收藏了商品</block>
              <block wx:if="{{item3.key=='customerCouponList'}}">优惠券即将过期</block>
              <block wx:if="{{item3.key=='netOrderList'}}">商城订单</block>
              <block wx:if="{{item3.key=='returnOrderList'}}">退款订单</block>
              <block wx:if="{{item3.key=='shopOrderList'}}">门店订单</block>
              <block wx:if="{{item3.key=='waitPayOrderList'}}">未支付订单</block>
              <block wx:if="{{item3.key=='visit'}}">访问了商城</block>
            </view>
          </view>
          <view class="item-content" wx:if="{{item3.data.length>0}}">
            <block wx:for="{{item3.data}}" wx:for-item="item4" wx:for-index="index4" wx:key="index4">
              <text user-select class="sub-title">
                <block wx:if="{{item4.braName&&item4.braCount}}">{{item4.braName}} {{item4.braCount}}件</block>
                <block wx:if="{{item4.tbOrdId}}">订单号：{{item4.tbOrdId}}</block>
              </text>
              <view class="img-list">
                <block wx:for="{{item4.goodsImgList}}" wx:for-item="item5" wx:for-index="index5" wx:key="index5">
                  <image bindtap="openImg" bindlongpress="openGoods" data-id="{{item5.goodsSn}}" data-i="{{index5}}" data-item="{{item4.goodsImgList}}" lazy-load src="{{utils.jpg2jpeg(item5.goodsImgUrl)}}">
                    <text>{{item5.goodsSn}}</text>
                  </image>
                </block>
              </view>
              <view class="coupon" wx:if="{{item3.key=='customerCouponList'}}">
                <view class="value">￥{{item4.money}}</view>
                <view class="info">
                  <view class="name">{{item4.name}}</view>
                  <view class="tip">{{item4.validEnd}}到期</view>
                </view>
              </view>
            </block>
          </view>
        </collapse>
      </block>
    </view>
  </view>
  <view wx:if="{{item.has_more != 4}}" class="show_end" bindtap="onReachBottom">
    <view wx:if="{{item.has_more == 1}}" class="loadmore-icon"></view>
    <text wx:if="{{item.has_more <= 10}}">{{ loadingText[item.has_more] }}</text>
    <text wx:else decode style="word-break: break-all;">{{item.has_more}}</text>
  </view>
</view>
