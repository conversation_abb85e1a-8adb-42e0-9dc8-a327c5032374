const app = getApp()
/**
 * Transition 过渡动画
 * @description 简单过渡动画组件
 * @tutorial https://ext.dcloud.net.cn/plugin?id=985
 * @property {Boolean} show = [false|true] 控制组件显示或隐藏
 * @property {Array} modeClass = [fade|slide-top|slide-right|slide-bottom|slide-left|zoom-in|zoom-out] 过渡动画类型
 *  @value fade 渐隐渐出过渡
 *  @value slide-top 由上至下过渡
 *  @value slide-right 由右至左过渡
 *  @value slide-bottom 由下至上过渡
 *  @value slide-left 由左至右过渡
 *  @value zoom-in 由小到大过渡
 *  @value zoom-out 由大到小过渡
 * @property {Number} duration 过渡动画持续时间
 * @property {String} styles 组件样式，同 css 样式
 */
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
    // addGlobalClass: true
    virtualHost: true
  },
  properties: {
    zIndex: {
      type: Number,
      value: 1
    },
    show: {
      type: Boolean,
      value: false
    },
    modeClass: {
      type: Array,
      value: ['fade']
    },
    duration: {
      type: Number,
      value: 350
    },
    styles: {
      type: String,
      value: ''
    }
  },
  data: {
    isShow: false,
    transform: '',
    ani: { in: '',
      active: ''
    }
  },
  attached() {},
  watch: {
    'show': function(newVal) {
      if (newVal) {
        this.open()
      } else {
        this.close()
      }
    }
  },
  methods: {
    change() {
      this.triggerEvent('click', {
        detail: this.data.isShow
      });
    },
    open() {
      clearTimeout(this.data.timer)
      this.data.isShow = true
      this.data.transform = ''
      this.data.ani.in = ''
      for (let i in this.getTranfrom(false)) {
        if (i === 'opacity') {
          this.data.ani.in = 'fade-in'
        } else {
          this.data.transform += `${this.getTranfrom(false)[i]} `
        }
      }
      this.setData({
        isShow: this.data.isShow,
        transform: this.data.transform,
        ani: this.data.ani
      })
      wx.nextTick(() => {
        setTimeout(() => {
          this._animation(true)
        }, 50)
      })

    },
    close(type) {
      clearTimeout(this.data.timer)
      this._animation(false)
    },
    _animation(type) {
      let styles = this.getTranfrom(type)

      this.data.transform = ''
      for (let i in styles) {
        if (i === 'opacity') {
          this.data.ani.in = `fade-${type?'out':'in'}`
        } else {
          this.data.transform += `${styles[i]} `
        }
      }
      this.setData({
        transform: this.data.transform,
        ani: this.data.ani
      })
      this.data.timer = setTimeout(() => {
        if (!type) {
          this.data.isShow = false
        }
        this.triggerEvent('change', {
          detail: this.data.isShow
        });
        this.setData({
          isShow: this.data.isShow,
        })
      }, this.properties.duration)

    },
    getTranfrom(type) {
      let styles = {
        transform: ''
      }
      this.properties.modeClass.forEach((mode) => {
        switch (mode) {
          case 'fade':
            styles.opacity = type ? 1 : 0
            break;
          case 'slide-top':
            styles.transform += `translateY(${type?'0':'-50%'}) `
            break;
          case 'slide-right':
            styles.transform += `translateX(${type?'0':'100%'}) `
            break;
          case 'slide-bottom':
            styles.transform += `translateY(${type?'0':'50%'}) `
            break;
          case 'slide-left':
            styles.transform += `translateX(${type?'0':'-100%'}) `
            break;
          case 'zoom-in':
            styles.transform += `scale(${type?1:0.8}) `
            break;
          case 'zoom-out':
            styles.transform += `scale(${type?1:1.2}) `
            break;
        }
      })
      return styles
    },

    // _modeClassArr(type) {
    //   let mode = this.properties.modeClass
    //   if (typeof(mode) !== "string") {
    //     let modestr = ''
    //     mode.forEach((item) => {
    //       modestr += (item + '-' + type + ',')
    //     })
    //     return modestr.substr(0, modestr.length - 1)
    //   } else {
    //     return mode + '-' + type
    //   }
    // },
    // getEl(el) {
    // 	console.log(el || el.ref || null);
    // 	return el || el.ref || null
    // },

  }
});
