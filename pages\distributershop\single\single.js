// pages/distributershop/single/single.js
import Share from '../../../components/common/share/index.js'
const util = require('../../../utils/util.js')
import common from '../common.js'
const app = getApp()
const prefix = (uri) => {
  return 'ms-sanfu-wap-customer-distribution/distribution/good/' + uri;
}
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showqrcode: true,
    showItem: {},
    shareItem: '',
    showDetail: false,
    tmppath: '',
    page: 1,
    has_more: 4, //对应loadingtext  3：无数据   4，全局loading
    loadingText: ['点击加载更多', '正在加载...', '没有更多了', '暂无该推荐'],
    //分部分类
    bra_list: [{
      id: '',
      name: '全部'
    }, {
      id: '00',
      name: '男装'
    }, {
      id: '01',
      name: '女装'
    }, {
      id: '03',
      name: '内衣'
    }, {
      id: '06',
      name: '美妆'
    }, {
      id: '07',
      name: '淘品'
    }, {
      id: '08',
      name: '鞋包'
    }, ],
    bra_index: 0, //分部索引
    reqData: {
      dateSort: '0',
      priceSort: '',
      saleAmountSort: '',
      classId: ''
    },
    groups: [{
        name: '日期',
        prop: 'dateSort',
        order: 0
      },
      {
        name: '销量',
        prop: 'saleAmountSort',
        order: 0
      },
      {
        name: '价格',
        prop: 'priceSort',
        order: 0
      }
    ],
    singles: [],
    searching: false, //正在搜索
    search_keyword: '', //搜索输入内容
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // this.getPages()
    // this.getBranchs()//获取分类

  },
  onReady() {
    this.getPages()
    this.workTrace(1)
  },
  onPullDownRefresh: function() {
    this.data.page = 1;
    this.setData({
      has_more: 4
    })
    this.getPages()
    wx.stopPullDownRefresh()
  },
  onReachBottom: function() {
    if (this.data.has_more != 0) return;
    this.setData({
      has_more: 1
    })
    this.getPages()
  },

  getBranchs() {
    app.reqGet(prefix('allClass'), {
      sid: wx.getStorageSync('sid'),
    }, res => {
      if (res.success) {
        let item = {
          name: '分类',
          prop: 'classId',
          order: 0,
          type: 1,
          type_array: []
        }
        res.data.map(a => {
          a['name'] = a['className']
        })
        item['type_array'] = res.data
        item['type_array'].push({
          name: '全部',
          classId: ''
        })
        this.data.groups.push(item)
        this.setData({
          groups: this.data.groups
        })

      } else {
        wx.showToast({
          title: res.msg || '服务器请求失败！',
          icon: 'none'
        })
      }
    })
  },
  /**
   * 单品推荐分页接口
   */
  getPages() {

    let obj = this.data.reqData
    obj = {
      ...obj,
      ...{
        braId: this.data.bra_list[this.data.bra_index].id || '',
        sid: wx.getStorageSync('sid'),
        shoId: app.local.get('sho_id'),
        page: this.data.page,
        pageSize: 6
      }
    }
    if (this.data.searching) {
      obj = {
        ...obj,
        ...{
          keyword: this.data.search_keyword
        }
      }
    }
    app.reqGet(prefix('page'), obj, res => {
      if (res.success) {
        let data = res.data
        if (this.data.page == 1) {
          this.data.singles = []
          wx.pageScrollTo({
            scrollTop: 0,
            duration: 200
          })
        }
        for (let i in data.result) this.data.singles.push(data.result[i])
        if (data.totalCount == this.data.singles.length) {
          this.data.has_more = 2
        } else {
          this.data.has_more = 0
          this.data.page++
        }
        if (data.totalCount == 0 || data.totalCount == null) this.data.has_more = 3
        this.setData({
          has_more: this.data.has_more,
          singles: this.data.singles,
        })
      } else
        // console.log(el)
        this.setData({
          has_more: 2
        })
    })
  },
  orderBy(e) {
    let detail = e.detail
    let obj = {}
    detail.all.map(el => {
      if (el.type !== 1 && detail['item'].prop === el.prop) {
        obj[el.prop] = el.order
      } else {
        obj[el.prop] = ''
      }
    })
    // 设置选中分类的值
    if (detail.prop) {
      obj[detail.prop] = detail['checkItem'].classId
    }
    obj = {
      ...this.data.reqData,
      ...obj
    }
    console.log(obj)
    this.data.reqData = obj
    this.data.page = 1
    this.setData({
      has_more: 4
    })
    this.getPages()
  },
  //分类点击修改
  changeBar(e) {
    let index = e.currentTarget.dataset.index
    if (index == this.data.bra_index) return
    for (let i in this.data.reqData) {
      //分类筛选清空
      this.data.reqData[i] = ''
      if (i == 'dateSort')
        this.data.reqData[i] = 0
    }
    this.selectComponent("#order").reset();

    this.data.page = 1
    this.setData({
      has_more: 4,
      bra_index: index
    })
    console.log(this.data.reqData)

    this.getPages()
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function(options) {
    var that = this;
    let shareItem = that.data.shareItem || this.data.singles[0]

    let shop = app.local.get('sho_id')
    let card = wx.getStorageSync('cardid')
    let endUrl = `/pages/goodsDisplay/goodsDisplay?goods_sn=${shareItem.gooId}&type=SHOP&disshop=${shop}&discard=${card}&shareId=${shareItem.recommendId}`
    if (wx.getSystemInfoSync().environment == 'wxwork')
      endUrl += '&from=qymall'
    // 设置菜单中的转发按钮触发转发事件时的转发内容
    var shareObj = {
      title: /*'￥'+shareItem.salePrice+' '+ */ shareItem.goodsName, // 默认是小程序的名称(可以写slogan等)
      path: endUrl, // 默认是当前页面，必须是以‘/’开头的完整路径
      imageUrl: shareItem.baseImg, //that.data.shareItem.baseImg,     //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      complete: function() {
        // 转发结束之后的回调（转发成不成功都会执行）
      }
    };
    app.sr.track('page_share_app_message', {
      "from_type": options.from,
      "share_title": shareItem.goodsName,
      "share_path": endUrl,
      "share_image_url": shareItem.baseImg
      // more...
    })
    // 来自页面内的按钮的转发
    if (options.from == 'button') {
      var eData = options.target.dataset;
      // 此处可以修改 shareObj 中的内容
      shareObj.path = endUrl
    }
    console.log(shareObj)
    // 返回shareObj
    return shareObj;
  },
  changeDetailStatus(e) {
    if (e.detail && e.detail.show) return
    if (e.detail && e.detail.show == false && !this.data.showDetail) return
    let item = {}
    if (e && e.currentTarget && e.currentTarget.dataset) {
      item = e.currentTarget.dataset.showitem
    }
    this.setData({
      showItem: item || {},
      showDetail: !this.data.showDetail
    })
  },
  closeQR() {
    this.setData({
      showqrcode: true
    })
  },
  showShare(e) {
    let that = this
    let shareItem = e.currentTarget.dataset.shareitem


    that.setData({
      shareItem: shareItem
    })
    Share().then(el => {
      console.log('微信好友')
      that.transfer(shareItem.recommendId, 1, shareItem.gooId)
      // let startCount = shareItem['shareCnt'] || 0
      // that.updateShareCount('share_single', shareItem['recommendId'], startCount)
    }).catch(el => {
      this.transfer(shareItem.recommendId, 1, shareItem.gooId)
      let active_time
      if (shareItem.promoName) {
        active_time = '活动日期：' + shareItem.promoStartTime.replace(/-/g, ".") + ' - ' + shareItem.promoEndTime.replace(/-/g, ".")

      }

      wx.setStorageSync('singleshare', {
        recommendId: shareItem.recommendId,
        gooId: shareItem.gooId,
        title1: shareItem.goodsName + shareItem.gooId,
        salePrice: shareItem.salePrice,
        memberPrice: shareItem.memberPrice,
        price: shareItem.price,
        active: shareItem.promoName,
        active_time: active_time,
        show_nav: true
      })
      wx.navigateTo({
        url: `../groupshare/groupshare?type=single&id=` + shareItem.recommendId,
      })
    })
  },

  //******输入绑定*********
  input: function(e) {
    this.setData({
      [e.currentTarget.dataset.name]: e.detail.value
    })
  },
  //******快速清除输入*********
  clearinput: function(e) {
    console.log(233333)
    this.setData({
      [e.currentTarget.dataset.name]: ''
    })
  },
  //******搜索*********
  search: async function() {
    if (this.data.search_keyword.length <= 0) {
      wx.showToast({
        title: '请输入商品关键词或6位货号',
        icon: 'none'
      })
      return;
    } else {}
    for (let i in this.data.reqData) {
      //分类筛选清空
      this.data.reqData[i] = ''
      if (i == 'dateSort')
        this.data.reqData[i] = 0
    }
    this.setData({
      searching: true,
      bra_index: 0,
      has_more: 4
    })
    this.data.page = 1
    this.getPages()
  },
  closeinput() {
    for (let i in this.data.reqData) {
      //分类筛选清空
      this.data.reqData[i] = ''
      if (i == 'dateSort')
        this.data.reqData[i] = 0
    }
    this.selectComponent("#order").reset();
    this.setData({
      search_keyword: '',
      searching: false,
      bra_index: 0,
      has_more: 4
    })
    this.getPages()

  },
  copy() {
    //复制
    if (this.data.showItem && this.data.showItem.recommendTxt)
      wx.setClipboardData({
        data: this.data.showItem.recommendTxt,
        success(res) {
          wx.getClipboardData({
            success(res) {
              console.log(res.data) // data
            }
          })
        }
      })
  },
  ...common
})
