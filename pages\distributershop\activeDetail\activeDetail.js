// pages/distributershop/active/active.js
import Share from '../../../components/common/share/index.js'
import common from '../common.js'
import shareCanvas from '../../../utils/shareCanvas.js'

const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showLoading: false,
    detail: '',
    showqrcode: true,
    showShareBtn: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function(options) {
    this.id = options.id
    await app.waitSid()
    this.traceRecord({
      operateType: 2,
      scene: 3,
      id: options.id,
      fromCusId: options.discard,
    })
    if (wx.getStorageSync('cardid') == wx.getStorageSync('discard')) {
      this.setData({
        showShareBtn: true
      })
    }
    this.getDetail()
  },
  async getDetail() {
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/act/detail', {
      sid: wx.getStorageSync('sid'),
      flag: 1,
      actId: this.id
    })
    if (res.success) {
      wx.setNavigationBarTitle({
        title: res.data.title,
      })
      this.setData({
        detail: res.data || ''
      })
    } else {
      app.util.reqFail(res)
    }
  },
  closeQR() {
    this.setData({
      showqrcode: true
    })
  },
  showShare(e) {

    Share().then(el => {
      console.log('微信好友')
      this.transfer(this.id, 3)
    }).catch(el => {
      this.transfer(this.id, 3)
      let endUrl = `https://${app.apidomain}/user/disshop-active?id=${this.id}`
      let url = this.data.detail.subtitle.split('链接')[1]
      if (this.data.detail.linkUrl) url = this.data.detail.linkUrl
      this.sharecanvas({
        imgList: [this.data.detail.picture],
        title1: this.data.detail.title,
        title2: this.data.detail.subtitle,
        actId: this.data.detail.actId,
        url: url || endUrl
      })
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function(options) {
    var that = this;
    let endUrl = `https://${app.apidomain}/user/disshop-active?id=${this.id}`
    let cusurl = this.data.detail.subtitle && this.data.detail.subtitle.split('链接')[1] || ''
    cusurl = cusurl.replace(/\s+/g, '')
    if (cusurl) {
      endUrl = cusurl
    }
    if (this.data.detail.linkUrl) endUrl = this.data.detail.linkUrl
    if (endUrl.indexOf('?') == -1) {
      endUrl = endUrl + `?`
    }
    if (wx.getSystemInfoSync().environment == 'wxwork')
      endUrl += '&from=qymall'
    // 设置菜单中的转发按钮触发转发事件时的转发内容
    console.log('endUrl', endUrl)
    var shareObj = {
      title: this.data.detail.title, // 默认是小程序的名称(可以写slogan等)
      path: '/pages/pcweb/pcweb?shareUrl=' + encodeURIComponent(endUrl), // 默认是当前页面，必须是以‘/’开头的完整路径
      imageUrl: this.data.detail.picture, //that.data.shareItem.baseImg,     //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      complete: function() {
        // 转发结束之后的回调（转发成不成功都会执行）
      }
    };

    // 返回shareObj
    return shareObj;
  },
  openH5(e) {
    let url = e.currentTarget.dataset.url
    app.toH5(url)
  },
  ...common,
  ...shareCanvas
})
