/* pages/nearShop/nearShop.wxss */
page {
  background: #f6f6f6;
  padding-bottom: 30rpx;
}


.item-box {
  background: #fff;
  margin: 20rpx auto 0;
  padding: 20rpx 16rpx 20rpx 20rpx;
  width: 690rpx;
  box-shadow: #ccc 0px 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #404040;
  display: flex;
  align-items: center;
  line-height: 1.5;
}

.item-box image{
  /* margin-top: 16rpx; */
 width: 170rpx;
 height: 170rpx;
 border-radius: 8rpx;
 margin-right: 20rpx;
 flex-shrink: 0;
}

.item-box > view{
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-self: stretch;
}

.item-box .right{
  flex-shrink: 0;
  font-size: 22rpx;
  justify-content: flex-end;
  align-items: flex-end;
  color: #888888;
}

.item-box .right .status{
  font-size: 28rpx;
  display: flex;
  align-items: center;
  margin-bottom: 15%;
  font-weight: 700;
  color: #202020;
}

.item-box .right .status.success::before{
  content: '';
  display: flex;
  width: 8rpx;
  height: 8rpx;
  background: #40adae;
  margin-right: 4rpx;
}

.item-box .right .status.fail::before{
  content: '';
  display: flex;
  width: 8rpx;
  height: 8rpx;
  background: #e60012;
  margin-right: 4rpx;
}

.store-box .content .go-btn {
  margin-left: 80rpx;
  min-width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #515151;
  padding-left: 6rpx;
  font-size: 46rpx;
  font-weight: 700;
  background: #e6e6e6;
  border-radius: 50%;
  margin-left: auto;
}

.get-btn {
  width: 300rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #ff6a8f, #f32871);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 70rpx auto 0;
  border-radius: 50rpx;
  font-size: 36rpx;
  color: #fff;
}
