.qrcode {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.shareCanvas {
  background: none;
}

.dis-top {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
}

.dis-top view {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  margin: 0 !important;
  padding: 10px 0;
  color: #555; 
  border-radius: 50px;
  box-shadow: 1px 1px 3px #999;
  flex:1;
}

.dis-top button {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  font-size: 13px;
  margin-left: 10px;
  padding: 12px 0;
  color: #555; 
  border-radius: 50px;
  box-shadow: 1px 1px 3px #999;
  line-height: 1 !important;
  background: #fff;
}
