/* pages/distributershop/groupshare/groupshare.wxss */

image, view, label {
  padding: 0;
  margin: 0;
  overflow: visible;
  box-sizing: border-box;
  font-size: inherit;
  position: relative;
}

page {
  background: #f5f6f7;
  box-sizing: border-box;
  min-height: 100%;
  padding: 0;
  padding-bottom: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.share-bottom {
  z-index: 21;
  position: fixed;
  bottom: 0;
  left: 0;
  height: 100rpx;
  width: 100%;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  background: #fd6c90;
  color: #fff;
}

.share-title {
  padding: 0 20rpx;
  padding-top: 20rpx;
  display: flex;
}

.share-title>text, .share-title>view {
  font-size: 34rpx;
  color: #333;
  display: flex;
  align-items: center;
  font-weight: 700;
}

.share-img-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.share-img-box>.share-img-item {
  width: 126rpx;
  height: 126rpx;
  overflow: hidden;
  background: #eee;
  margin-left: 20rpx;
  margin-top: 20rpx;
  border-radius: 6rpx;
  position: relative;
}

.share-img-box>.share-img-item >image {
  width: 100%;
  height: 100%;
}

.share-img-box>.share-img-item >checkbox {
  position: absolute;
  right: -5px;
  top: -5px;
  transform: scale(0.6);
  z-index: 20;
}

.share-img-box>.share-img-item >.select-no {
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 19;
  background: rgba(151, 130, 197, 0.65);
  display: flex;
  justify-content: center;
  align-items: center;
}

.share-img-box>.share-img-item >.select-no>text {
  font-size: 36rpx;
  color: #fff; 
  font-weight: 700;
}

.radio-box {
  box-sizing: border-box;
  width: 750rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  min-width: 140rpx;
  margin-right: 24rpx;
}

.radio-item>radio {
  transform: scale(0.8);
}

.radio-item>text {
  font-size: 28rpx;
  color: #404040;
}
