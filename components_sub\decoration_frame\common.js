function deleteImg(item) {
  const shopInfo = this.data.shopInfo
  if (!item.openLimit) return 0
  const now = Date.now() + wx.getStorageSync('differenceTime')
  if ((item.endTime && item.endTime < now) || item.beginTime && item.beginTime > now) {
    return 1
  }
  if (!item.imgUrl && !item.link) {
    return 1
  }
  // 返回待处理
  if (!shopInfo.orgId && (item.orgId || item.shops || item.packageIds || item.isGroup2 || item.isGroup)) {
    return 2
  }
  // 主体控制
  if (item.orgId && shopInfo.orgId && item.orgId == shopInfo.orgId) {
    delete item.orgId
  }
  // 展示店
  if (item.shops && shopInfo.dsho_id && item.shops.split(',').findIndex(e => e === shopInfo.dsho_id) > -1) {
    delete item.shops
  }

  // 人群包
  if (item.packageIds && Array.isArray(shopInfo.packageIds) && shopInfo.packageIds.some(e => item.packageIds.indexOf(e) > -1)) {
    delete item.packageIds
  }

  // 企微会员isGroup 1 已加入 2未加入
  if (shopInfo.isGroup >= 0 && ((item.isGroup == 1 && shopInfo.isGroup) || (item.isGroup == 2 && !shopInfo.isGroup))) {
    delete item.isGroup
  }
  // 企微会员isGroup2 1 已加入 2未加入
  if (shopInfo.isGroup2 >= 0 && ((item.isGroup2 == 1 && shopInfo.isGroup2) || (item.isGroup2 == 2 && !shopInfo.isGroup2))) {
    delete item.isGroup2
  }
  if (item.orgId || item.shops || item.packageIds || item.isGroup || item.isGroup2)
    return 1
  return 0
}

module.exports = {
  deleteImg
}
