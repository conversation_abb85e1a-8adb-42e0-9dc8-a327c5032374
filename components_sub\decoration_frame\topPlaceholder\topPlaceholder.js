const app = getApp()
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    config: {
      type: null,
      value: ''
    },
    unitData: {
      type: null,
      value: ''
    },
    index: {
      type: null,
      value: ''
    },
    menuButton: {
      type: null,
      value: ''
    }
  },
  data: {
    h: 0
  },
  attached() {
    try {
      const pages = getCurrentPages()
      const page = pages[pages.length - 1]
      page.swipeChange(this.data.config.color || '#202020')
    } catch (e) {
      //TODO handle the exception
    }
    const h = app.$bus.emit('getColumnPageH') || 0
    if (!h) return
    this.setData({
      h
    })
  },
  methods: {}
});
