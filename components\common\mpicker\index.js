const app = getApp()
Component({
  options: {
    virtualHost: true,
    addGlobalClass: true
  },
  properties: {
    headerText: {
      type: String,
      value: '请选择'
    },
    disable: {
      type: Boolean,
      value: false
    },
    value: {
      type: null,
      value: ''
    },
    valueType: {
      type: String,
      value: '' // key/name
    },
    valueClear: { // velue不匹配返回空
      type: Boolean,
      value: true
    },
    data: {
      type: Array,
      value: [],
    },
    listKey: {
      type: Array,
      value: []
    },
    key: {
      type: Array,
      value: []
    },
    name: {
      type: Array,
      value: []
    }
  },
  data: {
    v: [], //本地数据 索引列表
    pickerlist: [] //选择列表
  },
  observers: {
    'data': function(value) {
      if (value) this.init()
    },
    'value': function(value) {
      if (value) {
        // return
        console.log('old', 'new');
        console.log(this.oldValue, value);
        if (!Array.isArray(value) && value && this.oldValue != value) {
          if (Array.isArray(this.oldValue) && this.oldValue[this.oldValue.length - 1] == value) return
          // console.log('run1valueChange');
          this.valueChange()
        } else if (Array.isArray(value) && JSON.stringify(this.oldValue) != JSON.stringify(value)) {
          // console.log('run2valueChange');
          this.valueChange()
        }
        this.oldValue = value
      }
    }
  },
  attached() {},
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {
      app.$bus.on('setPicker', data => {
        if (Array.isArray(data) && data.length > 0) {
          this.data.data = data
          this.init()
        }
      })
    },
    hide: function() {
      app.$bus.off('setPicker')
    },
    resize: function() {},
  },
  detached: function() {
    app.$bus.off('setPicker')
  },
  methods: {
    init() {
      // 初始化
      //默认数据未开发
      if (!this.data.name || this.data.name.length != this.data.key.length) return
      if (this.data.data.length === 0) {
        this.setData({
          pickerlist: []
        })
        return
      }
      // console.log(typeof this.data.value);
      // console.log('initinit');
      // console.log(this.data.data);
      //初始化选择列表
      this.data.v = new Array(this.data.name.length).fill(0)
      if (this.data.listKey.length != this.data.key.length) this.data.listKey.unshift('')
      this.setList(0)
      this.load = true
      // console.log('start----------');
      this.valueChange()
    },
    valueChange() {
      // 值改变
      if (!this.load) return
      if (!this.data.valueType || !this.data.value) return
      let resetColumn = 0 // 重置起始列
      let iarr = new Array(this.data.name.length).fill(0) // 新索引列表
      let vKey = this.data[this.data.valueType] // key
      let value = ''
      let value2 = ''
      if (Array.isArray(this.data.value) && this.data.value.length == this.data.name.length) {
        // console.log('valueChange_START_ARRAY');
        // 数组型
        let list = this.data.data
        let times = 0
        const getKey = (list, num) => {
          // console.log('times1', times++)
          for (let i in list) {
            let list1 = list[i]
            if (this.data.value[num] == list1[vKey[num]]) {
              if (num == this.data.name.length - 1) {
                if (list1[vKey[num]] == this.data.value[num]) {
                  return [i]
                } else return
              } else {
                let k = getKey(list1[this.data.listKey[num + 1]], num + 1)
                if (k) return [i, ...k]
                else return [i]
              }
            }
          }
          return ''
        }
        value = getKey(list, 0)
      } else {
        // console.log('valueChange_START_ONE');
        // 只有最后一个值
        let list = this.data.data
        let times = 0
        const getKey = (list, num) => {
          // console.log('times2', times++)
          for (let i in list) {
            let list1 = list[i]
            if (JSON.stringify(list1).includes(this.data.value)) {
              if (num == this.data.name.length - 1) {
                if (list1[vKey[num]] == this.data.value) {
                  // console.log(list1[vKey[num]]);
                  return [i]
                } else return
              } else {
                let k = getKey(list1[this.data.listKey[num + 1]], num + 1)
                if (k) return [i, ...k]
              }
            }
          }
          return ''
        }
        value = getKey(list, 0)
      }
      value2 = [...value, ...new Array(this.data.name.length - value.length).fill(0)]
      value = [...value, ...new Array(this.data.name.length - value.length).fill(-1)]
      console.log(value);
      if (value) {
        this.data.v = value2
        this.setData({
          v: this.data.v
        })
        this.chooseconfirm({
          detail: {
            value: value
          }
        })
        this.setList(0)
      }
    },
    setList(column) {
      // console.log('setList', column);
      // 取key初始化每列
      let pickerlist = new Array(this.data.name.length).fill(['请选择'])
      let key = 'data'
      let list = this.data
      let i = column
      for (i in this.data.listKey) {
        if (this.data.listKey[i]) key = this.data.listKey[i]
        let picker = []
        // console.log(key, list[key]);
        for (let j in list[key]) {
          picker.push(list[key][j][this.data.name[i]])
        }
        if (list[key] && list[key].length > 0) {
          list = list[key][this.data.v[i]]
        } else break
        pickerlist[i] = picker
      }
      this.setData({
        pickerlist: pickerlist,
        v: this.data.v
      })
      // console.log(pickerlist, this.data.v);
    },
    PickerChange(e) {
      //选择器滑动操作处理
      let column = parseInt(e.detail.column)
      let value = parseInt(e.detail.value)
      // console.log(column, value);
      this.data.v[column] = value
      for (let i = column + 1; i < this.data.v.length; i++) {
        this.data.v[i] = 0
      }
      if (column == this.data.v.length - 1) { // 最后一列不重置  
        this.setData({
          v: this.data.v
        })
      } else this.setList(column)
    },
    chooseconfirm(e) {
      let v = e.detail.value
      if (!this.data.valueClear && v && v[0] && v[0] == -1) return

      let data = []
      let list = this.data.data
      if (this.data.valueType) {
        this.oldValue = []
      }
      for (let i in v) {
        let index = v[i]
        if (!list || !list[index]) {
          data.push({
            key: '',
            name: '',
            index: -1
          })
          if (this.data.valueType) {
            this.oldValue.push("")
          }
        } else {
          if (this.data.valueType) {
            this.oldValue.push(list[index][this.data[this.data.valueType][i]])
          }
          data.push({
            key: list[index][this.data.key[i]],
            name: list[index][this.data.name[i]],
            index: index
          })
        }
        if (index == -1) {
          list = []
        } else if (this.data.listKey[i * 1 + 1]) {
          list = list[index][this.data.listKey[i * 1 + 1]]
        }
      }
      if ((!Array.isArray(this.data.value) || !this.data.value) && this.data.valueType) {
        this.oldValue = this.oldValue[this.oldValue.length - 1] + ''
      }
      this.triggerEvent('change', data);
    },
  }
});
