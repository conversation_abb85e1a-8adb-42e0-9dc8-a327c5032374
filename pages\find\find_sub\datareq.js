var app = getApp();
var md5 = require('md5');
var base64 = require('../../../utils/Base64.js');
var config = {
  stsUrl: 'https://sns.sanfu.com/apis/cos/',
  Bucket: 'sanfu-1255324977',
  Region: 'ap-shanghai',
};
var cos = require('lib/cos');


function POST(url, params, inum) { //post提交,同步
  var that = this;
  //console.log(app.globalData.sys_url);
  let promise = new Promise(function(resolve, reject) {
    wx.request({
      url: app.globalData.sys_url + url,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'authorization': app.globalData.userInfo.token
      },
      data: params,
      method: 'POST',
      success: function(res) {
        if (res.data.code == 1009) { //重新获取accesstoken
          var vtime = Date.now();
          var vkey = md5.hexMD5(app.globalData.appid + vtime + app.globalData.openid + app.globalData.appkey);
          vkey = vkey.substring(0, 20);
          var idata = {
            "unionid": app.globalData.unionid,
            "openid": app.globalData.openid,
            "sanfuid": app.globalData.sanfu_id,
            "appid": app.globalData.appid,
            "time": vtime,
            "appkey": vkey
          }
          wx.request({
            url: app.globalData.sys_url + "accesstoken/accountnew",
            header: {
              'content-type': 'application/x-www-form-urlencoded',
            },
            data: idata,
            method: 'POST',
            success: (sysres) => {
              if (sysres.data.code == 1000) {
                app.globalData.userInfo = sysres.data.token;
                if (app.globalData.userInfo.u_namebase64 != null) {
                  var dcode = base64.decode(app.globalData.userInfo.u_namebase64);
                  if (dcode != "") {
                    app.globalData.userInfo.u_namebase64 = dcode;
                    app.globalData.userInfo.adddate = that.getTimestamp();
                  }
                }
                POST1(url, params, inum);
              }
            }
          })
        } else {
          app.netWorkData.result[inum] = res.data;
          resolve();
        }
      }
    })
  });
  return promise
}

function PostSanfu(url, params, inum) { //三福提交
  var that = this;
  //console.log(app.globalData.sys_url);
  let promise = new Promise(function(resolve, reject) {
    wx.request({
      url: app.globalData.sanfu_url + url,
      header: {
        'content-type': 'application/json',
      },
      data: JSON.stringify(params),
      method: 'POST',
      success: function(res) {
        app.netWorkData.result[inum] = res.data;
        resolve();
      }
    })
  });
  return promise
}

function PostSanfuALL(url, params, inum) { //三福提交
  var that = this;
  //console.log(app.globalData.sys_url);
  let promise = new Promise(function(resolve, reject) {
    wx.request({
      url: url,
      header: {
        'content-type': 'application/json',
      },
      data: JSON.stringify(params),
      method: 'POST',
      success: function(res) {
        app.netWorkData.result[inum] = res.data;
        resolve();
      }
    })
  });
  return promise
}

function GetSanfu(url, params, inum) { //三福提交
  var that = this;
  //console.log(app.globalData.sys_url);
  let promise = new Promise(function(resolve, reject) {
    wx.request({
      url: app.globalData.sanfu_url + url,
      // header: {
      //   'content-type': 'application/x-www-form-urlencoded',
      // },
      data: JSON.stringify(params),
      method: 'GET',
      success: function(res) {
        app.netWorkData.result[inum] = res.data;
        resolve();
      }
    })
  });
  return promise
}




function GetSanfuAll(url, params, inum) { //三福提交
  var that = this;
  //console.log(app.globalData.sys_url);
  let promise = new Promise(function(resolve, reject) {
    wx.request({
      url: url,
      // header: {
      //   'content-type': 'application/x-www-form-urlencoded',
      // },
      data: JSON.stringify(params),
      method: 'GET',
      success: function(res) {
        app.netWorkData.result[inum] = res.data;
        resolve();
      }
    })
  });
  return promise
}


function POST1(url, params, inum) { //post提交,同步
  let promise = new Promise(function(resolve, reject) {
    wx.request({
      url: app.globalData.sys_url + url,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'authorization': app.globalData.userInfo.token
      },
      data: params,
      method: 'POST',
      success: function(res) {
        app.netWorkData.result[inum] = res.data;
        resolve();
      }
    })
  });
  return promise
}

function addLiked(nid, authorid, orliked, inum, cn_like) { //取消或添加点赞
  let that = this;
  let promise = new Promise(function(resolve, reject) {
    wx.request({
      url: app.globalData.sys_url + "exists/content_likes_check",
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'authorization': app.globalData.userInfo.token
      },
      data: {
        cl_nid: nid,
        cl_uid: app.globalData.userInfo.id
      },
      method: 'POST',
      success: function(res) {
        if (res.data.code == 1000) {
          let likeds = res.data.data;
          var purl = "add/content_likes_add";
          var pdata = {
            cl_author: authorid,
            cl_uid: app.globalData.userInfo.id,
            cl_nid: nid
          }
          if (res.data.data) {
            purl = "delete/content_likes_delete";
            pdata = {
              cl_author: authorid,
              cl_nid: nid,
              cl_uid: app.globalData.userInfo.id
            }
          }
          if (res.data.data == orliked) {
            wx.request({
              url: app.globalData.sys_url + purl,
              header: {
                'content-type': 'application/x-www-form-urlencoded',
                'authorization': app.globalData.userInfo.token
              },
              data: pdata,
              method: 'POST',
              success: function(res) {
                that.updateNewsHot(nid, cn_like, likeds ? -2 : 2);
                app.netWorkData.result[inum] = res.data;
                resolve();
              }
            })
          } else {
            app.netWorkData.result[inum] = {
              "code": 1000,
              "msg": "数据请求成功"
            };
            resolve();
          }
        }
      }
    });
  });
  return promise

}


function addFavorite(nid, authorid, orliked, inum, cn_like) { //取消或添加收藏
  let that = this;
  var purl = "add/content_favoires_add";
  var pdata = {
    cf_author: authorid,
    cf_uid: app.globalData.userInfo.id,
    cf_cnid: nid
  }

  if (orliked) {
    purl = "delete/content_favoires_delete";
    pdata = {
      cf_cnid: nid,
      cf_uid: app.globalData.userInfo.id
    }
  }
  let promise = new Promise(function(resolve, reject) {
    wx.request({
      url: app.globalData.sys_url + purl,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'authorization': app.globalData.userInfo.token
      },
      data: pdata,
      method: 'POST',
      success: function(res) {
        app.netWorkData.result[inum] = res.data;
        if (orliked) {
          that.updateNewsHot(nid, cn_like, -1);
        } else {
          that.updateNewsHot(nid, cn_like, 1);
        }
        resolve();
      }
    })
  });
  return promise
}


function addTopicFans(topicid, orliked, inum) { //取消或添加话题关注
  let that = this;
  var purl = "add/content_topic_liked_add";
  var pdata = {
    btl_btid: topicid,
    btl_uid: app.globalData.userInfo.id,
  }

  if (orliked) {
    purl = "delete/content_topic_liked_delete";
  }
  let promise = new Promise(function(resolve, reject) {
    wx.request({
      url: app.globalData.sys_url + purl,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'authorization': app.globalData.userInfo.token
      },
      data: pdata,
      method: 'POST',
      success: function(res) {
        app.netWorkData.result[inum] = res.data;
        resolve();
      }
    })
  });
  return promise
}

function addFans(touid, addinfo, orfans, inum) { //关注或取消粉丝
  let that = this;
  if (touid != app.globalData.userInfo.id) {
    var purl = "add/content_fans_add";
    var pdata = {
      cf_uid: touid,
      cf_fuid: app.globalData.userInfo.id,
      cf_orabout: 0,
      cf_info: addinfo
    }
    if (orfans) {
      purl = "delete/content_fans_delete";
      pdata = {
        cf_uid: touid,
        cf_fuid: app.globalData.userInfo.id
      }
    }
    let promise = new Promise(function(resolve, reject) {
      wx.request({
        url: app.globalData.sys_url + purl,
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          'authorization': app.globalData.userInfo.token
        },
        data: pdata,
        method: 'POST',
        success: function(res) {
          app.netWorkData.result[inum] = res.data;

          resolve();
        }
      })
    });
    return promise
  } else {
    wx.showToast({
      title: '不能关注自己',
      icon: "none",
      duration: 800
    })
  }
}


function addComments(info, cnid, cnuid, cnrecnid, reusernc, inum, cn_like) { //添加评论,内容、文章编号、评论者、回复评论ID，回复者昵称、点赞数
  let that = this;
  let promise = new Promise(function(resolve, reject) {
    var cpassed = 1;

    wx.request({
      url: app.globalData.sys_url + "add/content_news_comments_add",
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'authorization': app.globalData.userInfo.token
      },
      data: {
        cnc_view: cpassed,
        cnc_uid: cnuid,
        cnc_cnid: cnid,
        cnc_info: info,
        cnc_reuid: app.globalData.userInfo.id,
        cnc_reusernc: reusernc,
        cnc_cncid: cnrecnid,
      },
      method: 'POST',
      success: function(res) {
        //that.POST("update/content_news_totals_updatecomments", { id: cnid, cn_comments:"999999"},9);
        app.netWorkData.result[inum] = res.data;
        that.updateNewsHot(cnid, cn_like, 3); //更新热度值
        resolve();
      }
    })
  });
  return promise
}

function addTopic(title, channelid, inum) { //发布话题
  let promise = new Promise(function(resolve, reject) {
    var cpassed = 1;
    wx.request({
      url: app.globalData.sys_url + "add/content_topic_add",
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'authorization': app.globalData.userInfo.token
      },
      data: {
        ct_channelid: channelid,
        ct_title: title,
        ct_passed: cpassed,
        ct_smallinfo: "",
        ct_uid: app.globalData.userInfo.id,
        ct_oranonymous: 0
      },
      method: 'POST',
      success: function(res) {
        app.netWorkData.result[inum] = res.data;
        resolve();
      }
    })
  });
  return promise
}


function getwxphone(e) { //
  var iv = e.detail.iv;
  var encryptedData = e.detail.encryptedData;
  if (iv != undefined && encryptedData != undefined) {
    var iurl = "diy" + app.globalData.projectid + "/getweixinphone";
    var idata = {
      encryptedDataStr: encryptedData,
      key: app.globalData.sessionkey,
      iv: iv,
      openid: app.globalData.openid,
      auid: app.globalData.storeid,
    };

    POST(iurl, idata, 3).then(() => {
      //请求成功的操作
      var user_data = app.netWorkData.result[3];
      if (user_data.code == 1000) { //名片绑定成功
        var vflist = user_data.data;
        app.globalData.phone = vflist;
        app.globalData.gbid = user_data.model;
        wxlogins();
        if (this.userInfoReadyCallback) {
          this.userInfoReadyCallback(e)
        }
      } else {
        wx.showToast({ //添加loading
          title: user_data.msg,
          icon: 'loading',
          duration: 800
        });
      }
    });
  }
}

function getqrcode(bid) { //生成二维码
  var that = this;
  var access_token = app.globalData.wxtoken;
  if (access_token != "" && bid > 0) {
    var iurl = "diy" + app.globalData.projectid + "/weixin_getwxqrcode";
    var idata = {
      "page": 'pages/businesscard/view?id=' + bid,
      "accesstoken": access_token,
      "bid": bid,
      "auid": app.globalData.auid,
    }
    this.POST(iurl, idata, 7).then(() => {
      var user_data = app.netWorkData.result[7];
      if (user_data.code == 1000) {
        app.globalData.qrcodeurl = app.globalData.imgurl + user_data.data;
      }
    })
  }
}

function historyadd(vtype, tbid) { //增加历史数据
  var that = this;
  var fbid = app.globalData.gbid;
  if (fbid > 0 || (fbid == 0 && vtype == '访问')) {
    var iurl = "diy" + app.globalData.projectid + "/history_add";
    var idata = {
      "auid": app.globalData.storeid,
      "h_fbid": fbid,
      "h_tbid": tbid,
      "h_longitude": app.globalData.longitude,
      "h_latitude": app.globalData.latitude,
      "h_type": vtype
    }
    this.POST(iurl, idata, 7).then(() => {})
  }
}

function canclesubscribe(aid) { //取消订阅
  var that = this;
  var iurl = "delete/anchor_subscribe_delete";
  var idata = {
    "s_aid": aid,
    "s_uid": app.globalData.userInfo.id
  }
  this.POST(iurl, idata, 3).then(() => {});
}



function gomycenter(url) { //我的"pages/quanyi/quanyi"
  let that = this;
  that.gootherminprogram(url);
}

function Guserinfo(e, url) {
  var that = this;
  wx.getUserInfo({
    success: function(res) {
      wx.request({
        url: app.globalData.sanfu_url + "wechat/user/wxMiniAppCustomerRegister",
        data: JSON.stringify({
          "encryptedData": res.encryptedData,
          "appid": app.globalData.appid,
          "sessionKey": app.globalData.session_key,
          "iv": res.iv
        }),
        method: 'POST',
        success: e => {
          if (e.data.success) {
            app.globalData.sanfu_sid = e.data.sid;
            app.globalData.unionid = e.data.unionid;
            app.globalData.u_sanfu_sid = e.data.curcusid;
            var that = this;
            //console.log(JSON.parse(e.data.nickName));
            //console.log(e.data.nickName);
            let nname = e.data.nickName;
            if (nname != undefined) {
              if (nname.indexOf("\\u") > -1) {
                //console.log(nname);
                nname = base64.encode(decodeUnicode(nname.replace(/&#/g, '\\u')));
              }
            }
            let channel_list = app.globalData.userInfo.u_channel_list;
            let faceurl = e.data.avatarUrl;
            var idata = {
              "id": app.globalData.userInfo.id,
              "u_namebase64": nname,
              "u_faceurl": faceurl,
              "u_sanfu_sid": e.data.curcusid,
              "u_unionid": e.data.unionid,
              "u_openid": app.globalData.openid,
              "u_gender": res.userInfo.gender,
              "u_channel_list": channel_list
            };

            wx.request({
              url: app.globalData.sys_url + "accesstoken/updateuser", //+ "update/users_update",
              header: {
                'content-type': 'application/x-www-form-urlencoded',
                'authorization': app.globalData.userInfo.token
              },
              data: idata,
              method: 'POST',
              success: (sysres) => {
                if (sysres.data.code == 1000) {
                  var vtime = Date.now();
                  var vkey = md5.hexMD5(app.globalData.appid + vtime + app.globalData.openid + app.globalData.appkey);
                  vkey = vkey.substring(0, 20);
                  var idata = {
                    "unionid": app.globalData.unionid,
                    "openid": app.globalData.openid,
                    "appid": app.globalData.appid,
                    "sanfuid": e.data.curcusid,
                    "time": vtime,
                    "appkey": vkey
                  }




                  wx.request({
                    url: app.globalData.sys_url + "accesstoken/accountnew",
                    header: {
                      'content-type': 'application/x-www-form-urlencoded',
                    },
                    data: idata,
                    method: 'POST',
                    success: (sysres) => {
                      if (sysres.data.code == 1000) {
                        app.globalData.userInfo = sysres.data.token;
                        if (app.globalData.userInfo.u_namebase64 != null) {
                          var dcode = base64.decode(app.globalData.userInfo.u_namebase64);
                          if (dcode != "") {
                            app.globalData.userInfo.u_namebase64 = dcode;
                            app.globalData.userInfo.adddate = that.getTimestamp();
                          }
                        }
                        if (channel_list != "" && channel_list != undefined) {
                          app.globalData.userInfo.u_channel_list = channel_list;
                        }
                        // wx.request({
                        //   url: app.globalData.sys_url + "update/users_total_update",  //+ "update/users_update",
                        //   header: {
                        //     'content-type': 'application/x-www-form-urlencoded',
                        //     'authorization': app.globalData.userInfo.token
                        //   },
                        //   data: { id: app.globalData.userInfo.id, u_channel_list: channel_list },
                        //   method: 'POST',
                        //   success: (sysres) => {

                        //   }
                        // });
                        wx.switchTab({
                          url: '/pages/find/find',
                        })

                        // wx.request({
                        //   url: app.globalData.sys_url + "update/users_total_updatenc",  //+ "update/users_update",
                        //   header: {
                        //     'content-type': 'application/x-www-form-urlencoded',
                        //     'authorization': app.globalData.userInfo.token
                        //   },
                        //   data: { id: app.globalData.userInfo.id, ut_namebase64: base64.encode(nname), ut_faceurl: faceurl },
                        //   method: 'POST',
                        //   success: (sysres) => {

                        //   }
                        // });
                      } else {
                        wx.showModal({
                          title: '提示',
                          content: sysres.data.msg,
                          showCancel: false
                        })
                      }
                    }
                  })


                } else {
                  wx.showModal({
                    title: '提示',
                    content: sysres.data.msg,
                    showCancel: false
                  })
                }
              }
            })
          } else {
            wx.showModal({
              title: '提示',
              content: e.data.msg,
              showCancel: false
            })
          }
        }
      });
    },
    fail: function(e) {
      wx.navigateBack({})
    }
  })
}




/**
 * 解析段落的unicode字符，聊天记录的内容中有很多是编码过的
 */
function decodeUnicode(str) {
  var ret = '';
  var splits = str.split('\\');
  for (let i = 0; i < splits.length; i++) {
    if (splits[i] != "") {
      ret += unescape("%" + splits[i]);
    }
  }
  return ret;
}


/**
 * 解析单个unidecode字符
 */
function spliteDecode(value) {
  var target = value.match(/\\u\d+/g);
  if (target && target.length > 0) {
    target = target[0];
    var temp = value.replace(target, '{{@}}');
    target = target.replace('\\u', '');
    target = String.fromCharCode(parseInt(target));
    return temp.replace("{{@}}", target);
  } else {
    // value = value.replace( '\\u', '' );
    // return String.fromCharCode( parseInt( value, '10' ) )
    return value;
  }
}

function addFubi(vtype) { //添加福币,0表示评论，1表示收藏，2表示点赞，3表示转发
  let that = this;
  //console.log(app.globalData.sanfu_fubi);
  if (app.globalData.sanfu_fubi != null) {
    let num = 0;
    if (vtype == 0) {
      num = app.globalData.sanfu_fubi.ul_sendfb_comments
    } else if (vtype == 1) {
      num = app.globalData.sanfu_fubi.ul_sendfb_favorite
    } else if (vtype == 2) {
      num = app.globalData.sanfu_fubi.ul_sendfb_zan
    } else if (vtype == 3) {
      num = app.globalData.sanfu_fubi.ul_sendfb_recommentd
    }
    if (num > 0) {
      let sanfuprodurl = "api/sale/saveGiveGift";
      let sanfudata = {
        sid: app.globalData.sanfu_sid,
        giftNum: num
      };
      that.PostSanfu(sanfuprodurl, sanfudata, 7).then(() => {});
    }
  }
}

//调用
//decodeUnicode(valueFiled.replace(/&#/g, '\\u'));

function uploadImage(sizeType, counts) {
  let that = this;
  wx.chooseImage({
    count: counts, //默认9
    sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
    sourceType: ['' + sizeType + ''], //从相册选择
    success: (res) => {
      var len = 0; //that.data.imgList.length;
      var tempFilePaths = res.tempFilePaths
      var len1 = tempFilePaths.length
      var count = parseInt(len) + parseInt(len1);
      if (count > counts) {
        wx.showToast({
          title: "最多上传" + counts + "张",
          icon: '',
          duration: 800
        });
        return false;
      }
      wx.showLoading();
      app.globalData.goodspic = [];
      let len2 = 0;
      let dlist = [];
      let times = new Date();
      for (var i = 0; i < tempFilePaths.length; i++) {
        var filePath = tempFilePaths[i];
        //console.log(filePath);
        var filename = app.globalData.userInfo.id + "_" + i + md5.hexMD5(app.globalData.userInfo.id + times) + filePath.substr(filePath.lastIndexOf('.'));
        dlist.push({
          "pic": "https://snsimg.sanfu.com/" + filename,
          "tags": [],
          "text": ""
        });
      }
      for (var i = 0; i < tempFilePaths.length; i++) {
        var filePath = tempFilePaths[i];
        var filename = app.globalData.userInfo.id + "_" + i + md5.hexMD5(app.globalData.userInfo.id + times) + filePath.substr(filePath.lastIndexOf('.'));
        cos.postObject({
          Bucket: config.Bucket,
          Region: config.Region,
          Key: filename,
          FilePath: filePath,
          onProgress: function(info) {
            //console.log(JSON.stringify(info));
          }
        }, function(err, data) {

          // console.log(data);
          if (data && data.Location) {
            let imgurl = 'https://snsimg.sanfu.com/' + data.Location.substr(data.Location.lastIndexOf("/") + 1);



            let checkurl = "https://m.sanfu.com/ms-sanfu-wechat-common/common/getImgSecCheck?appId=" + app.globalData.appid + "&sid=" + app.globalData.sanfu_sid + "&imgUrl=" + imgurl + "/img750";
            that.GetSanfuAll(checkurl, {}, 5).then(() => {
              var user_data = app.netWorkData.result[5];
              wx.hideLoading();
              if (user_data.success) {
                if (user_data.data.errcode != "87014") {
                  if (user_data.data.errcode != "0") {
                    app.globalData.cpassed = 0;
                  }
                  // var tplist = app.globalData.goodspic;
                  // tplist.push({ "pic": imgurl, "tags": [],"text":"" });
                  // app.globalData.goodspic=tplist;
                  len2 += 1;
                } else {
                  wx.showModal({
                    title: '提示',
                    content: '图片包含敏感信息，请重新上传图片！',
                    showCancel: false
                  })
                  return;
                }
              }
            });
          } else {
            wx.showToast({
              title: '上传失败',
              icon: 'error',
              duration: 2000
            });
          }
        });



        // wx.getFileSystemManager().readFile({
        //   filePath: file, //选择图片返回的相对路径
        //   encoding: 'base64', //编码格式
        //   success: res => { //成功的回调
        //     var iurl = "cos/upfile";
        //     var idata = {
        //       imgData: res.data
        //     }
        //     this.POST(iurl, idata, i).then(() => {
        //       var redata = app.netWorkData.result[i];
        //       if (redata.status == "100") {
        //         var tplist = app.globalData.goodspic;
        //         tplist.push({ "pic": redata.imageUrl, "tags": [],"text":"" });
        //         app.globalData.goodspic=tplist;
        //       }
        //     })
        //   }
        // })
      }

      let upcheck = setInterval(function() {
        //console.log(app.globalData.goodspic.length+"-"+len1)
        if (len2 == len1) {
          app.globalData.goodspic = dlist;
          clearInterval(upcheck);
          upcheck = null;
          wx.navigateTo({
            url: '/sfsns/pages/publish/pulish/pulish?num=0&type=home'
          })
        }
      }, 500);
    }
  });
}

function uploadVideo() {
  var that = this;
  wx.chooseVideo({
    sourceType: ['camera', 'album'],
    maxDuration: 60,
    compressed: false,
    camera: ['front', 'back'],
    success: function(res) {
      if (res.duration > 60) {
        wx.showModal({
          title: "提示",
          content: "视频太长，时间不能超过60秒。",
          showCancel: false,

        })
      } else {
        wx.showToast({
          title: '正在上传视频',
          icon: 'loading',
          duration: 50000
        });
        let vsize = (res.width / res.height).toFixed(1);
        var filePath = res.tempFilePath;
        var filename = app.globalData.userInfo.id + "_" + md5.hexMD5(app.globalData.userInfo.id + new Date()) + filePath.substr(filePath.lastIndexOf('.'));
        cos.postObject({
          Bucket: config.Bucket,
          Region: config.Region,
          Key: filename,
          FilePath: filePath,
          onProgress: function(info) {
            let percent = info.percent;
            if (percent != "") {
              wx.hideToast({
                success: (res) => {},
              })
              wx.showToast({
                title: '上传进度:' + (parseFloat(percent) * 100).toFixed(0) + '%',
                icon: 'loading',
                duration: 50000
              });
            } else {
              wx.hideToast({
                success: (res) => {},
              })
            }
          }
        }, function(err, data) {
          //  console.log(data);
          if (data && data.Location) {
            setTimeout(function() {
              wx.hideToast({
                success: (res) => {},
              })
              wx.hideLoading();
              wx.navigateTo({
                url: '/sfsns/pages/sppublish/index?vsize=' + vsize + '&video=' + 'https://snsimg.sanfu.com/' + data.Location.substr(data.Location.lastIndexOf("/") + 1)
              })

            }, 2000);

          } else {
            wx.hideToast({
              success: (res) => {},
            })
            wx.hideLoading();
            wx.showToast({
              title: '上传失败',
              icon: 'error',
              duration: 2000
            });
          }
        });
      }
    }
  })
}

//更新权重,0浏览，1收藏，2点赞，3评论，4转发
function updateNewsHot(id, cn_like, vtype) {
  var that = this;
  let newhot = 0;
 
  if (app.globalData.newsPower != null) {
    if (vtype == 0) { //浏览
      newhot = parseFloat(app.globalData.newsPower.cp_hits);
      // 文章跳转计数
    } else if (vtype == 1) { //收藏
      newhot = parseFloat(app.globalData.newsPower.cp_favorites);
    } else if (vtype == -1) { //取消收藏
      newhot = -parseFloat(app.globalData.newsPower.cp_favorites);
    } else if (vtype == 2) { //点赞
      newhot = parseFloat(app.globalData.newsPower.cp_likes);
      // 文章点赞次数
    } else if (vtype == -2) { //取消点赞
      newhot = -parseFloat(app.globalData.newsPower.cp_likes);
    } else if (vtype == 3) { //评论
      newhot = parseFloat(app.globalData.newsPower.cp_comments);
    } else if (vtype == 4) { //转发
      newhot = parseFloat(app.globalData.newsPower.cp_forward);
    }
    var iurl = "update/content_news_totals_hot";
    var idata = {
      "cn_hotsvalue": "000000000" + newhot,
      "id": id
    }
    this.POST(iurl, idata, 5).then(() => {})
  }
}

//更新转发数量
function updateNewsForward(nid, authorid) {
  var purl = "update/content_news_totals_updateforward";
  var pdata = {
    id: nid,
    cn_forward: "999999"
  }
  this.POST(purl, pdata, 6).then(() => {})
  var addurl = "add/content_forward_add";
  var adddata = {
    "cf_author": authorid,
    "cf_cnid": nid,
    "cf_uid": app.globalData.userInfo.id
  }
  this.POST(addurl, adddata, 11).then(() => {})
}

//更新文章中商品点击数
function updateNewsGoodshits(nid) {
  var purl = "update/content_news_totals_updategoodshits";
  var pdata = {
    id: nid,
    cn_goods_hits: "999999"
  }
  this.POST(purl, pdata, 6).then(() => {})
}

//更新用户标签
function updateUserTags(cn_tags, nid) {
  let that = this;
  if (cn_tags != "" && cn_tags != "[]" && cn_tags != null && cn_tags != undefined) {
    cn_tags += ",";
    var t = cn_tags.split(",");
    for (let i = 0; i < t.length - 1; i++) {
      let tags = t[i];
      if (tags != "" && tags != null && tags != undefined) {
        let curl = "model/content_usertags_check";
        let cdata = {
          cu_uid: app.globalData.userInfo.id,
          cu_tag: tags
        }
        this.POST(curl, cdata, 6).then(() => {
          var user_data = app.netWorkData.result[6];
          if (user_data.code == 1000) {
            let aurl = "update/content_usertags_update";
            let adata = {
              id: user_data.data.id,
              cu_counts: "999999"
            }
            this.POST(aurl, adata, 9).then(() => {});
          } else if (user_data.code = 1013) { //不存在
            let aurl = "add/content_usertags_add";
            let adata = {
              cu_uid: app.globalData.userInfo.id,
              cu_counts: 1,
              cu_tag: tags
            }
            this.POST(aurl, adata, 9).then(() => {});
          }
        })
      }
    }


  }
}

function fnupdatenewstags(tid) { //更新文章标签数
  if (tid != "") {
    let aurl = "update/content_tags_updatenews";
    let adata = {
      id: tid,
      ct_articlecounts: "999999"
    }
    this.POST(aurl, adata, 3).then(() => {});
  }
}



function goNewsView(e) { //跳转帖子详情
  let that = this;
  let vid = e.currentTarget.dataset.id;
  if (vid != undefined) {
    app.globalData.newsID = vid;
    //app.globalData.channID = e.currentTarget.dataset.channID;
    wx.switchTab({
      url: '/pages/find/find?id=' + vid,
    })
  }
}

function getTimestamp() {
  var timestamp = Date.parse(new Date());
  timestamp = timestamp / 1000;
  //console.log("当前时间戳为：" + timestamp);  
  return timestamp;
}


module.exports = {
  POST: POST,
  PostSanfu: PostSanfu,
  PostSanfuALL: PostSanfuALL,
  GetSanfu: GetSanfu,
  GetSanfuAll: GetSanfuAll,
  POST1: POST1,
  getwxphone: getwxphone,
  getqrcode: getqrcode,
  historyadd: historyadd,
  gomycenter: gomycenter,
  Guserinfo: Guserinfo,
  canclesubscribe: canclesubscribe,
  decodeUnicode: decodeUnicode,
  addTopic: addTopic, //添加主题
  addFans: addFans, //关注或取消关注
  uploadImage: uploadImage,
  uploadVideo: uploadVideo,
  addComments: addComments,
  addLiked: addLiked,
  addFavorite: addFavorite,
  addTopicFans: addTopicFans,
  addFubi: addFubi,
  updateNewsHot: updateNewsHot,
  updateNewsForward: updateNewsForward,
  updateNewsGoodshits: updateNewsGoodshits,
  updateUserTags: updateUserTags,
  fnupdatenewstags: fnupdatenewstags,
  goNewsView: goNewsView,
  getTimestamp: getTimestamp
}
