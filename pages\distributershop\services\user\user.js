// pages/distributershop/services/user/user.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cusid: '',
    user: {}, //用户信息
    consume: [{
      isOnline: 1
    }, {
      isOnline: 0
    }], //用户消费
    behaviorIndex: 0,
    behaviorList: [{
      data: [],
      has_more: 0,
      page: 1,
      show: false,
    }, {
      data: [],
      has_more: 0,
      show: false,
      page: 1
    }],
    loadingText: ['点击加载更多', '正在加载...', '没有更多了~']
  },
  onLoad: function(options) {
    // wx.showModal({
    //   content:wx.getStorageSync('qysid') +'      '
    // }) 
    if (options.cusid) {
      this.data.cusid = options.cusid
    } else {
      wx.showModal({
        content: '接收参数有误，请重试',
        showCancel: false,
        success: res => {
          wx.navigateBack({
            delta: 1
          })
        }
      })
      return
    }

  },
  onShow: function() {},
  onReady() {
    this.getUser()
    // this.getConsume()
    this.getBehavior()
  },
  onReachBottom: function() {
    this.getBehavior()
  },
  getUser: function() {
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/customer/get', {
      sid: wx.getStorageSync('sid'),
      curCusId: this.data.cusid,
      qysid: wx.getStorageSync('qysid')
    }, res => {
      if (res.success) {
        this.setData({
          user: res.data
        })
      } else {
        app.util.reqFail(res)
      }
    })
  },
  getConsume: function() {
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/customer/consume', {
      sid: wx.getStorageSync('sid'),
      curCusId: this.data.cusid,
      qysid: wx.getStorageSync('qysid')
    }, res => {
      if (res.success) {
        for (let i in res.data) {
          if (res.data[i].isOnline) {
            this.data.consume[0] = res.data[i]
          } else {
            this.data.consume[1] = res.data[i]
          }
        }
        this.setData({
          consume: this.data.consume
        })
      } else {
        app.util.reqFail(res)
      }
    })
  },
  toInfo: function() {
    wx.navigateTo({
      url: '../userInfo/userInfo?cusid=' + this.data.cusid
    })
  },
  setStar: function(e) {
    wx.showLoading()
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/customer/star', {
      curCusId: this.data.user.curCusId,
      sid: wx.getStorageSync('sid'),
      starType: this.data.user.isStar ? 0 : 1 //星标状态[0-无星标/1-有星标]
    }, res => {
      wx.hideLoading()
      if (res.success) {
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        })
        this.setData({
          ['user.isStar']: this.data.user.isStar ? 0 : 1
        })
        let pages = getCurrentPages()
        if (pages.length > 2) {
          let lastpage = pages[pages.length - 2]
          console.log(lastpage)
          lastpage.data.showRefresh = true
        }
      } else {
        app.util.reqFail(res)
      }
    })
  },

  changeBehave: function(e) {
    wx.pageScrollTo({
      selector: '#top',
      duration: 200
    })
    let i = e.currentTarget.dataset.i
    this.setData({
      behaviorIndex: i
    })
    if (this.data.behaviorList[this.data.behaviorIndex].data.length == 0)
      this.getBehavior()
  },
  changeShow: function(e) {
    this.setData({
      [`behaviorList[${this.data.behaviorIndex}].show`]: this.data.behaviorList[this.data.behaviorIndex].show ? false : true
    })
  },
  getBehavior: function() {
    let url, index = this.data.behaviorIndex
    if (index == 0) {
      url = 'ms-sanfu-wap-customer-distribution/assistant/customer/browseInfo'
    } else {
      url = 'ms-sanfu-wap-customer-distribution/assistant/customer/tradeInfo'
    }
    if (this.data.behaviorList[index].has_more != 0) return
    this.setData({
      [`behaviorList[${index}].has_more`]: 1
    })
    app.reqGet(url, {
      sid: wx.getStorageSync('sid'),
      qysid: wx.getStorageSync('qysid'),
      curCusId: this.data.cusid,
      page: this.data.behaviorList[index].page,
      pageSize: 12
    }, res => {
      if (res.success) {
        if (res.data.length < 12) this.data.behaviorList[index].has_more = 2
        else {
          this.data.behaviorList[index].has_more = 0
          this.data.behaviorList[index].page++
        }
        // 处理未分类、格式不一致数据
        for (let i in res.data) {
          let list = res.data[i]
          list.data = []
          for (let j in list) {
            if (typeof list[j] == 'object' && j != 'data') {
              let data = {}
              if (list[j].length > 0) {
                data.key = j
                data.data = list[j]
                list.data.push(data)
              }
              delete list[j]
            }
            if (j == 'isVisit' && list[j])
              list.data.push({
                key: 'visit'
              })
          }
        }

        this.data.behaviorList[index].data = [...this.data.behaviorList[index].data, ...res.data]
        console.log(this.data.behaviorList[index].data)
        this.setData({
          [`behaviorList[${index}]`]: this.data.behaviorList[index]
        })
      } else {
        this.data.behaviorList[index].has_more = res.msg
        this.setData({
          [`behaviorList[${index}]`]: this.data.behaviorList[index]
        })
        app.util.reqFail(res)
      }
    })
  },
  toGoods: function() {
    wx.navigateTo({
      url: '/pages/distributershop/services/recGoods/recGoods?cusid=' + this.data.cusid
    })
  },
  toActive: function() {
    wx.navigateTo({
      url: '/pages/distributershop/services/recActive/recActive?cusid=' + this.data.cusid
    })
  },
  /* 点击放大主图 */
  openImg: function(e) {
    let i = e.currentTarget.dataset.i
    let item = e.currentTarget.dataset.item
    let url = item[i].goodsImgUrl
    let urls = []
    for (let j in item) {
      urls.push(item[j].goodsImgUrl)
    }
    wx.previewImage({
      current: url, // 当前显示图片的http链接
      urls: urls // 需要预览的图片http链接列表
    })
  },
  openGoods(e) {
    wx.vibrateShort()
    let id = e.currentTarget.dataset.id
    app.toH5(`goods/goodsDisplay?goods_sn=${id}`);
  },
  showtip: function(e) {
    //查看提示信息
    let i = e.currentTarget.dataset.i
    let msg
    if (i == 1)
      msg = '线上只展示最近90天数据，线下展示最近30天数据'
    else if (i == 2)
      msg = '只展示最近90天行为数据'
    else if (i == 3)
      msg = '只展示最近30天交易数据'
    if (msg) {
      wx.showModal({
        title: '提示',
        content: msg,
        showCancel: false
      })
    }
  },
  async toSale() {
    if (!await this.checkQy()) return
    app.toH5('newmgr/crm/#/kefu/cardsearch?hide=1&cardid1=' + this.data.cusid)
  },
  checkQy(type) {
    return new Promise(resolve => {
      let sys = wx.getSystemInfoSync()
      if (sys.environment == 'wxwork') {
        let caniuse = wx.qy.canIUse('getEnterpriseUserInfo')
        if (this.data.qyLogin) resolve(true)
        else {
          wx.qy.login({
            success: async res2 => {
              await app.waitSid()
              if (res2.code) {
                app.reqPost('ms-sanfu-wechat-customer/customer/wxWork/wxMiniAppLogin', {
                  sid: wx.getStorageSync('sid'),
                  appid: app.globalData2.appid,
                  code: res2.code
                }, res => {
                  if (res.success) {
                    // this.data.userId = res.userId
                    resolve(true)
                  } else {
                    resolve(false)
                    app.util.reqFail(res)
                  }
                })
              } else {
                resolve(false)
                wx.showModal({
                  content: '登录失败' + JSON.stringify(res),
                  showCancel: false
                })
              }
            },
            fail: res => {
              resolve(false)
              wx.showModal({
                content: '登录失败' + JSON.stringify(res),
                showCancel: false
              })
            }
          })
        }

      } else {
        if (!type)
          wx.showModal({
            content: '请在企业微信内打开',
            showCancel: false
          })
        resolve(false)
      }
    })

  },
  async toChat() {
    if (!await this.checkQy()) return
    const id = this.data.user.externalUserId
    wx.showActionSheet({
      itemList: ['查看个人信息', '创建会话', ],
      success: res => {
        if (res.tapIndex == 0)
          this.openUserProfile(id)
        else if (res.tapIndex == 1)
          this.openEnterpriseChat(id)

      },
    })

  },
  openUserProfile(id) {
    //打开个人信息页
    wx.qy.openUserProfile({
      type: 2, //1表示该userid是企业成员，2表示该userid是外部联系人
      userid: id, //可以是企业成员，也可以是外部联系人
      success: function(res) {
        // 回调
      }
    });
  },
  openEnterpriseChat(id) {
    //创建会话
    wx.qy.openEnterpriseChat({
      // 注意：userIds和externalUserIds至少选填一个，且userIds+externalUserIds总数不能超过2000，如果externalUserIds有微信联系人，则总数不能超过40人。
      userIds: '', //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
      externalUserIds: id, // 参与会话的外部联系人列表，格式为userId1;userId2;…，用分号隔开。
      groupName: '', // 必填，会话名称。单聊时该参数传入空字符串""即可。
      success: function(res) {
        // 回调
      },
      fail: function(res) {
        // 失败处理
      }
    });
  },


})
