const app = getApp()
import IntersectionObserver from '../../../utils/intersection-observer.js';
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
    virtualHost: true,
    addGlobalClass: false,
  },

  properties: {
    start: {
      type: Boolean,
      value: false,

    },
    top: {
      type: Number,
      value: 0
    },
    // 非动态顶部
    dafaultNav: {
      type: Boolean,
      value: false
    },
    // 自定义配置项
    options: {
      type: null,
      value: {}
    },
    styles: {
      type: String,
      value: ''
    },
    // 多元素检测
    observeAll: {
      type: Boolean,
      value: false
    }
  },
  data: {},
  ready() {
    if (this.data.start && !this.data.observeAll) {
      this.startObserver()
    }
    this.ready = true
  },
  detached() {
    this.ob && this.ob.disconnect && this.ob.disconnect()
  },
  watch: {
    'start': function(a) {
      if (a && this.ready) {
        // this.ob && this.ob.reconnect && this.ob.reconnect()
      } else {
        this.ob && this.ob.disconnect && this.ob.disconnect()
      }
    }
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {
      if (this.data.start) {
        this.ob && this.ob.reconnect && this.ob.reconnect()
        if (!this.data.observeAll) this.startObserver()
      }
    },
    hide: function() {
      this.ob && this.ob.disconnect && this.ob.disconnect()
    },
    resize: function() {},
  },
  methods: {
    startObserver(opt = {}, restart) {
      // console.log('startObserverstartObserver', opt);
      if (this.started) {
        if (restart) {
          this.ob && this.ob.reconnect && this.ob.reconnect()
        }
        return
      }
      this.started = true
      let top = 0
      let currentPage
      if (__wxConfig && __wxConfig.page) {
        for (const i in __wxConfig.page) {
          currentPage = __wxConfig.page[i]
        }
      }
      if (!this.data.dafaultNav && currentPage && currentPage.window && currentPage.window.navigationStyle == "custom") {
        let navbarInfo = wx.getStorageSync('navbarInfo') || {}
        top = navbarInfo.navBarHeight || 0 + navbarInfo.statusBarHeight || 0
      }
      //曝光处理
      // observeAll: true,
      // context: this,
      // delay: 30,
      // threshold: 0.000000000000000000000000001,
      // initialRatio: 0,
      this.ob = new IntersectionObserver({
        selector: `.observer`,
        observeAll: false,
        context: this,
        delay: 300,
        // threshold: 0.000000000000000000000000001,
        initialRatio: 0,
        threshold: 0.95, // 阈值
        // initialRatio: 0.95, // 相交比例
        viewport: {
          bottom: 0,
          top: (this.data.top > 0 ? -1 * app.util.rpx2px(this.data.top) : 0) + -1 * top
        },
        ...this.data.options,
        ...opt,
        onNoV: () => {
          this.triggerEvent('nov');
        },
        onEach: e => {
          this.triggerEvent('each', e.dataset);
          return e.dataset
        },
        onFinal: args => {
          if (!args) return
          this.triggerEvent('observer', args);
        },
      })
      // this.ob && this.ob.connect && this.ob.connect()
      setTimeout(() => {
        this.ob && this.ob.reconnect && this.ob.reconnect()
      }, 500)
    }
  }
});
