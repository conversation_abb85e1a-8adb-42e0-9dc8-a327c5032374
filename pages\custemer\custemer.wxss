/**index.wxss**/
@import "/font/icon_f3.wxss";
@import "/font/icon_f7.wxss";

page {
  background: #f5f5f5;
  padding-bottom: 40rpx;
}


.top-bg {
  background: linear-gradient(139deg, #FAEEF0 0%, #EFE9EE 24%, #E3EEFF 100%);
}


.topinfo-wrap {
  width: 100%;
  padding: 0 0 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.topinfo-wrap .head-img {
  width: 116rpx;
  height: 116rpx;
  border: 2rpx solid #FFFFFF;
  border-radius: 50%;
  overflow: hidden;
  margin-left: 30rpx;
}


.info-box {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding-right: 24rpx;
  padding-left: 20rpx;
  line-height: 1;
}

.info-box>.nickname {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.topinfo-wrap .btn-sign {
  padding: 20rpx;
  margin-right: 10rpx;
}

.topinfo-wrap .btn-sign>text {
  background: linear-gradient(270deg, #E60012 0%, #FF7956 100%);
  color: #fff;
  border-radius: 28rpx;
  font-size: 24rpx;
  padding: 16rpx 0;
  line-height: 1;
  min-width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 3px 3px 3px #e6001225;
}


.coupons-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx 30rpx;
}

.coupons-wrap>view {
  flex: 1;
  align-self: stretch;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  font-size: 24rpx;
  color: #333;
  line-height: 1;
}


.coupons-wrap>view>.value {
  font-weight: 700;
  font-size: 36rpx;
  margin-bottom: 16rpx;
  /* text-decoration: underline; */
}

.common-title {
  width: 100%;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.common-title>.title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: left;
  color: #333;
  height: 32rpx;
  line-height: 32rpx;
}

.common-title>.more {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  align-items: center;
}

.moreTitle {
  display: inline-block;
  height: 28rpx;
  line-height: 28rpx;
}

.layout-func-row {
  width: 100%;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 4rpx;
}

.layout-func-row>.layout-func-row-item {
  min-height: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  position: relative;
}

.layout-func-row-item .tip {
  font-size: 20rpx;
  position: absolute;
  top: -8rpx;
  right: -12rpx;
  width: 30rpx;
  height: 30rpx;
  background: #e93c4a;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.layout-func-row>.cell-4 {
  width: 25%;
}

.layout-func-row>.cell-5 {
  width: 20%;
}

.layout-func-row>.cell-flex {
  flex: 1;
}

.func-part {
  width: 690rpx;
  margin: 0 auto;
  padding: 32rpx 0 0;
  margin-top: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 15rpx rgba(233, 233, 233, 0.50);
}

.card {
  width: 690rpx;
  height: 90rpx;
  margin: 0 auto;
  padding: 0 14rpx 0 30rpx;
  background-position: top;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #f9f9f9;
  font-size: 28rpx;
  border-radius: 12rpx 12rpx 0 0;
}

.card .card-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.card .card-name-wrap {
  display: flex;
  align-items: center;
}

.card .card-name {
  font-size: 32rpx;
  font-weight: 700;
  height: 36rpx;
  line-height: 36rpx;
}

.card .cardid {
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #fff;
  opacity: 0.75;
  font-weight: 400;
  height: 24rpx;
  line-height: 24rpx;
  display: inline-block;
}

.card .card-right {
  display: flex;
  flex-direction: column;
  color: #fff;
  padding-right: 16rpx;
}

.card .cardtime {
  text-shadow: none;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.70);
  height: 20rpx;
  line-height: 20rpx;
  margin-top: 8rpx;
}

.card .discounts {
  font-size: 26rpx;
  line-height: 1;
  color: #f3f3f3;
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gradeDescription {
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  padding: 14rpx 0 46rpx 0;
}

.gradeDescription>view {
  border: 1rpx solid hsla(0, 0%, 70%, 0.8);
  color: hsla(0, 0%, 70%, 0.8);
  font-size: 24rpx;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  text-align: center;
}

.vip-interest-box {
  background-color: #fff;
}

.common-title>.title {
  background: linear-gradient(180deg, #ffffff00, #fff);
  z-index: 3;
}

.common-title {
  padding: 0 24rpx;
  padding-bottom: 36rpx;
}

.layout-func-row-item {
  margin-bottom: 32rpx;
}

.item-func-title {
  font-size: 24rpx;
  text-align: center;
  margin-top: 12rpx;
  color: #303030;
  height: 24rpx;
  line-height: 24rpx;
}


.mall-icon {
  height: 56rpx;
  width: 56rpx;
  border-radius: 50%;
  font-size: 50rpx;
  line-height: 56rpx;
  position: relative;
  text-align: center;
  color: #353535;
  font-size: 56rpx;
}


.serv-icon {
  font-size: 56rpx;
  position: relative;
  color: #494949;
  padding-top: 6rpx;
}

.serv-icon::after {
  content: ' ';
  width: 80rpx;
  height: 80rpx;
  /* background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0) 70%); */
  position: absolute;
  left: 0;
  top: 0;
  z-index: 5;
}



.order-btn-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 4rpx 0 0;
  padding-left: 24rpx;
  margin-bottom: 36rpx;
  width: 100%;
}

.order-btn-box>view {
  padding: 16rpx 36rpx;
  background: #f4f4f5;
  border-radius: 60rpx;
  font-size: 24rpx;
  color: #333;
  margin-right: 48rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.order-btn-box>view>text {
  height: 24rpx;
  line-height: 24rpx;
  display: inline-block;
}

.order-btn-box>view>view {
  font-size: 20rpx;
  position: absolute;
  top: -6rpx;
  right: -10rpx;
  line-height: 30rpx;
  width: 30rpx;
  height: 30rpx;
  background: #e93c4a;
  color: #fff;
  border-radius: 50%;
  text-align: center;
}

.order-btn-box>.active {
  background: linear-gradient(to right, #FF7956, #E60012);
  color: white;
  border: none;
}

/* 我的任务 */
.taskLine {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.taskTitle {
  font-size: 28rpx;
}

.taskTip {
  font-size: 22rpx;
  color: #b2b2b2;
}

.taskBtn {
  height: 60rpx;
  width: 120rpx;
  border-radius: 30rpx;
  color: #fd5f8a;
  border: 2rpx solid #fd5f8a;
  font-size: 24rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
}

.iconBorder {
  width: 60rpx;
  height: 60rpx;
  background-size: 100% !important;
  display: inline-block;
  margin-right: 25rpx;
}

.inline-block {
  display: inline-block;
}

.ad {
  padding: 0;
  display: flex;
  align-items: center;
  overflow: hidden;
  max-height: 330rpx;
  flex-direction: row;
}

.ad image {
  /* border-radius: 16rpx; */
  position: relative;
  overflow: visible;
  height: 0;
}

/* .ad image:nth-of-type(2)::before{
  position: absolute;
  content:  ' ';
  left: -2rpx;
  top: 20%;
  width: 2rpx;
  height: 60%;
  background: #e6e6e6;
  z-index: 666;
} */

.order-info {
  height: 110rpx;
  margin: 0 20rpx;
  border-radius: 12rpx;
  background: #f3f3f3;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.order-info .order-item {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 10rpx 16rpx;
  font-size: 24rpx;
  line-height: 1.5;
  color: #333333;
}

.take-code {
  margin-left: auto;
  padding: 10rpx 20rpx;
  background: #e60012;
  color: #fff;
  font-size: 26rpx;
  border-radius: 32rpx;
  line-height: 1;
}
