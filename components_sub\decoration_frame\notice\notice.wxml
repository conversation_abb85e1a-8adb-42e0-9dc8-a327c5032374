<view class="notice" wx:if="{{unitData.text}}" style="z-index:{{index+1}};margin: {{config.mTB}}rpx {{config.mLR}}rpx;background: {{config.bg}};border-radius: {{config.radius}}rpx;color: {{config.color}};" bindtap="open">
  <text class="notice-title" style="border-color: {{config.color}};">通知</text>
  <img wx:if="{{uniData.imgUrl}}" src="{{uniData.imgUrl}}" style="width: 18rpx;height: 18rpx;"></img>
  <text wx:else class="sficon sf-volume-down-line noticeIcon"></text>
  <view class="notice-text " bindtap="nav">
    <text class="{{config.scrollDir?'notice-text-info moveX':''}}">{{unitData.text}}</text>
  </view>
</view>
