const app = getApp()
Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    type: {
      type: String,
      value: 0
    },
    customerAddressList: {
      type: Array,
      value: []
    },
    seqNo:{
      type: Number,
      value: -1
    }
  },
  data: {
    addressList: [],
    showLoading: false
  },
  attached() {
    if (this.data.type) {
      this.getAddressList()
    }
  },
  detached() {},
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {
      if (this.data.type) {
        this.getAddressList()
      }
    },
    hide: function() {},
    resize: function() {},
  },
  methods: {
    /* 显示收货地址的方法 */
    async getAddressList() {
      this.setLoading(true)
      const res = await app.util.getUserAddress()
      this.setLoading(false)
      this.setData({
        addressList: res
      })
    },
    close() {
      this.triggerEvent('close')
    },
    selAdd(e) {
      let seqNo = e.currentTarget.dataset.no
      this.triggerEvent('callback', seqNo)
      app.$bus.emit('countSelAdd', 0)
    },
    selAdd2(e){
      let i = e.currentTarget.dataset.i
      this.triggerEvent('callback', this.data.addressList[i])
    },
    goAdd() {
      app.toH5(`user/customer/receiveAddress?ty=1`)
    },
    goAddAddress() {
      app.toH5(`user/customer/addAddress?seqNo=0&ty=1`)
    },
    toH5(e) {
      let url = e.currentTarget.dataset.url
      app.toH5(url)
    },
  }
});
