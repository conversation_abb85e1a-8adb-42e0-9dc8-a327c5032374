<!--pages/distributershop/active/active.wxml-->
<sf-loading wx:if="{{has_more==4||showLoading}}" />
<block wx:for="{{activitys}}" wx:key="index">
  <template data="{{item}}" id="{{index}}" is="group-goods" />
</block>
<view wx:if="{{has_more != 4}}" class="show_end" bindtap="onReachBottom">
  <view wx:if="{{has_more == 1}}" class="loadmore-icon"></view>
  <text>{{ loadingText[has_more] }}</text>
</view>
<!-- 分享朋友圈二维码 -->
<canvas type="2d" id="shareCanvas" class="shareCanvas" style="width:{{canvasWidth}}px;height:{{canvasHeight}}px;position:fixed;top:-{{canvasHeight||9999}}px;right:-{{canvasHeight}}rpx;margin-top:-1000px;margin-right:-1000px;"></canvas>
<view hidden="{{showqrcode}}" class="qrcode">
  <image lazy-load mode="aspectFit" src="{{tmppath}}" style="height:{{canvasHeight}}px;width:630rpx;max-height:85%;border-radius: 20rpx;"></image>
  <view class="dis-top">
    <view style="background:white;width:40%;flex:none;" bindtap="closeQR">关闭</view>
    <view style="background:white;width:40%;flex:none;" bindtap="saveImage">保存</view>
  </view>
</view>
<!-- 分享组件 -->
<share id="sanfu-share" />
<!-- 活动内容列表模板 -->
<template name='group-goods'>
  <view class="group-goods">
    <view class="title">{{item.title}}</view>
    <view class="subtitle">{{item.subtitle}}</view>
    <view class="autor">
      <view class="autor-name">三福君</view>
      <view class="date">{{item.publishTime}}</view>
    </view>
    <view bindtap="toDetail" data-item="{{item}}" class="bg" style='background:url({{item.picture}}) center -5px / 100% no-repeat'>
      <!-- <view class="article-title">{{item.title}}</view> -->
    </view>
    <view class="icon-groups"></view>
    <view class="viewer">

      <view class="viewer-count">
        <span class="f7 iconDG-liulan"></span>
        <text>{{item.viewCnt || 0}}</text>
      </view>
      <view class="viewer-count" bindtap="showShare" data-shareItem="{{item}}">
        <span class="f7 iconDG-fenxiang" style="color:#F63979"></span>
        <text>{{item.shareCnt || 0}}</text>
      </view>
    </view>
  </view>
</template>