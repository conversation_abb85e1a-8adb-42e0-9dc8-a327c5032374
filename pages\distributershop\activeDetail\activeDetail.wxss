/* pages/distributershop/active/active.wxss */
@import "/font/icon_f7.wxss";
@import '../qrcode.wxss';

.autor view {
  padding-right: 10rpx;
}

.title {
  align-items: center;
  display: flex;
  color: #202020;
  font-size: 46rpx;
  font-weight: 700;
  background: white;
  padding: 20rpx 20rpx 10rpx;
}

.subtitle {
  align-items: center;
  display: flex;
  color: #858585;
  font-size: 32rpx;
  background: white;
  padding: 20rpx 20rpx 0;
}

.autor {
  align-items: center;
  display: flex;
  color: #a9a9a9;
  font-size: 26rpx;
  background: white;
  padding: 10rpx 20rpx 20rpx;
}

.article-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(360deg, rgba(0, 0, 0, .6), rgba(0, 0, 0, 0));
  margin: 0 auto;
  padding: 120rpx 20rpx 40rpx 20rpx;
  text-align: center;
  color: white;
  font-size: 26rpx;
}

.iconDG-fenxiang {
  height: 80rpx;
  width: 80rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 25px;
  font-size: 50rpx;
  box-shadow: 0 0 3px #888;
  position: fixed;
  bottom: 280rpx;
  right: 56rpx;
  transform: translateX(50%);
  color: #F63979;
}
