<wxs src="../../utils/utils.wxs" module="utils"></wxs>
<uni-popup new="{{new}}" type="center" show="{{show}}" zIndex="999" maskStyles="background:rgba(0,0,0,0.3);" bind:close="close" maskClick="{{maskClick}}">
  <block wx:if="{{!custom}}">
    <view class='tip_text_container' style="{{styles}}">
      <text class='tip_title' style="{{alertText&&alertText.length>4?'color:#303030;font-weight:400;':''}}{{titleStyle}}">{{alertText || '提示'}}</text>
      <image wx:if="{{img}}" lazy-load mode="aspectFit" src="{{utils.jpg2jpeg(img)}}" class="tip_img" />
      <block wx:if="{{slot}}">
        <slot></slot>
      </block>
      <text style="{{contentStyles}}" class='tip_content'>{{content}}</text>
      <view class='confirm' style="{{confirmStyles}}">
        <view wx:if="{{left===''||left!=false}}" class='left' bindtap='cancel'> {{ left || '取消'}}</view>
        <view class='right' bindtap='confirm'>{{ right || '确定'}}</view>
      </view>
    </view>
  </block>
  <block wx:else>
    <slot></slot>
  </block>
</uni-popup>
