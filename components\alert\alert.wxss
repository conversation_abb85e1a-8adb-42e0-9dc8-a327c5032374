view{
  box-sizing: border-box;
}


.tip_text_container {
  width: 600rpx;
  min-height: 280rpx;
  margin-top: -15%;
  background-color: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  border-radius: 16rpx;
  overflow: hidden;
}

.tip_title {
  font-size: 34rpx;
  color: #191919;
  font-weight: 700;
  line-height: 1.5;
  text-align: center;
  padding: 10rpx 24rpx;
  padding-top: 50rpx;
}
.tip_img{
  /* padding: 0 30rpx; */
  width: 420rpx;
  height: 280rpx;
  margin: 0 auto;
  margin-bottom: 12rpx;
}
.tip_content {
  font-size: 30rpx;
  color: #7f7f7f;
  line-height: 1.5;
  text-align: center;
  padding: 0 24rpx;
}

.confirm {
  font-size: 30rpx;
  width: 100%;
  text-align: center;
  padding: 50rpx;
  white-space: nowrap;
  text-overflow: clip;
  color: white;
  display: flex;
  justify-content: space-around;
}

.left {
  padding: 18rpx 20rpx;
  border-radius: 40rpx;
  flex: 1;
  box-shadow: 0 0 2rpx #888;
  color: #555;
  margin-right: 40rpx;
}

.right {
  padding: 18rpx 20rpx;
  border-radius: 40rpx;
  flex: 1;
  box-shadow: 0 0 2rpx #f02e72;
  background: linear-gradient(270deg, #E60012 0%, #FF7956 100%);
}
