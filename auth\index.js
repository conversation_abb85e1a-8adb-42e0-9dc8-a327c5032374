/** 公用小程序授权弹窗 */
console.log('authindex')
let queue = [];

function getPage() {
  const pages = getCurrentPages();
  return pages[pages.length - 1];
}
const app = getApp()
const Auth = (type, active, ee, opt, that) => {
  // type 0 登录  1 跳转办卡（停用）   2重新获取会员信息  3补充资料  4。验证手机号  5.返回手机号  6.会员协议更新
  // active 是否直接激活  跳过弹窗
  // ee  直接激活的带参
  let options = Object.assign({}, Auth.currentOptions);
  console.log(options)
  return new Promise((resolve, reject) => {
    const page = that || getPage();
    const auth = page && page.selectComponent(options.selector);
    delete options.selector;
    if (auth || active) {
      console.log('执行auth', type, ee)
      let confirm = async function(e) {
        console.log('confirmconfirmconfirm');
        if (type == 3) {
          wx.navigateTo({
            url: '/pages/user/userInfo/userInfo?type=1'
          })
          return
        }
        if (type == 1) {
          wx.navigateToMiniProgram({
            appId: 'wxeb490c6f9b154ef9', //固定为此 appId，不可改动
            path: 'pages/member/member', //固定为此path
            // pages/card_open/card_open
            // envVersion: 'trial', //商家正式版小程序拉起正式版组件；若此时为商家体验版小程序拉起体验版组件，则envVersion传值trial（体验版拉起，需找微信支付产品单独开权限）
            extraData: {
              create_card_appid: getApp().globalData2.appid == 'wxf7a02a5ff48a00f5' ? "wx0783c0bb36046d26" : 'wx76b5a45ad6713774', //测试wx0783c0bb36046d26 
              card_id: getApp().globalData2.appid == 'wxf7a02a5ff48a00f5' ? "pZ55u6K8fTjWmVI4dXPMVRYg_904" : 'pL-5nuMN0G9gAzcUVNWVvonYxetY', //测试 pZ55u6K8fTjWmVI4dXPMVRYg_904
              // card_code: "XX999999",
              outer_str: "123",
              activate_type: "ACTIVATE_TYPE_JUMP", // 指定跳转激活  ACTIVATE_TYPE_JUMP  ACTIVATE_TYPE_NORMAL
              // jump_url: "https://tm.sanfu.com/d" ,//跳转路径
              jump_appid: getApp().globalData2.appid,
              jump_path: 'pages/register/register'
            },
            success: function() {},
            fail: function() {},
            complete: function(res) {
              console.log('跳转回调', res)
            }
          })
          return;
        }
        if (e && e.detail && (e.detail.errMsg == "getUserInfo:fail auth deny" || e.detail.errMsg == "getPhoneNumber:fail user deny" || e.detail.errMsg == "getPhoneNumber:fail:user deny")) {
          wx.showToast({
            title: '已取消授权',
            icon: 'none',
            duration: 1500
          })
          return
        }
        wx.showToast({
          icon: 'none',
          title: '请稍等...',
        })
        if (!getApp().globalData2.session_key) {
          await getApp().apploading(getApp(), true, () => {})
        }
        if (type == 4) {
          getApp().sf.track('wechat_get_phone', {
            text: '验证手机号'
          })
          wx.showLoading()
          getApp().reqPost('ms-sanfu-wap-customer/updateMobile', {
            // "appid": getApp().globalData2.appid,
            "encryptedData": e.detail.encryptedData,
            "sessionKey": getApp().globalData2.session_key || null,
            "iv": e.detail.iv,
            sid: wx.getStorageSync('sid')
          }, res => {
            wx.hideLoading()
            wx.setStorageSync('bindcard', 1)
            // if (res.success) { //表示数据返回成功，
            typeof opt == "function" && opt()
            // wx.hideToast();
            // wx.showModal({
            //   content: JSON.stringify(res)
            // })
            // }
          })
          return
        }
        if (type == 5) {
          getApp().sf.track('wechat_get_phone', {
            text: '返回手机号'
          })
          wx.showLoading()
          getApp().reqPost('ms-sanfu-wap-customer/analyseMobileAuthInfo', {
            "appid": getApp().globalData2.appid,
            "encryptedData": e.detail.encryptedData,
            "sessionKey": getApp().globalData2.session_key || null,
            "iv": e.detail.iv,
            sid: wx.getStorageSync('sid')
          }, res => {
            wx.hideLoading()
            typeof opt == "function" && opt(res)
          })
          return
        }
        if (type == 6) {
          wx.showLoading()
          getApp().reqPost('ms-sanfu-wap-customer/updateMemberShipAgreementTime', {
            sid: wx.getStorageSync('sid')
          }, res => {
            wx.hideLoading()
            wx.setStorageSync("bindcard", 1)
            typeof opt == "function" && opt(res)
          })
          return
        }
        getApp().reqPost('wechat/user/wxMiniAppCustomerRegister', {
          "appid": getApp().globalData2.appid,
          "encryptedData": e.detail.encryptedData,
          "sessionKey": getApp().globalData2.session_key || null,
          "iv": e.detail.iv,
          shoId: wx.getStorageSync('sho_id') || ''
        }, async res => {
          if (res.success) { //表示数据返回成功，已经是会员
            auth.setData({
              show: false
            })
            wx.hideToast();
            wx.showToast({
              icon: 'none',
              title: type == 2 ? '更新成功' : '登录成功',
              duration: 1500
            })
            wx.setStorageSync('sid', res['sid'])
            wx.setStorageSync('cardid', res['curcusid'])
            wx.setStorageSync('opid', res['openid'])
            wx.setStorageSync('login', true)
            await getApp().checkCard(getApp())
            if (getPage() && typeof getPage().init == "function") {
              getPage().init('loading')
            }
            getApp().sr.setUser({
              app_id: 'wx24e34bc75882ebb3',
              open_id: res['openid']
            })
          
            // 开启上报
            getApp().sr.startReport()
            getApp().sr.flush()
            if (type != 2) {
              getApp().sf.track('login_success', {
                text: '手动登录'
              })

            }
            //检测用户绑定卡片  
            // if (type != 2)
            // getApp().checkCard(getApp(), '', true)
          } else {
            getApp().util.reqFail({
              msg: '登录出错:' + getApp().globalData.failMsg + '*********' + JSON.stringify(res) || '登录出错',
            })
          }
        })

        resolve(e)
      }
      if (active) confirm(ee)
      else {
        auth.setData(Object.assign({
          tip_index: type || 0,
          cancel: reject,
          confirm
        }, options));
      }
    } else {
      console.warn('未找到 auth 节点，请确认 selector  是否正确');
    }
  });
};
Auth.defaultOptions = {
  show: true,
  selector: '#sanfu-auth'
};
Auth.setDefaultOptions = options => {
  Object.assign(Auth.currentOptions, options);
};
Auth.resetDefaultOptions = () => {
  Auth.currentOptions = Object.assign({}, Auth.defaultOptions);
};
Auth.resetDefaultOptions();
export default Auth;
