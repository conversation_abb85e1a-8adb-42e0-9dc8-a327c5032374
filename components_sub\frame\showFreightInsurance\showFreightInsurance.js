const app = getApp()
Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    new: {
      type: Boolean,
      value: true
    },
  },
  data: {
    show: 0
  },
  attached() {},
  detached() {
    app.$bus.off('showFreightInsurance')
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {
      app.$bus.on('showFreightInsurance', content => {
        this.setData({
          show: 1,
          content: content
        })
      })
    },
    hide: function() {
      app.$bus.off('showFreightInsurance')
    },
    resize: function() {},
  },
  methods: {
    close() {
      this.setData({
        show: 0,
      })
    }

  }
});
