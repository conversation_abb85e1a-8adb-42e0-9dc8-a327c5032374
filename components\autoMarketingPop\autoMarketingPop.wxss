.auto-close {
  color: #777;
  font-size: 24rpx;
  width: 100%;
  text-align: center;
}

.top-box {
  min-height: 120rpx;
  width: 100%;
  border-radius: 12rpx;
  background: #fff;
  box-shadow: 2rpx 2rpx 10rpx #ddd;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  background: linear-gradient(60deg, #fff2e2, #fff 35%);
  padding-top: 16rpx;
}

.top-box .title {
  color: #fff;
  padding-left: 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
  font-weight: 700;
  background: linear-gradient(90deg, #f3284d, #fd6c90);

  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.top-box .top-box-btn {
  /* background: #fff; */
  padding: 12rpx 24rpx;
  font-size: 22rpx;
  border-radius: 18rpx;
  background: #f02e72;
  box-shadow: 2rpx 2rpx 6rpx #fd6c90;
  margin-left: auto;
  margin-right: 30rpx;
  white-space: nowrap;
  color: #fff;
}

.center-coupon {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.center-coupon .img {
  width: 700rpx;
  height: 420rpx;
  position: relative;
}

.center-coupon .img .title {
  position: absolute;
  width: 100%;
  bottom: 70rpx;
  text-align: center;
  font-size: 56rpx;
  font-weight: 900;
  color: transparent;
  text-shadow: 2rpx 6rpx 2rpx #D52A03;
}

.center-coupon .img .title::after {
  content: attr(data-content);
  display: block;
  position: absolute;
  color: #fff;
  left: 0;
  top: 0;
  width: 100%;
  text-align: center;
  font-size: 56rpx;
  font-weight: 900;
  z-index: 2;
  background-image: -webkit-linear-gradient(-90deg, #fffbf5, #fcc48f);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: none;
}

.center-coupon .content {
  background: #c5291c;
  width: 620rpx;
  border-radius: 0 0 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 18rpx 40rpx 18rpx;
}

.coupon-one {
  background: #fff;
  height: 160rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.coupon-one:last-of-type {
  margin: 0;
}

.coupon-one .money {
  width: 210rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coupon-one .money .money-text {
  position: relative;
  font-size: 96rpx;
  font-weight: 900;
  letter-spacing: -4rpx;
  padding-right: 10rpx;
  background-image: linear-gradient(180deg, #f08c33, #e22e1e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.coupon-one .money .money-text::before {
  content: ' ';
  position: absolute;
  right: -6rpx;
  top: 36rpx;
  z-index: 9;
  background: #fff;
  width: 26rpx;
  height: 26rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.coupon-one .money .money-text::after {
  content: '¥';
  position: absolute;
  right: -6rpx;
  top: 36rpx;
  z-index: 10;
  color: #fff;
  font-size: 22rpx;
  letter-spacing: 0rpx;
  border: 2rpx solid #d8827e;
  background: linear-gradient(180deg, #f08c33, #e22e1e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  width: 26rpx;
  height: 26rpx;
  text-align: center;
  line-height: 26rpx;
  border-radius: 50%;
}

.coupon-one .line {
  background-image: linear-gradient(0deg, #d8827e 0%, #d8827e 50%, transparent 0%);
  background-size: 2rpx 14rpx;
  background-repeat: repeat-y;
  height: 100%;
  width: 2rpx;
  padding: 20rpx 0;
  position: relative;
}

.coupon-one .line::before {
  content: ' ';
  position: absolute;
  right: -14rpx;
  top: -16rpx;
  width: 30rpx;
  height: 30rpx;
  background: #c5291c;
  border-radius: 50%;
}

.coupon-one .line::after {
  content: ' ';
  position: absolute;
  right: -14rpx;
  bottom: -16rpx;
  width: 30rpx;
  height: 30rpx;
  background: #c5291c;
  border-radius: 50%;
}


.coupon-one .right {
  max-width: 376rpx;
  padding-left: 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 22rpx;
  color: #919191;
}

.coupon-one .right .right-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.coupon-one .right .title {
  line-height: 1;
  margin-bottom: 6rpx;
  letter-spacing: 2rpx;
  color: #3a3a3a;
  font-size: 32rpx;
  font-weight: 900;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}

.content .btn {
  color: #c5291c;
  width: 240rpx;
  height: 70rpx;
  background: linear-gradient(180deg, #fffaf3, #ffab68);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 32rpx;
  font-size: 36rpx;
  font-weight: 700;
  letter-spacing: 4rpx;
  margin-top: 40rpx;
  flex: auto;
}
