var WxParse = require('./wxParse.js');
const app = getApp()
Component({
  properties: {
    openImg:{
      type: Boolean,
      value: false
    },
    content: {
      type: String,
      value: '',
      observer: function(n) {
        if (n) {
          WxParse.wxParse('html', 'html', this.data.content, this, this.data.padding, this.data.openImg);
        }
      }
    },
    padding: {
      type: Number,
      value: 0
    },
  },
  data: {},
  attached: function() {
    /**
     * WxParse.wxParse(bindName , type, data, target,imagePadding)
     * 1.bindName绑定的数据名(必填)
     * 2.type可以为html或者md(必填)
     * 3.data为传入的具体数据(必填)
     * 4.target为Page对象,一般为this(必填)
     * 5.imagePadding为当图片自适应是左右的单一padding(默认为0,可选)
     */
  },
  detached: function() {},
  methods: {
    openH5(e) {
      let url = e.currentTarget.dataset.url
      app.toH5(url)
      app.sf.track('vhtml_tap', {
        value: e.currentTarget.dataset.url
      })
    }
  }
})
