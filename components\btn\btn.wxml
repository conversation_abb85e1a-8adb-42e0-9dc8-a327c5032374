<view wx:if="{{close === 0}}" bindtap="closeProp" bindtouchstart="closeProp" style="{{propChecked ? 'width: 100%;height: 100%;background: rgba(0, 0, 0, 0.5);position: fixed;z-index:99999;left: 0px;bottom: 0;': ''}}">
  <view style="position: fixed;right: 100rpx;top: 75%;z-index: 13;font-size: 50rpx;color: #666;" wx:if="{{!propChecked}}" bindtap="close">×</view>
  <view style="position: fixed;right: 30rpx;top: 75%;z-index: 9;">
    <view class="k_menu_2">
      <view class="k_menu-open-button"  style="{{propChecked? 'width:85rpx;height:85rpx;background: #f568a7;display: flex;justify-content: center;align-items: center;': 'width:85rpx;height:85rpx;background: rgba(0, 0, 0, 0.6);display: flex;justify-content: center;align-items: center;'}}" >
        <view style="font-size: 22rpx;color: #fff;" wx:if="{{!propChecked}}" catchtap="openProp">
          <view>快速</view>
          <view>导航</view>
        </view>
        <text catchtap="closeProp" wx:if="{{propChecked}}" style="font-size: 70rpx;color: #fff;margin-top: -12rpx;">×</text>
      </view>
      <view class="{{propChecked?'k_menu-item k_menu-one':'close'}}" bindtap="goindex" style="{{propChecked? '': 'opacity:0'}}">
        <view class="iconshouye1 f7" style="font-size: 40rpx;color: #555;"></view>
        <text style="font-size: 20rpx;color:#555;">商城</text>
      </view>
      <view class="{{propChecked?'k_menu-item k_menu-two':'close'}}" bindtap="myOrder" style="{{propChecked? '': 'opacity:0'}}">
        <view class="icongerenzhongxin f7" style="font-size: 40rpx;color: #555;">
        </view>
        <text style="font-size: 20rpx;color:#555;">订单</text>
      </view>
      <view class="{{propChecked?'k_menu-item k_menu-three':'close'}}" bindtap="cart" style="{{propChecked? '': 'opacity:0'}}">
        <view class="icongouwuche1 f7" style="font-size: 40rpx;color: #555;">
        </view>
        <text style="font-size: 20rpx;color:#555">购物车</text>
      </view>
      <view class="{{propChecked?'k_menu-item k_menu-four':'close'}}" bindtap="goScan" style="{{propChecked? '' : 'opacity:0'}}">
        <view class="yd-icon yd-icon-qrscan" style="font-size: 40rpx;color: #555;">
        </view>
        <text style="font-size: 20rpx;color:#555;">扫一扫</text>
      </view>
      <view class="{{propChecked?'k_menu-item k_menu-five':'close'}}" bindtap="gokefu" style="{{propChecked? '' : 'opacity:0'}}">
        <view class="iconzaixiankefu f7" style="font-size: 40rpx;color: #555;">
        </view>
        <text style="font-size: 20rpx;color:#555">客服</text>
      </view>
      <view class="{{propChecked?'k_menu-item k_menu-six':'close'}}" bindtap="gosort" style="{{propChecked? '' : 'opacity:0'}}">
        <view class="iconfenlei1 f7" style="font-size: 40rpx;color: #555;">
        </view>
        <text style="font-size: 20rpx;color:#555;">分类</text>
      </view>
    </view>
  </view>
  <!--旧快捷按钮-->
  <view wx:if="{{false}}" class="button" style="display:none;">
    <view class="fastNavOut">
      <transition name="fade">
        <view class="fastNav" id="click_event" bindtap="open" wx:if="{{close === 0}}">
          <image class="fastNavImg" src="https://img.sanfu.com/img/index_boy/t1.png" />
          <text>快速导航</text>
        </view>
      </transition>
    </view>
    <view style="clear:both"></view>
    <view class="outBtn" v-show="zhankai">
      <view bindtap="open" style="position:fixed; top:0; right:0; width:100%;height:48%; z-index:999;"></view>
      <view bindtap="open" style="position:fixed; bottom:0; right:0; width:100%;height:19%; z-index:999;"></view>
      <view bindtap="open" style="position:fixed; bottom:19%; left:0; width:20%;height:25%; z-index:999;"></view>
      <transition name="right">
        <view wx:if="{{zhankai}}">
          <view style="padding: 50rpx 0 50rpx 160rpx;margin-top:680rpx">
            <view class="foldNav" bindtap="open">
              <image class="foldImg" src="https://img.sanfu.com/img/index_boy/t2.png" />
              收起
            </view>
            <view style="background: #fff;margin-top: -30rpx;margin-left: 120rpx;padding: 10rpx 0 10rpx 20rpx;">
              <view class="iconTo" bindtap="goindex">
                <view style="height:50rpx;">
                  <yd-icon name="iconshouye1 f7" custom size="40rpx" color="#f370aa"></yd-icon>
                </view>
                <view>首页</view>
              </view>
              <view class="iconTo" bindtap="myOrder">
                <view style="height:50rpx;">
                  <yd-icon name="icongerenzhongxin f7" custom size="40rpx" color="#f370aa"></yd-icon>
                </view>
                <view>个人中心</view>
              </view>
              <view class="iconTo" bindtap="gosort">
                <view style="height:50rpx;">
                  <yd-icon name="iconfenlei1 f7" custom size="40rpx" color="#f370aa"></yd-icon>
                </view>
                <view>分类</view>
              </view>
              <view class="iconTo" bindtap="cart">
                <view style="height:50rpx;">
                  <yd-icon name="icongouwuche1 f7" custom size="40rpx" color="#f370aa"></yd-icon>
                </view>
                <view>购物车</view>
              </view>
              <view class="iconTo" bindtap="gokefu">
                <view style="height:50rpx;">
                  <yd-icon name="iconzaixiankefu f7" custom size="40rpx" color="#f370aa"></yd-icon>
                </view>
                <view>联系客服</view>
              </view>
              <view class="iconTo" bindtap="goScan">
                <view style="height:50rpx;">
                  <yd-icon name="qrscan" size="40rpx" color="#f370aa"></yd-icon>
                </view>
                <view>扫一扫</view>
              </view>
            </view>
          </view>
        </view>
      </transition>
    </view>
  </view>
</view>
