.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9998;
}

.loading {
  position: absolute;
  z-index: 9999;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.65);
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.65);
  padding-bottom: 100rpx;
  zoom: 0.75;
}

.loading-box::-webkit-scrollbar,
.loading::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.loading-box {
  z-index: 999;
  width: 190rpx;
  height: 190rpx;
  /* background: skyblue; */
  position: relative;
  transform: scale(0.5);
  box-sizing: content-box;
  border: 50rpx solid rgba(255, 255, 255, 0);
  border-radius: 30rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

@keyframes load {

  0% {
    opacity: 1;
  }

  80% {
    opacity: 0;
  }

  100% {
    opacity: 0.9;
  }
}


.loading-box>.view {
  width: 48rpx;
  height: 48rpx;
  border-radius: 2rpx;
  opacity: 0;
  animation: load 0.8s ease infinite;
}


.loading-box>.view5 {
  background: #e70012;
  animation-duration: 0.7s;
  margin-bottom: 18rpx;
  margin-right: 18rpx;
}


.loading-box>.view1 {
  transform: rotate(-16deg);
  background: #bdd632;
  margin-bottom: 18rpx;
  margin-right: 18rpx;
}


.loading-box>.view2 {
  background: #f49600;
  animation-delay: -0.7s;
  margin-bottom: 18rpx;
  margin-right: 18rpx;
}


.loading-box>.view3 {
  background: #e5007f;
  animation-delay: -0.6s;
  margin-bottom: 18rpx;
}

.loading-box>.view6 {
  transform: rotate(-16deg);
  background: #786bb0;
  animation-delay: -0.5s;
  margin-bottom: 18rpx;
}

.loading-box>.view9 {
  background: #c3d601;
  animation-delay: -0.4s;
}


.loading-box>.view8 {
  background: #e5007f;
  transform: rotate(-16deg);
  animation-delay: -0.3s;
  margin-right: 18rpx;
}

.loading-box>.view7 {
  background: #f49600;
  animation-delay: -0.2s;
  margin-right: 18rpx;
}

.loading-box>.view4 {
  background: #02a0e9;
  animation-delay: -0.1s;
  margin-bottom: 18rpx;
  margin-right: 18rpx;
}
