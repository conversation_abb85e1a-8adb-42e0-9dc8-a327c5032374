<wxs module="f">
function touchMove(e, ins) {
  ins.selectComponent('#sph').setStyle({
    left: e.changedTouches[0].clientX  + 'px',
    top: e.changedTouches[0].clientY  + 'px'
  })
}
module.exports.touchMove = touchMove;
</wxs>
<view class="tigger"></view>
<view class="top-btn-box" wx:if="{{showTopBtn}}" catchtap="toTop" style="bottom: {{top}}rpx;z-index: {{zIndex}};">
  <view class="top-btn-item">
    <view class="f7 iconbacktop top-btn"></view>
  </view>
</view>
<view wx:if="{{showDisBtn && !limitShop}}" class="dis-btn-box" catchtap="toDis" style="bottom: {{top+240}}rpx;background: #6667ab;z-index: {{zIndex}};">
  <view class="dis-item">导购</view>
  <view class="dis-item">分销</view>
  <view class="uni-icon uni-icon-closeempty close" catchtap="closeDis"></view>
</view>


<view id='sph' wx:if="{{showSPHBtn>0}}" class="sph-box" style="top: 30vh;z-index: {{zIndex}};" catchtap="getSPH" catchtouchmove='{{f.touchMove}}'>
  <view class="icon1" wx:if="{{showSPHBtn==2}}">
    <view></view>
    <view></view>
    <view></view>
  </view>
  <view class="uni-icon uni-icon-checkbox-filled icon2" wx:if="{{showSPHBtn==1}}">
  </view>
  <yd-icon name="clock"></yd-icon>
  <view class="title">{{showSPHBtn==2?'直播中':'直播预约'}}</view>
</view>


