// pages/component/alert/alert.js
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    alertText: String,
    contentStyles: String,
    styles: String,
    titleStyle: String,
    confirmStyles: String,
    new: {
      type: Boolean,
      value: true
    },
    custom: {
      type: Boolean,
      value: false
    },
    autoclose: {
      type: Boolean,
      value: true
    },
    maskClick: {
      type: Boolean,
      value: false
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    text: '',
    show: !1
  },

  /**
   * 组件的方法列表
   */
  methods: {
    cancel() {
      this.close()
      typeof this.data.cancel == 'function' && this.data.cancel()
    },

    async confirm() {
      if (this.data.autoclose) {
        this.close()
        setTimeout(() => {
          wx.nextTick(() => {
            typeof this.data.confirm == 'function' && this.data.confirm()
          })
        }, 50)
      } else {
        typeof this.data.confirm == 'function' && this.data.confirm()
      }
    },
    close() {
      this.setData({
        show: false
      })
    }
  }
})
