<!--pages/distributershop/change_disshop/change_disshop.wxml-->
<view class="container">
	<picker style="width:100%;" mode="multiSelector" bindchange="chooseconfirm" bindcolumnchange="PickerChange" value="{{cityIndexArr}}" range="{{pickerlist}}">
		<view class="item">
			<view class="title">城市：</view>
			<view class="other" style="{{cityIndexArr[0]==0?'color:#999;':''}}">
				{{showCity}}
			</view>
			<view style="height:100%;padding:0 12rpx;display:flex;align-items:center;" wx:if="{{cityIndexArr[0]>0}}" catchtap="clearcity">
				<text class="uni-icon uni-icon-closeempty" style="color: #202020;margin-top:2rpx;font-size: 34rpx;" ></text>
			</view>
		</view>
	</picker>
	<view class="item">
		<view class="title">搜索门店：</view>
		<view class="other">
			<input placeholder-style="color:#999;" bindinput="input" bindconfirm="search" confirm-type="search" type="text" data-name="search_keyword" value="{{search_keyword}}" placeholder="请输入店名或店号" style="color: #404040;" />
			<view style="height:100%;padding:0 12rpx;display:flex;align-items:center;" wx:if="{{search_keyword != ''}}" data-name="search_keyword" bindtap="clearinput">
				<text class="uni-icon uni-icon-closeempty" style="color: #202020;margin-top:2rpx;font-size: 34rpx;" ></text>
			</view>
		</view>
	</view>
	<!-- <text style="font-size:28rpx;color:#404040;padding-left:10rpx;">提示：可仅使用1个条件搜索，官网输入GZW搜索</text> -->
	<button class="btn" hover-class="btn-hover" bindtap="search" data-change="1">搜索门店</button>
	<view class="item" style="height:auto;" wx:for="{{shoplist}}" wx:key="index">
		<view class="other" style="justify-content: space-between;padding: 10rpx 0;">
			<view style="display:flex;flex-direction: column;align-items: flex-start;">
				<text>门店：{{item.shoName}} </text>
				<text>店号：{{item.shoId}}</text>
			</view>
			<view style="padding: 12rpx 32rpx;background: #fd6c90;color: #fff;border-radius:4rpx;" bindtap="chooseshop" data-index="{{index}}">选择</view>
		</view>
	</view>
	<view wx:if="{{has_more != 4}}" class="show_end" bindtap="search">
		<view wx:if="{{has_more == 1}}" class="loadmore-icon"></view>
		<text>{{has_more==2&&shoplist.length==0?'搜索结果：暂无门店信息': loadingText[has_more] }}</text>
	</view>

</view>