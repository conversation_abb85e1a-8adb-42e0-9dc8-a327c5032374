// pages/distributershop/change_disshop/change_disshop.js
import city_data from "../city_data.js"
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    search_keyword: '', //搜索输入店号店名
    cityIndexArr: [0, 0],
    pickerlist: [],
    showCity: '点击选择城市',
    shoplist: [],
    page: 1,
    has_more: 4, //对应loadingtext  3：无数据   4，不显示
    loadingText: ['点击加载更多', '正在加载...', '没有更多了', '暂无该推荐'],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.init()
  },
  init() {
    //初始化城市选择列表
    let picker1 = []
    let picker2 = []

    for (let i in city_data) {
      picker1.push(city_data[i].p)
    }
    picker2 = city_data[0].c
    this.setData({
      pickerlist: [picker1, picker2]
    })
  },
  chooseconfirm(e) {
    //选择器确认
    this.setData({
      cityIndexArr: e.detail.value
    })
    this.setshowcity()
  },
  PickerChange(e) {
    //选择器滑动操作处理
    let column = parseInt(e.detail.column)
    let value = parseInt(e.detail.value)

    console.log(e.detail.column, e.detail.value)
    if (column == 0) {
      this.data.pickerlist[1] = city_data[value].c
      this.setData({
        pickerlist: this.data.pickerlist,
        cityIndexArr: [value, 0]
      })
    } else {
      this.data.cityIndexArr[1] = value
      this.setData({
        showCity: this.data.showCity,
        cityIndexArr: this.data.cityIndexArr
      })
    }
    this.setshowcity()
  },
  setshowcity() {
    //选择城市显示处理
    this.data.showCity = this.data.pickerlist[0][this.data.cityIndexArr[0]] + this.data.pickerlist[1][this.data.cityIndexArr[1]]
    if (this.data.pickerlist[0][this.data.cityIndexArr[0]] == this.data.pickerlist[1][this.data.cityIndexArr[1]])
      this.data.showCity = this.data.pickerlist[0][this.data.cityIndexArr[0]]
    this.setData({
      showCity: this.data.showCity,
    })
  },
  clearcity() {
    //清除城市
    this.setData({
      cityIndexArr: [0, 0],
      showCity: '点击选择城市'
    })
  },
  //******输入绑定*********
  input: function(e) {
    this.setData({
      [e.currentTarget.dataset.name]: e.detail.value
    })
  },
  //******快速清除输入*********
  clearinput: function(e) {
    this.setData({
      [e.currentTarget.dataset.name]: ''
    })
  },
  //******搜索*********
  search: async function(e) {
    let change
    if (e)
      change = e.currentTarget.dataset.change
    if (change == 1) {
      if (this.data.search_keyword.length <= 0 && this.data.cityIndexArr[0] == 0) {
        wx.showToast({
          title: '请搜索城市或输入关键词',
          icon: 'none'
        })
        return;
      }
      this.data.page = 1
      this.data.shoplist = []
    } else if (this.data.has_more == 2) return
    let cityName = ''
    let provinceName = ''
    let inputName = this.data.search_keyword
    if (this.data.cityIndexArr[0] > 0) {
      provinceName = this.data.pickerlist[0][this.data.cityIndexArr[0]]
      cityName = this.data.pickerlist[1][this.data.cityIndexArr[1]]
    }
    this.setData({
      has_more: 1,
      shoplist: this.data.shoplist
    })
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/agentShop/page', {
      sid: wx.getStorageSync('sid'),
      cityName: cityName,
      provinceName: provinceName,
      inputName: inputName,
      page: this.data.page,
      pageSize: 12
    }, )
    if (res.success) {
      this.data.page++;
      if (res.data && res.data.result && res.data.result.length < 12) {
        this.data.has_more = 2
      } else {
        this.data.has_more = 0
      }

      this.data.shoplist = [...this.data.shoplist, ...res.data.result]
      this.setData({
        shoplist: this.data.shoplist,
        has_more: this.data.has_more
      })
    } else {
      app.util.reqFail(res)
    }
  },
  chooseshop(e) {
    let index = e.currentTarget.dataset.index
    wx.showModal({
      title: '提示',
      content: '是否选择' + this.data.shoplist[index].shoName,
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '请稍等...'
          })
          let res2 = await app.reqPost('ms-sanfu-wap-customer-distribution/distribution/agentShop/update', {
            shoId: this.data.shoplist[index].shoId,
            sid: wx.getStorageSync('sid'),
          })
          wx.hideLoading()
          if (res2.success) {
            wx.showToast({
              title: res2.msg,
              mask: true,
              duration: 600
            })
            wx.setStorageSync('disshop', this.data.shoplist[index].shoId || '')
            app.local.set('sho_id', this.data.shoplist[index].shoId)
            setTimeout(() => {
              wx.navigateBack({
                delta: 1
              })
            }, 600);

          }
        }
      }

    })
  },
  onReachBottom() {
    if (this.data.page > 1)
      this.search()
  }
})
