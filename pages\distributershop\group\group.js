// pages/distributershop/group/group.js
import Share from '../../../components/common/share/index.js'
import Alert from '../../../components/alert/index'
const app = getApp()
import common from '../common.js'
const prefix = (uri) => {
  return 'ms-sanfu-wap-customer-distribution/distribution/' + uri;
}
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showLoading: true,
    showqrcode: true,
    isHis: true,
    isShowCaogao: false,
    useds: [],
    shareItem: {},
    loadingText: ['点击加载更多', '正在加载...', '没有更多了', '暂无组合商品'],
    viewList: [{
      page: 1,
      has_more: 0,
      type_index: 0,
      data: [],
      reqData: {
        dateSort: '0',
        viewCntSort: '',
        shareCntSort: '',
        flag: 0
      },
      combType: [{
        name: '全部',
        id: '0'
      }, {
        name: '新品',
        id: '1'
      }, {
        name: '热卖',
        id: '2'
      }],
      groups: [{
          name: '日期',
          prop: 'dateSort',
          order: 0
        },
        {
          name: '浏览量',
          prop: 'viewCntSort',
          order: 0
        },
        {
          name: '分享次数',
          prop: 'shareCntSort',
          order: 0
        }
      ],
    }, {
      page: 1,
      has_more: 0,
      type_index: 0,
      data: [],
      reqData: {
        dateSort: '0',
        viewCntSort: '',
        shareCntSort: '',
        flag: 1
      },
      combType: [{
        name: '全部',
        id: '0'
      }, {
        name: '新品',
        id: '1'
      }, {
        name: '热卖',
        id: '2'
      }],
      groups: [{
          name: '日期',
          prop: 'dateSort',
          order: 0
        },
        {
          name: '浏览量',
          prop: 'viewCntSort',
          order: 0
        },
        {
          name: '分享次数',
          prop: 'shareCntSort',
          order: 0
        }
      ]
    }],
    caogaolist: [],
    caogaoNum: 0,
    // tab切换
    currentTab: 1,


  },
  onLoad: function(options) {
    // 获取系统信息
    this.getCaogaoCount()

    this.workTrace(2)
  },
  onShow() {
    if (this.data.currentTab == 1) {
      this.data.viewList[1].page = 1
      this.data.viewList[1].data = []
      this.getCaogaoCount()
    }
    if ((this.data.currentTab == 0 && this.data.viewList[0].data.length == 0) || this.data.currentTab == 1)
      this.getPages()
    if (this.data.isShowCaogao) {
      this.getCaogao()
    }

  },
  orderBy(e) {
    let detail = e.detail
    let obj = {}
    detail.all.map(el => {
      if (el.type !== 1 && detail['item'].prop === el.prop) {
        obj[el.prop] = el.order
      } else {
        obj[el.prop] = ''
      }
    })
    if (detail.prop) {
      obj[detail.prop] = detail['checkItem'].classId
    }
    console.log(detail, {
      ...obj
    })

    obj = {
      ...this.data.viewList[this.data.currentTab].reqData,
      ...obj
    }
    this.data.viewList[this.data.currentTab].reqData = obj
    this.data.viewList[this.data.currentTab].page = 1


    this.getPages()
  },
  loadData() {
    let index = this.data.currentTab
    if (this.data.viewList[index].has_more != 0) return
    this.data.viewList[index].has_more = 1
    this.setData({
      [`viewList[${index}].has_more`]: 1
    })
    this.getPages()
  },
  //分类点击修改
  changeCombType(e) {
    let index = e.currentTarget.dataset.index
    let currentTab = this.data.currentTab
    if (index == this.data.viewList[currentTab].type_index) return

    for (let i in this.data.viewList[currentTab].reqData) {
      //分类筛选清空
      if (i == 'flag') continue
      this.data.viewList[currentTab].reqData[i] = ''
      if (i == 'dateSort')
        this.data.viewList[currentTab].reqData[i] = 0
    }
    this.selectComponent("#order" + currentTab).reset();

    this.data.viewList[currentTab].page = 1
    this.setData({
      ['viewList[' + currentTab + '].type_index']: index,
      has_more: 4
    })
    console.log(this.data.reqData)

    this.getPages()
  },
  /**
   * 分页接口
   */
  getPages() {
    let index = this.data.currentTab
    let page = this.data.viewList[index].page
    let obj = this.data.viewList[index].reqData
    console.log('index', index)
    console.log(obj)

    obj = {
      ...obj,
      ...{
        combType: this.data.viewList[index].combType[this.data.viewList[index].type_index].id || 0,
        sid: wx.getStorageSync('sid'),
        shoId: app.local.get('sho_id'),
        page: page,
        pageSize: 8
      }
    }
    if (page == 1) {
      this.setData({
        showLoading: true
      })
    }
    app.reqGet(prefix('goodsh/page'), obj, res => {
      if (res.success) {
        let data = res.data
        if (page == 1) {
          this.data.viewList[index].data = []
        }
        for (let i in data.result) this.data.viewList[index].data.push(data.result[i])
        if (data.totalCount == this.data.viewList[index].data.length) {
          this.data.viewList[index].has_more = 2
        } else {
          this.data.viewList[index].has_more = 0
          this.data.viewList[index].page++
        }
        if (data.totalCount == 0 || data.totalCount == null) this.data.viewList[index].has_more = 3
        let target1 = 'viewList[' + index + '].has_more'
        let target2 = 'viewList[' + index + '].data'
        this.setData({
          [target1]: this.data.viewList[index].has_more,
          [target2]: this.data.viewList[index].data
        })
      } else {
        wx.showToast({
          title: res.msg || '服务器请求失败！',
          icon: 'none'
        })
      }
      this.setData({
        showLoading: false
      })
    })
  },

  // 滑动切换tab
  bindChange: function(e) {
    let current = e.detail.current
    this.setData({
      currentTab: current
    });
    if (this.data.viewList[current].data.length === 0 && this.data.viewList[current].has_more == 0) {
      this.getPages()
    }
  },
  // 点击tab切换
  swichNav: function(e) {
    let current = e.target.dataset.current
    if (this.data.currentTab === e.target.dataset.current)
      return false;
    this.setData({
      currentTab: current
    });
    if (this.data.viewList[current].data.length === 0 && this.data.viewList[current].has_more == 0) {
      this.getPages()
    }
  },
  async getCaogao() {
    this.setData({
      showLoading: true
    })
    let res = await app.reqGet(prefix('goodshDrafts/page'), {
      page: 1,
      pageSize: 20,
      sid: wx.getStorageSync('sid')
    })
    this.setData({
      caogaolist: res.data.result || []
    })
    console.log(this.data.caogaolist)
    this.setData({
      showLoading: false
    })
  },
  async getCaogaoCount() {
    let res = await app.reqGet(prefix('goodshDrafts/count'), {
      sid: wx.getStorageSync('sid')
    })
    this.setData({
      caogaoNum: res.data || 0
    })
  },

  showCaogao: function() {
    let that = this
    that.setData({
      isShowCaogao: !that.data.isShowCaogao
    });
    if (that.data.isShowCaogao) {
      that.getCaogao()
    }
  },
  closeCaogao(){
    that.setData({
      isShowCaogao: false
    });
  },
  toEdit: function() {
    app.toH5('user/disshop-edit?type=save')
  },
  toDetail(e) {
    let id = e.currentTarget.dataset.item.recommendId
    wx.navigateTo({
      url: '/pages/distributershop/groupdetail/detail?id=' + id
    })
  },
  /** 编辑草稿 */
  editCaogao(e) {
    let item = e.target.dataset.item
    app.toH5('user/disshop-edit?type=edit&id=' + item.recommendId)
    console.log(item)
  },
  /** 删除草稿 */
  delCaogao(e) {
    let item = e.target.dataset.item
    Alert({
      alertText: '确认删除该组合？',
      confirm: async el => {
        let res = await app.reqPost(prefix('goodsh/delete'), {
          recommendId: item.recommendId,
          channel: 'distributionFront',
          sid: wx.getStorageSync('sid')
        })
        wx.showToast({
          title: res.success ? '删除成功' : res.msg || '删除失败',
          icon: 'none'
        })
        if (res.success) {
          this.getCaogao()
          this.getCaogaoCount()
        }
      }
    })
    console.log(item)
  },
  /** 预览草稿 */
  previewCaogao(e) {
    let item = e.target.dataset.item
    wx.navigateTo({
      url: '/pages/distributershop/groupdetail/detail?type=preview&id=' + item.recommendId
    })
  },
  async showShareCode2(e) {
    let that = this
    let shareItem = e.currentTarget.dataset.shareitem
    that.data.shareItem = shareItem
    Share().then(el => {
      console.log('微信好友')
      let startCount = shareItem['shareCnt'] || 0
      that.transfer(shareItem.recommendId, 2)
      // that.updateShareCount('share_group', shareItem['recommendId'], startCount)
    }).catch(el => {
      that.transfer(shareItem.recommendId, 2)
      console.log(shareItem)
      wx.navigateTo({
        url: '../groupshare/groupshare?type=group&id=' + shareItem.recommendId,
      })

    })
  },
  ...common,
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function(options) {
    var that = this;
    let shareItem = that.data.shareItem
    let endUrl = `/pages/distributershop/groupdetail/detail?id=${shareItem.recommendId}`
    if (wx.getSystemInfoSync().environment == 'wxwork')
      endUrl += ',from=qymall'
    console.log('endUrl', endUrl)

    // 设置菜单中的转发按钮触发转发事件时的转发内容
    var shareObj = {
      title: shareItem.recommendTitle, // 默认是小程序的名称(可以写slogan等)
      path: endUrl, // 默认是当前页面，必须是以‘/’开头的完整路径
      imageUrl: shareItem.coverUrl || '',
    };
    return shareObj;
  }
})
