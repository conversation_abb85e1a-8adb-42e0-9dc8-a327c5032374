// pages/distributershop/services/index/index.js
const app = getApp()
Page({

  /**
   * 页面的初始数据 111
   */
  data: {
    showRefresh: false, //外部传入刷新
    list: [{
        name: '全部',
        id: '',
        data: [],
        page: 1,
        has_more: 0,
        total: 0
      },
      {
        name: '个微来源',
        id: 1,
        data: [],
        page: 1,
        has_more: 0,
        total: 0
      },
      {
        name: '企微来源',
        id: 2,
        data: [],
        page: 1,
        has_more: 0,
        total: 0
      },
      { //搜索
        id: '',
        data: [],
        page: 1,
        has_more: 0,
        total: 0
      },
      { //筛选
        id: '',
        data: [],
        page: 1,
        has_more: 0,
        total: 0
      }
    ],
    loadingText: ['点击加载更多', '正在加载...', '没有更多了~'],
    currentTab: 0,
    filterShow: false, //过滤弹窗
    filterLoading: false,
    labelList: [],

    form: {
      startTime: '',
      endTime: '',
      arrList: [],
      labels: ''
    },
    searchkey: '',
    qyLogin: false,
    qyEntry: false
  },
  onLoad: function(options) {},

  onShow: function() {
    console.log(this.data.showRefresh)
    if (this.data.showRefresh) {
      this.onPullDownRefresh()
      this.data.showRefresh = false
    }
  },
  onReady(){
    this.getList()
    this.getContext()
  },
  onPullDownRefresh: function() {
    let index = this.data.currentTab
    this.setData({
      [`list[${index}].page`]: 1,
      [`list[${index}].has_more`]: 0,
      [`list[${index}].data`]: [],
    })
    this.getList()
    wx.stopPullDownRefresh()
  },
  onReachBottom: function() {
    this.getList()
  },
  async getList() {
    let index = this.data.currentTab
    if (this.data.list[index].has_more != 0) return
    this.setData({
      [`list[${index}].has_more`]: 1
    })
    let getdata = {
      sid: wx.getStorageSync('sid'),
      page: this.data.list[index].page,
      pageSize: 10,
      userSource: this.data.list[index].id,
    }
    if (index == 3) {
      //搜索
      getdata.searchKey = this.data.searchkey
    } else if (index == 4) {
      //过滤
      getdata.labelName = this.data.form.labels
      getdata.beginCreateTime = this.data.form.startTime
      getdata.endCreateTime = this.data.form.endTime
    }
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/assistant/guide/customerList', getdata)
    if (res.success) {
      res.data.result = res.data.result || []
      if (res.data.result && res.data.result.length == 10) {
        this.data.list[index].has_more = 0
        this.data.list[index].page++
      } else this.data.list[index].has_more = 2
      this.data.list[index].data = [...this.data.list[index].data, ...res.data.result]
      this.data.list[index].total = res.data.totalCount || 0
      this.setData({
        [`list[${index}]`]: this.data.list[index]
      })
    } else {
      this.data.list[index].has_more = 0
      this.setData({
        [`list[${index}]`]: this.data.list[index]
      })
      app.util.reqFail(res)
    }
  },
  async getLabel() {
    this.setData({
      filterLoading: true
    })
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/assistant/guide/distributionLabel', {
      sid: wx.getStorageSync('sid')
    })
    this.setData({
      filterLoading: false
    })
    if (res.success) {
      for (let i in res.data) this.data.form.arrList.push([])
      this.setData({
        labelList: res.data,
        form: this.data.form
      })
    } else {
      app.util.reqFail(res)
    }
  },
  openUser(e) {
    let cusid = e.currentTarget.dataset.cusid
    wx.navigateTo({
      url: '../user/user?cusid=' + cusid
    })
  },

  changeTab: function(e) {
    this.setData({
      currentTab: e.detail,
      searchkey: ''
    })
    if (this.data.list[this.data.currentTab].data.length == 0) this.getList()
  },
  toSearch() {
    if (!this.data.searchkey) {
      wx.showToast({
        title: '请输入关键词',
        icon: 'none'
      })
      return
    }
    this.setData({
      currentTab: 3,
      [`list[3].page`]: 1,
      [`list[3].has_more`]: 0,
      [`list[3].data`]: [],
    })
    this.getList()
  },
  toFilter() {
    this.setData({
      filterShow: true
    })
    if (this.data.labelList.length == 0) this.getLabel()
  },
  closeFilter() {
    this.setData({
      filterShow: false
    })
  },
  checkChange(e) {
    this.setData({
      filterLoading: true
    })
    let index = e.currentTarget.dataset.index
    let vlist = e.detail.value
    let type // 1 增  0 减
    if (this.data.form.arrList[index].length > vlist.length) type = 0
    else type = 1
    let arr = []
    //差集
    var result = this.data.form.arrList[index].concat(vlist).filter((v) => {
      return this.data.form.arrList[index].indexOf(v) === -1 || vlist.indexOf(v) === -1
    })
    //增减的index
    result = result[0]
    if (type) {
      let length = 0
      for (let i in this.data.form.arrList) {
        length += this.data.form.arrList[i].length
      }
      this.setData({
        [`labelList[${index}].distributionLabelList[${result}].check`]: true
      })
      if (length + 1 > 5) {
        wx.showToast({
          title: '最多选择5个标签',
          icon: 'none'
        })
        this.setData({
          [`labelList[${index}].distributionLabelList[${result}].check`]: false,
          filterLoading: false
        })
        return
      }
    } else {
      this.setData({
        [`labelList[${index}].distributionLabelList[${result}].check`]: false,
      })
    }
    this.data.form.arrList[index] = vlist
    //处理数据
    let values = []
    for (let i in this.data.form.arrList) {
      for (let j in this.data.form.arrList[i]) {
        values.push(this.data.labelList[i].distributionLabelList[this.data.form.arrList[i][j]].labelName)
      }
    }
    this.data.form.labels = values.join(',')
    console.log(this.data.form.labels)
    this.setData({
      filterLoading: false
    })

  },
  formReset() {
    let list = []
    for (let i in this.data.labelList) {
      list.push([])
      for (let j in this.data.labelList[i].distributionLabelList) {
        this.data.labelList[i].distributionLabelList[j].check = false
      }
    }
    this.setData({
      labelList: this.data.labelList,
      form: {
        startTime: '',
        endTime: '',
        arrList: list,
        labels: ''
      },
      currentTab: 0
    })
  },
  formSubmit() {
    if (!this.data.form.startTime && !this.data.form.endTime && !this.data.form.labels) {
      wx.showToast({
        title: '请选择任意条件',
        icon: 'none'
      })
      return
    }
    this.setData({
      currentTab: 4,
      [`list[4].page`]: 1,
      [`list[4].has_more`]: 0,
      [`list[4].data`]: [],
      filterShow: false
    })
    this.getList()
  },
  input(e) {
    let key = e.currentTarget.dataset.key
    this.setData({
      [key]: e.detail.value
    })
  },
  setStar(e) {
    let index = e.currentTarget.dataset.index
    let item = this.data.list[this.data.currentTab].data[index]
    wx.showLoading()
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/customer/star', {
      curCusId: item.cusId,
      sid: wx.getStorageSync('sid'),
      starType: item.isStar ? 0 : 1 //星标状态[0-无星标/1-有星标]
    }, res => {
      wx.hideLoading()
      if (res.success) {
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        })
        this.onPullDownRefresh()
      } else {
        app.util.reqFail(res)
      }
    })
  },
  checkQy(toast) {
    return new Promise(resolve => {
      let sys = wx.getSystemInfoSync()
      if (sys.environment == 'wxwork') {
        let caniuse = wx.qy.canIUse('getEnterpriseUserInfo')
        if (this.data.qyLogin) resolve(true)
        else
          wx.qy.login({
            success: res2 => {
              if (res2.code) {
                app.reqPost('ms-sanfu-wechat-customer/customer/wxWork/wxMiniAppLogin', {
                  sid: wx.getStorageSync('sid'),
                  appid: app.globalData2.appid,
                  code: res2.code
                }, res => {
                  if (res.success) {
                    this.setData({
                      qyLogin: true
                    })
                    this.data.userId = res.userId
                    resolve(true)
                  } else {
                    resolve(false)
                    app.util.reqFail(res)
                  }
                })
              } else {
                resolve(false)
                wx.showModal({
                  content: '登录失败' + JSON.stringify(res),
                  showCancel: false
                })
              }
            },
            fail: res => {
              resolve(false)
              wx.showModal({
                content: '登录失败' + JSON.stringify(res),
                showCancel: false
              })
            }
          })

      } else {
        if (!toast)
          wx.showModal({
            content: '请在企业微信内打开',
            showCancel: false
          })
        resolve(false)
      }
    })

  },
  testqy2() {
    wx.qy.selectEnterpriseContact({
      fromDepartmentId: 0, // 必填，-1表示打开的通讯录从自己所在部门开始展示, 0表示从最上层开始
      mode: "multi", // 必填，选择模式，single表示单选，multi表示多选
      type: ["department", "user"], // 必填，选择限制类型，指定department、user中的一个或者多个
      success: function(res) {
        console.log(res)
        var selectedDepartmentList = res.result.departmentList; // 已选的部门列表
        for (var i = 0; i < selectedDepartmentList.length; i++) {
          var department = selectedDepartmentList[i];
          var departmentId = department.id; // 已选的单个部门ID
          var departemntName = department.name; // 已选的单个部门名称
        }
        var selectedUserList = res.result.userList; // 已选的成员列表
        for (var i = 0; i < selectedUserList.length; i++) {
          var user = selectedUserList[i];
          var userId = user.id; // 已选的单个成员ID
          var userName = user.name; // 已选的单个成员名称
          var userAvatar = user.avatar; // 已选的单个成员头像
        }
      }
    });
  },
  async syncExternal() {
    wx.vibrateShort()
    if (!await this.checkQy()) return
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/wechatWork/syncExternal', {
      sid: wx.getStorageSync('sid'),
      userId: this.data.userId
    }, res => {
      wx.hideLoading()
      if (res.success) {
        this.data.list[0].page = 1
        this.data.list[0].has_more = 0
        this.data.list[0].data = []
        this.data.list[2].page = 1
        this.data.list[2].has_more = 0
        this.data.list[2].data = []
        this.getList()
      }
      app.util.reqFail(res)
    })

  },
  async getExternal() {
    wx.vibrateShort()
    if (!await this.checkQy()) return
    wx.qy.selectExternalContact({
      filterType: 0, //0表示展示全部外部联系人列表，1表示仅展示未曾选择过的外部联系人。默认值为0；除了0与1，其他值非法。
      success: (res) => {
        console.log(res)
        var userIds = res.userIds.join(',') // 返回此次选择的外部联系人userId列表，数组类型
        // wx.showModal({
        //   content: userIds
        // })
        this.saveExternal(userIds)
      },
      fail: res => {
        if (res.errMsg != 'qy.selectExternalContact:fail cancel')
          wx.showModal({
            content: JSON.stringify(res),
            showCancel: false
          })
      }
    });
  },
  getCurExternalContact() {
    wx.qy.getCurExternalContact({
      success: function(res) {
        var userId = res.userId //返回当前外部联系人userId
      }
    })
  },
  saveExternal(ids) {
    wx.showLoading()
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/wechatWork/saveExternal', {
      sid: wx.getStorageSync('sid'),
      userIds: ids
    }, res => {
      wx.hideLoading()
      if (res.success) {
        // wx.showToast({
        //   title: '添加成功'
        // })
        this.data.list[this.data.currentTab].page = 1
        this.data.list[this.data.currentTab].has_more = 0
        this.data.list[this.data.currentTab].data = []
        this.getList()
      } else {
        app.util.reqFail(res)
      }
    })
  },
  async openOptions(e) {
    let id = e.currentTarget.dataset.id
    let card = e.currentTarget.dataset.cusid
    wx.vibrateShort()
    let list = ['删除']
    if (await this.checkQy(true) && id) {
      list.push('查看个人信息')
      list.push('创建会话')
    }
    wx.showActionSheet({
      itemList: list,
      success: res => {
        if (res.tapIndex == 0)
          this.deleteGuideCustomer(card)
        if (res.tapIndex == 1)
          this.openUserProfile(id)
        else if (res.tapIndex == 2)
          this.openEnterpriseChat(id)
        // case 2:
        //   this.sendChatMessage(id)
        // case 3:
        //   this.shareToExternalContact(id)
        // case 4:
        //   this.shareToExternalChat(id)

      },
    })
  },
  deleteGuideCustomer(cardid) {
    //导购助手-删除单个客户信息
    wx.showModal({
      title: '提示',
      content: '是否删除展示该客户',
      success: res => {
        if (res.confirm) {
          app.reqPost('ms-sanfu-wap-customer-distribution/assistant/guide/deleteGuideCustomer', {
            curCusId: cardid,
            sid: wx.getStorageSync('sid')
          }, res => {
            wx.hideLoading()
            if (res.success) {
              wx.showToast({
                title: '删除成功'
              })
              for(let i=0;i<3;i++){
                this.data.list[i].page = 1
                this.data.list[i].has_more = 0
                this.data.list[i].data = []
              }
              this.getList()
            } else {
              app.util.reqFail(res)
            }
          })
        }
      }
    })
  },
  openUserProfile(id) {
    //打开个人信息页
    wx.qy.openUserProfile({
      type: 2, //1表示该userid是企业成员，2表示该userid是外部联系人
      userid: id, //可以是企业成员，也可以是外部联系人
      success: function(res) {
        // 回调
      }
    });
  },
  openEnterpriseChat(id) {
    //创建会话
    wx.qy.openEnterpriseChat({
      // 注意：userIds和externalUserIds至少选填一个，且userIds+externalUserIds总数不能超过2000，如果externalUserIds有微信联系人，则总数不能超过40人。
      userIds: 'zhangsan;lisi;wangwu', //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
      externalUserIds: id, // 参与会话的外部联系人列表，格式为userId1;userId2;…，用分号隔开。
      groupName: '讨论组', // 必填，会话名称。单聊时该参数传入空字符串""即可。
      success: function(res) {
        // 回调
      },
      fail: function(res) {
        // 失败处理
      }
    });
  },
  sendChatMessage(id) {
    // 通过聊天工具栏向当前会话发送消息，支持多种消息格式，包括文本(“text”)，图片(“image”)，视频(“video”)，文件(“file”)、H5(“news”）和小程序(“miniprogram”)。
    wx.qy.sendChatMessage({
      msgtype: "text", //消息类型，必填
      text: {
        content: "你好", //文本内容
      },
      // image: {
      //   mediaid: "", //图片的素材id
      // },
      // video: {
      //   mediaid: "", //视频的素材id
      // },
      // file: {
      //   mediaid: "", //文件的素材id
      // },
      // news: {
      //   link: "", //H5消息页面url 必填
      //   title: "", //H5消息标题
      //   desc: "", //H5消息摘要
      //   imgUrl: "", //H5消息封面图片URL
      // },
      // miniprogram: {
      //   appid: "wx8bd80126147df384", //小程序的appid
      //   title: "this is title", //小程序消息的title
      //   imgUrl: "/appData/pic/pic1.jpg", //小程序消息的封面图
      //   page: "/index/page.html", //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
      // },
      success: function(res) {
        wx.showModal({
          content: 'success' + JSON.stringify(res)
        })
        //todo:
      },
      fail: res => {
        wx.showModal({
          content: 'fail' + JSON.stringify(res)
        })
      }
    });
  },
  shareToExternalContact() {
    // 客户群发 此接口支持企业成员把小程序，传递到群发助手进行发送。
    // 为了防止滥用，同一个成员每日向一个客户最多可群发一条消息，每次群发最多可选200个客户。
    wx.qy.shareToExternalContact({
      appid: "wx8bd80126147df384", //小程序的appid
      title: "this is title", //小程序消息的title
      imgUrl: "/appData/pic/pic1.jpg", //小程序消息的封面图
      page: "/index/page.html", //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
      success: function(res) {
        //todo:
      }
    });
  },
  shareToExternalChat() {
    // 客户群群发 此接口支持企业成员把小程序，传递到群发助手进行发送。
    // 为了防止滥用，同一个成员每日向一个客户群最多可群发一条消息，每次群发最多可选200个最近活跃的客户群。
    wx.qy.shareToExternalChat({
      appid: "wx8bd80126147df384", //小程序的appid
      title: "this is title", //小程序消息的title
      imgUrl: "/appData/pic/pic1.jpg", //小程序消息的封面图
      page: "/index/page.html", //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
      success: function(res) {
        //todo:
      }
    });
  },
  async getContext() {
    if (!await this.checkQy(true)) return
    this.setData({
      qyEntry: true //返回进入小程序的入口类型
    })
    // 值	说明
    // contact_profile	从联系人详情进入
    // single_chat_tools	从单聊会话的工具栏进入
    // group_chat_tools	从群聊会话的工具栏进入
    // chat_attachment	从聊天附件栏进入
    // normal	除以上场景之外进入，例如工作台，聊天会话等
    wx.qy.getContext({
      success: (res) => {
        this.setData({
          qyEntry: res.entry //返回进入小程序的入口类型
        })
      }
    })
  }

})
