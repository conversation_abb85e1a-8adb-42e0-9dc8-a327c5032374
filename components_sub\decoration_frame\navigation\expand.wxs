var startX = 0
var startY = 0
var lastLeft = 0
var moving = false
var currentLeft = 0

// 节流相关变量
var lastExecuteTime = Date.now()
var THROTTLE_DELAY = 16 // 约60fps的刷新率

// 节流函数
function throttle(fn) {
  var now = Date.now()
  if (now - lastExecuteTime >= THROTTLE_DELAY) {
    fn()
    lastExecuteTime = now
  }
}

// 更新元素样式
function updateElementStyle(expandWrap, left, state, withTransition) {
  if (!expandWrap) return

  var config = state.config
  var currentPage = state.currentPage
  var height = currentPage === 0 ? config.iH : state.maxHeight

  expandWrap.setStyle({
    transform: 'translateX(' + left + 'rpx)',
    height: height + 'rpx',
    transition: withTransition ? 'all 0.3s ease-out' : 'none'
  })
}

function touchstart(event, ins) {
  var expandWrap = ins.selectComponent('.expand-wrap')
  var state = expandWrap.getState()
  var touch = event.touches[0]
  startX = touch.pageX
  startY = touch.pageY
  moving = true
  currentLeft = lastLeft

  state.startTime = Date.now()
}

function touchmove(event, ins) {
  if (!moving) return
  var expandWrap = ins.selectComponent('.expand-wrap')
  var state = expandWrap.getState()
  var touch = event.touches[0]
  var moveX = touch.pageX - startX
  var moveY = touch.pageY - startY
  var moveXInRpx = moveX * 2

  if (Math.abs(moveX) > Math.abs(moveY)) {
    var left = currentLeft + moveXInRpx
    if (left > 0) left = 0
    if (left < state.maxTranslateX) left = state.maxTranslateX

    var progress = Math.abs(left / state.maxTranslateX)
    var heightDiff = state.maxHeight - state.config.iH
    var currentHeight = state.config.iH + (heightDiff * progress)

    // 使用节流控制更新频率
    throttle(function() {
      state.currentHeight = currentHeight
      expandWrap.setStyle({
        transform: 'translateX(' + left + 'rpx)',
        height: currentHeight + 'rpx',
        transition: 'none'
      })
    })

    return false
  }
  return true
}

function touchend(event, ins) {
  if (!moving) return
  moving = false

  // 重置最后执行时间
  lastExecuteTime = Date.now()

  var expandWrap = ins.selectComponent('.expand-wrap')
  var state = expandWrap.getState()
  var touch = event.changedTouches[0]
  var moveX = (touch.pageX - startX) * 2
  var duration = Date.now() - state.startTime

  var isQuickSlide = duration < 300 && Math.abs(moveX) > 60
  var threshold = Math.abs(state.maxTranslateX) / 3

  if (isQuickSlide || Math.abs(moveX) > threshold) {
    if (moveX < 0) {
      lastLeft = state.maxTranslateX
      state.currentPage = 1
    } else {
      lastLeft = 0
      state.currentPage = 0
    }
  } else {
    lastLeft = state.currentPage === 0 ? 0 : state.maxTranslateX
  }

  currentLeft = lastLeft
  updateElementStyle(expandWrap, lastLeft, state, true)

  ins.callMethod('handlePageChange', {
    currentPage: state.currentPage
  })
}

function configInit(newVal, oldVal, ownerInstance, ins) {
  var state = ins.getState()
  state.config = newVal
  var dataLength = state.dataLength
  if (dataLength) {
    state.maxHeight = newVal.iH * Math.ceil(dataLength / newVal.itemsPerRow - 1) + Math.ceil(dataLength / newVal.itemsPerRow - 2) * newVal.rowGap
  }
  state.maxTranslateX = -(750 - 2 * newVal.pLR - 2 * newVal.mLR - (newVal.showHalfFirst ? newVal.iW / 2 : 0))
}

function dataLengthInit(newVal, oldVal, ownerInstance, ins) {
  var state = ins.getState()
  state.dataLength = newVal
  var config = state.config
  if (config) {
    state.maxHeight = config.iH * Math.ceil(newVal / config.itemsPerRow - 1) + Math.ceil(newVal / config.itemsPerRow - 2) * config.rowGap
  }
}

module.exports = {
  touchstart: touchstart,
  touchmove: touchmove,
  touchend: touchend,
  configInit: configInit,
  dataLengthInit: dataLengthInit
}
