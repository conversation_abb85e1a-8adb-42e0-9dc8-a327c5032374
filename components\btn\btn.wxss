@import "/font/icon_f7.wxss";
.fastNavOut {
  position: fixed;
  right: 0;
  top: 800rpx;
  z-index: 9;
}

.fastNav {
  position: relative;
  background: rgba(254, 85, 150, 0.8);
  font-size: 24rpx;
  padding: 18rpx 14rpx 18rpx 14rpx;
  width: 56rpx;
  color: rgba(255, 255, 255, 0);
  border-bottom-left-radius: 10rpx;
  border-top-left-radius: 10rpx;
  text-align: center;
  line-height: 05rpx;
}

.closeIcon {
  position: absolute;
  top: -10rpx;
  right: 60rpx;
  font-size: 45rpx;
  color: rgba(116, 116, 116, 0.7);
  font-weight: 100;
}

.foldNav {
  /* float: left; */
  background: rgba(254, 85, 150, 0.8);
  font-size: 24rpx;
  padding: 26rpx 14rpx;
  width: 120rpx;
  color: rgb(255, 255, 255);
  border-bottom-left-radius: 10rpx;
  border-top-left-radius: 10rpx;
  display: inline-block;
  margin-top: 50rpx;
  text-align: center;
}

.fastNavImg {
  float: left;
  padding-top: 20rpx;
  padding-right: 08rpx;
  width: 30rpx;
}

.foldImg {
  float: left;
  padding-top: 04rpx;
  padding-right: 08rpx;
  width: 30rpx;
}

.outBtn {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.05);
  position: fixed;
  z-index: 999;
  left: 0px;
  bottom: 0;
}

.iconTo {
  text-align: center;
  display: inline-block;
  color: #747474;
  width: 32%;
  padding: 10rpx 10rpx 15rpx 10rpx;
}

.k_menu-item,
.k_menu-open-button {
  background: #FFF;
  /*Color*/
  border-radius: 100%;
  width: 50px;
  height: 50px;
  margin-left: -40px;
  position: absolute;
  /*  top: 100px; */
  color: #ccc;
  text-align: center;
  /*  line-height: 70px; */
  /*  box-shadow: inset 0 0 0 5px #fff;/*Color*/ 
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: -webkit-transform ease-out 200ms;
  transition: -webkit-transform ease-out 200ms;
  transition: transform ease-out 200ms;
  transition: transform ease-out 200ms, -webkit-transform ease-out 200ms;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.k_menu-open_3,
.k_menu-open_2,
.k_menu-open_1,
.k_menu-open_4,
.k_menu-open_5,
.k_menu-open_6 {
  display: none;
}

/*Here you can change your button color
Some colors: indigo:3F51B5;teal:009688;red:F44336;orange:FF9800;pink:E91E63;green:4CAF50;green-light:8BC34A;amber:FFC107;
*/
/* .k_menu-item:hover { */
  /*  background: #F44336;/*Color*/ 
  /*  color: #F44336;/*Color*/ 
  /*  box-shadow: inset 0 0 0 5px #ffffff; */
/* } */

.k_menu-open-button {
  z-index: 2;
  -webkit-transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  -webkit-transition-duration: 400ms;
  transition-duration: 400ms;
  -webkit-transition-timing-function: linear;
  transition-timing-function: linear;
  -webkit-transition-duration: 200ms;
  transition-duration: 200ms;
  /*  -webkit-transform: scale(1.1, 1.1) translate3d(0, 0, 0); */
  /*          transform: scale(1.1, 1.1) translate3d(0, 0, 0); */
  /* cursor: pointer; */
}




.k_menu_material-icons.md-30 {
  font-size: 30px;
  color: #FFFFFF;
  -webkit-transform: translate3d(0, 8px, 0);
  transform: translate3d(0, 8px, 0);
}

/*2: Circular 6 more buttons*/
.k_menu_2 {
  position: absolute;
  left: 40%;
  /*  margin-left: -190px; */
  padding-top: 20px;
  /*  padding-left: 190px; */
  /*  width: 380px; */
  /*  height: 250px; */
  box-sizing: border-box;
  font-size: 20px;
  text-align: left;
}

.k_menu-one {
  
  transition-timing-function:  ease;
  -webkit-transition-duration: 200ms;
  transition-duration: 200ms;
  -webkit-transform: translate3d(0px, -100px, 0);
  transform: translate3d(0px, -100px, 0);
}

.k_menu-two {
  transition-timing-function:  ease;
  -webkit-transition-duration: 200ms;
  transition-duration: 200ms;
  -webkit-transform: translate3d(-60px, -80px, 0);
  transform: translate3d(-60px, -80px, 0);
}

.k_menu-three {
  transition-timing-function:  ease;
  -webkit-transition-duration: 200ms;
  transition-duration: 200ms;
  -webkit-transform: translate3d(-60px, 80px, 0);
  transform: translate3d(-60px, 80px, 0);
}

.k_menu-four {
  transition-timing-function:  ease;
  -webkit-transition-duration: 200ms;
  transition-duration: 200ms;
  -webkit-transform: translate3d(0px, 100px, 0);
  transform: translate3d(0px, 100px, 0);
}

.k_menu-five {
  transition-timing-function:  ease;
  -webkit-transition-duration: 200ms;
  transition-duration: 200ms;
  -webkit-transform: translate3d(-104px, 30px, 0);
  transform: translate3d(-104px, 30px, 0);
}

.k_menu-six {
  transition-timing-function:  ease;
  -webkit-transition-duration: 200ms;
  transition-duration: 200ms;
  -webkit-transform: translate3d(-104px, -30px, 0);
  transform: translate3d(-104px, -30px, 0);
}

.k_menu-open_2:checked+.k_menu-open-button .k_menu_material-icons.md-48 {
  -webkit-animation: k_menu-move2 200ms linear 1;
  animation: k_menu-move2 200ms linear 1;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  font-size: 40px;
}

.yd-grids-3 {
  position: initial;
  border-radius: 1px 0px 0px 1px;
}

.yd-grids-item {
  float: left;
  position: initial;
  padding: 10rpx 0;
  font-size: 18rpx;
}

.yd-grids-txt {
  color: #747474;
}

.yd-grids-icon {
  height: 60rpx;
}

.close{
  position: absolute;
  left: 100rpx;
}