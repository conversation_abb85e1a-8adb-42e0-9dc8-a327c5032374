// pages/distributershop/groupshare/groupshare.js
import canvasmethod from '../../../utils/shareCanvas.js'
const app = getApp()

Page({
  ...canvasmethod,

  data: {
    shareType: '', //single group
    showLoading: true,
    recommendId: '',
    contentImgarr: [], //文章图片
    choose_contentImg_index_arr: [], //已选择文章图片
    goodsImgarr: [], //默认商品图片
    goodsDelayLoadIndex: 0, //商品加载图片延时
    choose_goodsImg_index_arr: [], //已选择  默认商品图片
    canvasTypeName: ['默认商品图片', '自定义', ],
    canvasType: 0, // 0: 默认商品图片  1：文章图片  2:自定义   3:不显示
    customCanvas: false
  },

  onLoad: async function(options) {
    this.data.timer = '' //定义计时器
    this.data.recommendId = options.id
    //组合商品
    if (options.type == 'group') {
      this.getGroupDetails(this.data.recommendId)
      this.setData({
        shareType: 'group'
      })
    } else if (options.type == 'single') {
      //单品
      this.data.shareobj = wx.getStorageSync('singleshare')
      wx.removeStorageSync('singleshare')
      // this.setData({
      //   canvasTypeName: ['默认商品图片', '自定义', ],
      // })
      // 获取秒杀价格
      let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/good/secKillPrice', {
        gooId: this.data.shareobj.gooId,
        shoId: app.local.get('sho_id')
      }, res => {})
      if (res.success) {
        if (res.data) {
          this.data.shareobj.seckillprice = res.data || ''
        }
      }
      this.getGoodsImg({
        gooIds: this.data.shareobj.gooId
      })
    }
  },

  onShow: function() {},
  onUnload: function() {
    clearInterval(this.data.timer)
  },
  //多选改动
  checkChange: function(e) {
    let type = e.currentTarget.dataset.type
    for (let i in this.data[type]) {
      this.data[type][i].bool = false
    }
    for (let i in e.detail.value) {
      let key = parseInt(e.detail.value[i])
      this.data[type][key].bool = true
    }
    this.setData({
      [type]: this.data[type]
    })
  },
  //自定义选择
  customSelect: function(e) {
    if (!this.data.customCanvas) {
      return
    }
    let type = e.currentTarget.dataset.type
    let index = e.currentTarget.dataset.index
    let t = true // true 新增  false 不新增
    for (let i in this.data.selectarr) {
      if (this.data.selectarr[i].type == type && this.data.selectarr[i].index == index) {
        t = false
        this.data.selectarr.splice(i, 1);
        //排序显示处理(去除)
        let arrname = `choose_${type.split('arr')[0]}_index_arr`
        this.data[arrname][index].no = ''
        break;
      }
    }
    if (t) {
      this.data.selectarr.push({
        type: type,
        index: index
      })
      //排序显示处理(新增)
      let arrname = `choose_${type.split('arr')[0]}_index_arr`
      this.data[arrname][index].no = this.data.selectarr.length
    } else { //排序显示处理(重新排列)
      for (let i in this.data.selectarr) {
        let arrname = `choose_${this.data.selectarr[i].type.split('arr')[0]}_index_arr`
        console.log(arrname)
        this.data[arrname][this.data.selectarr[i].index].no = parseInt(i) + 1
      }
    }
    this.setData({
      choose_contentImg_index_arr: this.data.choose_contentImg_index_arr,
      choose_goodsImg_index_arr: this.data.choose_goodsImg_index_arr
    })
  },
  /** 获取商品明细 */
  getGroupDetails: async function(id) {

    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/goodsh/detail', {
      sid: wx.getStorageSync('sid'),
      id: id
    })

    if (res.success) {
      this.data.shareobj = {
        title1: res.data.recommendTitle || '',
        title2: res.data.subtitle || '',
        recommendId: this.data.recommendId,
      }
      let imglist = []
      //分享展示文章图片
      for (let i in res.data.tempPicList) {
        for (let j in res.data.tempPicList[i].imgList) {
          if (!res.data.tempPicList[i].imgList[j].gooId) {
            this.data.choose_contentImg_index_arr.push({
              bool: false
            })
            imglist.push(res.data.tempPicList[i].imgList[j].originalUrl)
          }
        }
      }
      this.data.shareobj.imgList = imglist
      this.setData({
        contentImgarr: imglist,
        choose_contentImg_index_arr: this.data.choose_contentImg_index_arr
      })
      this.getGoodsImg({
        recommendId: this.data.recommendId
      })
    } else {
      wx.showToast({
        title: res.msg,
        icon: 'none'
      })
    }

  },
  /** 获取商品列表 */
  getGoodsImg: async function(data) {
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/goodsh/allGoodsBaseImg', data)

    let toasttitle
    if (res.success != true) {
      toasttitle = res.msg
    } else if (!res.data) {
      toasttitle = '暂无图片，请重试id=' + this.data.recommendId
    }
    if (toasttitle) {
      wx.showToast({
        title: toasttitle,
        icon: 'none',
        duration: 3000
      })
      return;
    }
    let imgarr = [];
    let canvasarr = []
    let lastid
    for (let i in res.data) {
      imgarr.push(res.data[i].baseImgUrl)
      this.data.choose_goodsImg_index_arr.push({
        bool: false
      })
      if (lastid != res.data[i].goodsSn) {
        canvasarr.push(res.data[i].baseImgUrl)
        lastid = res.data[i].goodsSn
      }
    }
    this.setData({
      goodsImgarr: imgarr,
      choose_goodsImg_index_arr: this.data.choose_goodsImg_index_arr
    })
    wx.nextTick(() => {
      this.data.timer = setInterval(() => {
        if (this.data.goodsDelayLoadIndex <= imgarr.length) {
          let index = this.data.goodsDelayLoadIndex + 10
          if (index > imgarr.length)
            index = imgarr.length
          this.setData({
            goodsDelayLoadIndex: index
          })
        } else {
          clearInterval(this.data.timer)
        }
      }, 300)
    })
    this.data.canvasarr = canvasarr
    if (data.gooIds)
      this.data.shareobj.imgList = canvasarr
    this.createShare()
  },
  //画布图片控制
  canvasChange: function(e) {
    console.log(e.detail.value)
    this.setData({
      canvasType: e.detail.value,
      tmppath: ''
    })
    this.createShare();
  },
  // ***创建分享画布***
  createShare: function() {
    if (!this.data.shareobj) {
      wx.showToast({
        title: '生成失败',
        icon: 'none',
        duration3000
      })
      return
    }
    //控制分享图图片展示  0: 默认商品图片   1：文章图片 2:自定义  3:不显示
    if (this.data.canvasTypeName[this.data.canvasType] == '默认商品图片') {
      this.data.shareobj.imgList = this.data.canvasarr
      this.sharecanvas(this.data.shareobj)
    } else if (this.data.canvasTypeName[this.data.canvasType] == '文章图片') {
      this.data.shareobj.imgList = this.data.contentImgarr
      this.sharecanvas(this.data.shareobj)
    } else if (this.data.canvasTypeName[this.data.canvasType] == '关') {
      this.setData({
        tmppath: ''
      })
    } else if (this.data.canvasTypeName[this.data.canvasType] == '自定义') {
      this.data.choose_contentImg_index_arr_old = JSON.stringify(this.data.choose_contentImg_index_arr)
      this.data.choose_goodsImg_index_arr_old = JSON.stringify(this.data.choose_goodsImg_index_arr)
      for (let i in this.data.choose_contentImg_index_arr)
        this.data.choose_contentImg_index_arr[i].bool = false
      for (let i in this.data.choose_goodsImg_index_arr)
        this.data.choose_goodsImg_index_arr[i].bool = false
      this.data.selectarr = []
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      })
      this.setData({
        tmppath: '',
        customCanvas: true,
        choose_contentImg_index_arr: this.data.choose_contentImg_index_arr,
        choose_goodsImg_index_arr: this.data.choose_goodsImg_index_arr,
      })
      if (!wx.getStorageSync('group_share_confirm_dontshow'))
        wx.showModal({
          title: '提示',
          content: '①按勾选的前3张图片顺序展示\r\n②勾选结束点击完成退出',
          //'自定义生成最多展示3张图片，按选择的顺序展示，选择后点击底部按钮完成（退出）',
          confirmText: '不再提示',
          showCancel: true,
          cancelText: '我知道了',
          confirmColor: '#000',
          cancelColor: '#3cc51f',
          success: res => {
            if (res.confirm) {
              wx.setStorageSync('group_share_confirm_dontshow', true)
            }
          }
        })
    }
  },
  //素材保存控制  //生成图片控制
  controlSave: async function() {
    /*  生成分享图  */
    if (this.data.customCanvas) {
      if (this.data.selectarr.length == 0) {
        for (let i in this.data.canvasTypeName) {
          if (this.data.canvasTypeName[i] == '默认商品图片')
            this.data.canvasType = i
        }
        this.createShare()
      } else {
        let imgarr = []
        for (let i in this.data.selectarr) {
          imgarr.push(this.data[this.data.selectarr[i].type][this.data.selectarr[i].index])
        }
        this.data.shareobj.imgList = imgarr
        this.sharecanvas(this.data.shareobj)
      }
      this.setData({
        canvasType: this.data.canvasType,
        customCanvas: false,
        choose_goodsImg_index_arr: JSON.parse(this.data.choose_goodsImg_index_arr_old),
        choose_contentImg_index_arr: JSON.parse(this.data.choose_contentImg_index_arr_old)
      })
      wx.pageScrollTo({
        scrollTop: 9999,
        duration: 0
      })

      return
    }
    /* 保存组合素材 */
    let sharegoodsImgarr = []
    //添加二维码分享图
    if (this.data.tmppath && !this.data.showLoading)
      sharegoodsImgarr.push(this.data.tmppath)
    //添加商品图
    for (let i in this.data.choose_goodsImg_index_arr) {
      if (this.data.choose_goodsImg_index_arr[i].bool)
        sharegoodsImgarr.push(this.data.goodsImgarr[i])
    }
    //添加文章图
    for (let i in this.data.choose_contentImg_index_arr) {
      if (this.data.choose_contentImg_index_arr[i].bool)
        sharegoodsImgarr.push(this.data.contentImgarr[i])
    }
    if (sharegoodsImgarr.length == 0) {
      wx.showToast({
        icon: 'none',
        title: '请选择要保存的图片',
        duration: 2000,
      })
      return
    }
    wx.showToast({
      icon: 'none',
      title: '正在保存图片至相册...',
      duration: 100000,
    })
    for (let i in sharegoodsImgarr) {
      wx.showToast({
        icon: 'none',
        title: `正在保存图片至相册(${parseInt(i)+1}/${sharegoodsImgarr.length})`,
        duration: 100000,
      })
      let img = sharegoodsImgarr[i]
      if (img.indexOf('https://') != -1) {
        img = await this.loadImg(sharegoodsImgarr[i])
        img = img.path
      }
      let status = await this.saveImage(img)
      if (status != true) return;
    }
    wx.hideToast();
    wx.showToast({
      icon: 'success',
      title: '保存成功，快去分享吧',
      duration: 2000
    })
  },
  saveImage: function(path) {
    return new Promise(resolve => {
      wx.saveImageToPhotosAlbum({
        filePath: path,
        success: function(data) {
          resolve(true)
        },
        fail(err) {
          resolve(err)
          wx.hideLoading()
          wx.hideToast()
          if (err.errMsg === "saveImageToPhotosAlbum:fail:auth denied" || err.errMsg === "saveImageToPhotosAlbum:fail auth deny"|| err.errMsg === "saveImageToPhotosAlbum:fail authorize no response") {
            // 这边微信做过调整，必须要在按钮中触发，因此需要在弹框回调中进行调用
            wx.showModal({
              title: '提示',
              content: '需要您授权保存相册',
              success: res => {
                if (res.confirm) {
                  wx.openSetting({
                    success(settingdata) {
                      console.log("settingdata", settingdata)
                      if (settingdata.authSetting['scope.writePhotosAlbum']) {
                        wx.showToast({
                          title: '获取权限成功,再次点击图片即可保存',
                          icon: 'none',
                          duration: 3000
                        })
                      } else {
                        wx.showToast({
                          title: '获取权限失败，将无法保存到相册哦~',
                          icon: 'none',
                          duration: 3000

                        })
                      }
                    },
                  })
                }
              }
            })
          } else if (err.errMsg == "saveImageToPhotosAlbum:fail cancel") {
            wx.showToast({
              title: '已取消保存~',
              icon: 'none',
              duration: 2000
            })
          } else {
            wx.showToast({
              title: '保存失败：' + err.errMsg,
              icon: 'none',
              duration: 5000
            })
          }
        }
      })
    })
  },
  preview: function() {
    if (this.data.tmppath)
      wx.previewImage({
        current: this.data.tmppath, // 当前显示图片的http链接
        urls: [this.data.tmppath] // 需要预览的图片http链接列表
      })
  }
})
