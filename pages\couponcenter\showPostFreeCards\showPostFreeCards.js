// pages/couponcenter/showPostFreeCards/showPostFreeCards.js
const app = getApp()
Page({
  data: {
    list: [],
    recList: [],
    canget: 0
  },
  onReady: async function() {
    await app.waitSid()
    this.getPostFreeCards()
  },
  getPostFreeCards: async function() {
    const res = await app.reqGet('ms-sanfu-wap-customer/showPostFreeCards', {
      sid: wx.getStorageSync('sid')
    })
    if (res.success) {
      this.setData({
        list: res.data.postFreeCardsAvailable,
        recList: res.data.postFreeCardsReceive,
        canget: res.data.totalCanGet
      })
    } else {
      app.util.reqFail(res)
    }
  },
  getCoupon: async function(e) {
    wx.showModal({
      title: '提醒',
      content: '确认领取吗？',
      success: async r => {
        if (r.confirm) {
          const id = e.currentTarget.dataset.id
          const res = await app.reqGet('ms-sanfu-wap-customer/sendPostFree', {
            sid: wx.getStorageSync('sid'),
            freeId: id
          })
          if (res.success) {
            wx.showToast({
              title: '领取成功~',
              icon: 'none'
            })
            app.subscribeMsg(2004, res.data.freeCode)
            this.getPostFreeCards()
          } else {
            app.util.reqFail(res)
          }
        }
      }
    })
  }
})
