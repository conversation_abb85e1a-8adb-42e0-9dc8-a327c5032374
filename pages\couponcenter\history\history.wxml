<!--pages/couponcenter/history/history.wxml-->
<view class="swiper-tab">
  <view class="swiper-tab-list {{currentTab==0 ? 'on' : ''}}" data-current="0" bindtap="swichNav">已使用</view>
  <view class="swiper-tab-list {{currentTab==1 ? 'on' : ''}}" data-current="1" bindtap="swichNav">已过期</view>
</view>
<view style="flex: 1;position: relative;width: 100%;">
  <swiper current="{{currentTab}}" class="swiper-box" duration="300" bindchange="bindChange">
    <swiper-item wx:for="{{tabs}}" wx:key="index">
      <scroll-view scroll-y bindscrolltolower="dealList" style="height: 100%;">
        <view class='coupon-list'>
          <block wx:for="{{item.data}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1">
            <coupon item="{{item1}}" isHis="{{true}}" showUse="{{index==0}}" showCode bind:useDetail="showUse"> </coupon>
          </block>
        </view>
        <view wx:if="{{item.has_more != 4}}" class="show_end" bindtap="dealList">
          <view wx:if="{{item.has_more == 1}}" class="loadmore-icon"></view>
          <text>{{['点击加载更多', '正在加载...', '没有更多了','暂无数据'][item.has_more==2&&item.data&&item.data.length==0?3:item.has_more] }}</text>
        </view>
      </scroll-view>
    </swiper-item>
  </swiper>
</view>
<uni-popup type="center" show="{{useDetail}}" closeName="优惠券历史记录-优惠券使用详情弹框" zIndex="999" bind:close="showUseClose">
  <view class="uni-icon uni-icon-close" style="text-align: right;color: #ddd;font-size: 80rpx;margin-bottom: -30rpx;margin-right: -36rpx;" bindtap="showUseClose"></view>
  <view class="popup">
    <view>优惠券使用详情</view>
    <text>订单编号：<text style="color:blue" bindtap="toDetail">{{useDetail.ordId}}</text></text>
    <text wx:if="{{!limitShop}}">出货类型：{{useDetail.sendTypeName}}</text>
    <text wx:if="{{!limitShop}}">订单门店：{{useDetail.shoName}}</text>
    <text>创建时间：{{useDetail.createTime}}</text>
    <text wx:if="{{useDetail.payTime}}">付款时间：{{useDetail.payTime||''}}</text>
    <text>订单状态：{{useDetail.inStatusName}}</text>
    <text>用券规则：\n<text style="color: #888;">{{useDetail.memo}}</text></text>
    <button wx:if="{{useDetail.inOrderStatusId==1}}" bindtap="cancleOrder" class="cancel-btn">取消订单</button>
  </view>
</uni-popup>
