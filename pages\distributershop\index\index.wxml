<!--pages/distributershop/index/index.wxml-->
<block wx:if="{{1}}">
  <view class="dis-nav">
    <view class="nav-item">
      <view class="dis-card">卡号：<text style="font-weight: 700;font-size: 34rpx;">{{cardid}}</text></view>
      <view class="dis-shop">
        <view class="info">当前分销：<text style="font-weight: 700;font-size: 34rpx;">{{disshop||'暂无'}}</text></view>
        <view class="change" bindtap="disshop_change">更改</view>
        <view wx:if="{{show_reset}}" bindtap="disshop_reset" class="change" style="background: #fd6c90;">还原</view>
      </view>
    </view>
    <view class="nav-item">
      <view class="dis-card">
        <view>
          <view>当前区域：<text style="font-weight: 700;font-size: 34rpx;">{{orgId==102?'百货':'服饰'}}{{dsho_id}}</text>
          </view>

        </view>
        <view class="dis-shop" style="margin-left: 30rpx;">
          <view class="change" bindtap="shop_change" style="background: #ff80b7;">刷新区域</view>
        </view>
      </view>
      <view wx:if="{{belongShop}}" class="dis-card" style="flex: unset;flex-direction: column;align-items: flex-end;">
        <view>归属店：{{belongShop}}</view>
        <view wx:if="{{belongOrgId==101||belongOrgId==102}}">归属公司：{{belongOrgId==101?'服饰':'百货'}}</view>
      </view>
    </view>
  </view>
  <view class="score" wx:if="{{scoreList.length>0}}">
    <view class="score-box" wx:for="{{scoreList}}" wx:key="index">
      <view class="score-item" wx:for="{{item}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2">
        <view class="score-title">{{item2.title}}<text wx:if="{{index2==0}}" class="uni-icon uni-icon-info" catchtap="showtip" data-i="{{index}}"></text></view>
        <view class="score-value">{{item2.value}}</view>
      </view>
    </view>
    <button class="score-more" bindtap="toSelect" hover-class="hover" hover-start-time="20" hover-stay-time="100">查看更多业绩<text class="uni-icon uni-icon-arrowright"></text></button>
  </view>

  <view class="menu" wx:for="{{menu}}" wx:key="index">
    <view class="menu-tab">{{item.tab}}</view>
    <view class="menu-box">
      <view class="menu-item" wx:for="{{item.data}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2" bindtap="{{item2.tap}}">
        <image wx:if="{{item2.img}}" class="menu-img" src="{{item2.img}}"></image>
        <view wx:if="{{item2.icon}}" class="f7 {{item2.icon}}"></view>
        <view class="menu-title">{{item2.name}}</view>
        <text class="has-new" wx:if="{{newFlags[item2.flag]}}">new</text>
      </view>
    </view>
  </view>
</block>

<block wx:if="{{0}}">
  <view class="showdisshop"><text> 当前分销店号：{{disshop||'暂无'}}</text>
    <view bindtap="disshop_change">更改门店</view>
    <view wx:if="{{show_reset}}" bindtap="disshop_reset" style="color: #fd6c90;border: 2rpx solid #fd6c90;">还原</view>
  </view>
  <view class="dis-top">
    <view bindtap="toMyShop">我的店铺</view>
    <!-- 如果只是展示用户头像昵称，可以使用 <open-data /> 组件
    <open-data type="userAvatarUrl"></open-data>
    <open-data type="userNickName"></open-data> -->
    <button wx:if="{{1}}" open-type="getUserInfo" bindgetuserinfo="bindGetUserInfo">分享店铺</button>
    <view wx:else bindtap="share">分享店铺</view>
    <view style="margin-left: 20rpx !important;" bindtap="toSns">三福时尚笔记</view>
  </view>
  <view class="dis-block">
    <view class="item">
      <view class="item-content" style="background:url( https://img.sanfu.com/sf_access/uploads/t8JslenuRWK66UDrbomfzGYdcIqEqfp6.png) center center / 100% no-repeat" bindtap="toSingle">
        <text wx:if="{{newFlags.good === 1}}" class="has-new">new</text> 单品推荐
      </view>
    </view>
    <view class="item">
      <view class="item-content" style="background:url( https://img.sanfu.com/sf_access/uploads/aVSAlpLYOirY2TUUSNZvpJOW11RVgzrF.png) center center / 100% no-repeat" bindtap="toGroup">
        <text wx:if="{{newFlags.goodSh === 1}}" class="has-new">new</text> 组合商品推荐
      </view>
    </view>
    <view class="item">
      <view class="item-content" style="background:url( https://img.sanfu.com/sf_access/uploads/PMr0wIV0yjhQymwwx9hs8BSgPudVsT9b.png) center center / 100% no-repeat" bindtap="toActivity">
        <text wx:if="{{newFlags.act === 1}}" class="has-new">new</text> 活动推荐
      </view>
    </view>
    <view class="item">
      <view class="item-content" style="background:url( https://img.sanfu.com/sf_access/uploads/ykXve95JpfCAFzNQrtcrwzn2xDMny5gz.png) center center / 100% no-repeat" bindtap="toClass">
        <text wx:if="{{newFlags.microClass === 1}}" class="has-new">new</text> 分销微课堂
      </view>
    </view>
    <view class="item">
      <view class="item-content" style="background:url( https://img.sanfu.com/sf_access/uploads/D78W6lZa9zKlIL7jZK3ljGTLbGN3QKaC.png) center center / 100% no-repeat" bindtap="toSelect">
        业绩查询
      </view>
    </view>
    <view class="item">
      <view class="item-content" style="background:url( https://img.sanfu.com/sf_access/uploads/EEuDZ0GP0yxRuQieP2h1vDDnONlICuUe.jpg) center center / 100% no-repeat" bindtap="toNetShop">
        网店未上架商品
      </view>
    </view>
    <!-- <view class="item">
      <view class="item-content" style="width:{{width}}px;height:{{width}}px"bindtap="toInvite">
        会员邀请
      </view>
    </view> -->
  </view>
  <view style="height: 100rpx;" bindtap="toServices"></view>
</block>
<canvas type="2d" id="shareCanvas1" class="shareCanvas" style="width:520rpx;height:720rpx;position:fixed;left:-99999px;top:-999999px"></canvas>
<view hidden="{{showqrcode}}" class="qrcode" bindlongpress='saveInviteCard'>
  <!-- <image wx:if="{{tmppath.length > 0}}" style="display:block;width:{{windowWidth*0.8}}px;height:{{windowHeight*0.7}}px" class="shareCanvas" src="{{tmppath}}"></image> -->
  <image src="{{tmppath}}" style="width:520rpx;height:650rpx;border-radius: 20rpx;"></image>
  <view class="dis-top">
    <view style="background:white;width:10%;flex:none;" bindtap="refresh">重置</view>
    <view style="background:white;width:40%;flex:none;" bindtap="closeQR">关闭</view>
    <view style="background:white;width:40%;flex:none;" bindtap="saveImage">保存</view>
  </view>
</view>
<!-- 分享组件 -->
<share id="sanfu-share" />
<sanfu-alert id="sanfu-alert1" contentStyles="text-align:left;max-height:40vh;overflow-y:scroll;">
</sanfu-alert>

<sanfu-alert id="sanfu-alert2">
  <textarea class="textarea" fixed="{{true}}" bindinput="bindInput" data-key="goodids" placeholder-style="font-size:26rpx;color: #999;" placeholder="请输入色码，多个用逗号隔开，限上架商品(暂不支持货号,仅支持8位色码)" maxlength="300"></textarea>
</sanfu-alert>
