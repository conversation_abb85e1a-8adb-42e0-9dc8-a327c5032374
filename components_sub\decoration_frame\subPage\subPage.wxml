<import src="/pages/index/index.skeleton.wxml" />
<view id="top"></view>
<block wx:if="{{currentIndex>=0&&!list[currentIndex]}}">
  <view wx:if="{{isTop}}" style="height: calc({{menuButton.t}}px + 206rpx);background: #fff;"></view>
  <template is="skeleton" data="{{hideTab}}" />
</block>
<block wx:if="{{currentIndex>=0}}" wx:for="{{list[currentIndex]}}" wx:key="unitId">
  <album wx:if="{{item.unitKey=='album'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" />
  <cube wx:elif="{{item.unitKey=='cube'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" />
  <singleRowImage wx:elif="{{item.unitKey=='singleRowImage'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" pid="{{pid}}" bind:alert="alert" shopInfo="{{shopInfo}}" index="{{index}}" />
  <blank wx:elif="{{item.unitKey=='blank'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" pid="{{pid}}" index="{{index}}" />
  <hotArea wx:elif="{{item.unitKey=='hotArea'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" pid="{{pid}}" index="{{index}}" />
  <popup wx:elif="{{item.unitKey=='popup'&&!item.hide&&!hidePop}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" />
  <swipe wx:elif="{{item.unitKey=='swipe'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" menuButton="{{menuButton}}" index="{{index}}" />
  <navigation wx:elif="{{item.unitKey=='navigation'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" />
  <goods wx:elif="{{item.unitKey=='goods'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" />
  <mulGoods wx:elif="{{item.unitKey=='multGoods'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" menuButton="{{menuButton}}" id="mulGoods{{index}}" index="{{index}}" bindscrollTo="scrollTo" />
  <dynamicGoods wx:elif="{{item.unitKey=='dynamicGoods'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" />
  <hotSwipe wx:elif="{{item.unitKey=='hotSwipe'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" />
  <notice wx:elif="{{item.unitKey=='notice'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" />
  <floatWindow wx:elif="{{item.unitKey=='floatWindow'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" />
  <columnPaginationTab wx:elif="{{item.unitKey=='columnPagination'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" menuButton="{{menuButton}}" />
  <columnPagination wx:elif="{{item.unitKey=='subPage'}}" list="{{item.list}}" shopInfo="{{shopInfo}}" pid="{{pid}}" index="{{index}}" menuButton="{{menuButton}}" />
  <topPlaceholder wx:elif="{{item.unitKey=='topPlaceholder'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" pid="{{id}}" index="{{index}}" menuButton="{{menuButton}}" />
  <backButton wx:elif="{{item.unitKey=='backButton'&&!item.hide}}" config="{{item.unitStyle}}" unitData="{{item.unitContent}}" pid="{{id}}" index="{{index}}" />
</block>
