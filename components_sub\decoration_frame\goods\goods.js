const app = getApp()
Component({
  options: {
    // addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    config: {
      type: Object,
      value: {}
    },
    unitData: {
      type: Object,
      value: {}
    },
    unitName: {
      type: null,
      value: ''
    },
    pid: {
      type: null,
      value: ''
    },
    shopInfo: {
      type: null,
      value: ''
    },
    styles: {
      type: null,
      value: ''
    },
    index: {
      type: null,
      value: ''
    }
  },
  data: {

  },

  attached() {},
  ready() {
    const observer = this.selectComponent('#observer')
    if (observer && observer.startObserver) {
      observer.startObserver({
        selector: `.gitem>>>.goods-item`,
        observeAll: true,
        context: this
      })
    }
  },
  methods: {
    hideCountDown() {
      this.setData({
        ['unitData.list_countdown']: 0
      })
    },
    onTap(e) {
      const item = e.currentTarget.dataset.item
      try {
        app.sf.track('mallpage_click', {
          track_cfg_id: this.data.pid,
          track_title: this.data.unitName || this.data.config.unitName || '商品',
          text: item.trackMemo
        })
      } catch (e) {}
      if (item.link) {
        app.toH5(item.link)
      }
    },
    onGoodsTap(e) {
      const { item } = e.currentTarget.dataset
      app.sf.track('mallpage_click', {
        track_cfg_id: this.data.pid,
        track_title: this.data.unitName || this.data.config.unitName || '商品',
        text: '商品点击',
        spu_id: item.goodsSn,
        spu_name: item.goodsName
      })
      app.toH5(`/goods/goodsDisplay?goods_sn=${item.goodsSn}`)
    },

    observer(e) {
      const list = e.detail
      if (!Array.isArray(list)) return
      console.log('observer1', e.detail);
      list.forEach(e => {
        const item = e.item
        app.report.exposeGoods({
          gooid: item.goodsSn,
          goodsName: item.goodsName,
          img: item.mImg,
          price_original: (item.qiangPrice || item.groupBuyPrice) ? item.salePrice || item.scPrice : item.scPrice,
          price_current: item.qiangPrice || item.groupBuyPrice || item.memberPrice || item.salePrice
        })
      })
    }
  }
})
