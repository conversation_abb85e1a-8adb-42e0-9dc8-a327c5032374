// components/common/share.js
const app = getApp()
Component({
  options: {
    virtualHost: true

  },
  properties: {
    styles: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isShowAlert: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    close() {
      this.setData({
        isShowAlert: false
      })
    },
    pengyouquan() {
      // this.hideShare()
      this.data.pengyouquan()
    },
    weixin() {
      // this.hideShare()
      this.data.weixin()
    }
  }
})
