// pages/distributershop/index/index.js
import Alert from '../../../components/alert/index'
import Share from '../../../components/common/share/index.js'
const util = require('../../../utils/util.js')
const CV = require('../../../utils/sf-canvas.js')
import shareCanvas from '../../../utils/shareCanvas.js'
import common from '../common.js'


const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    cardid: '', //当前卡号
    disshop: '', //分销店号
    dsho_id: '',
    scoreList: [],
    menu: [],


    width: 0,
    tmppath: '',
    dis: true,
    showqrcode: true,
    shareShop: '',
    card: '',
    newFlags: {
      act: 0,
      good: 0,
      microClass: 0,
      goodSh: 0
    },
    show_change: false, //显示更改门店按钮
    show_reset: false, //显示还原门店
    olddisshop: '', //初始导购店号
    showTimes: 1,
    tips: `亲爱的导购员：
      　　大家好！
      　　门店特供商品仅由分销店发货。为了确保商品供应充足，请务必在分销前提前核实本店库存，避免出现缺货情况，防止因无库存而无法发货，进而引发客户投诉。
      　　感谢您的理解和配合！
      　　祝您工作愉快！`,
    goodids: ''
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function(options) {
    this.test()
    await app.waitSid()
    this.init2()

  },
  async init2() {
    this.setData({
      cardid: wx.getStorageSync('cardid')
    })
    let check = await this.getdisshop()
    if (check) {
      await this.getBelongShop()
    }
    this.getScoreTotal()
    this.createShareUrl()
    if (this.data.showTimes == 1) {
      this.data.showTimes++
    }
    this.getNewsFlag()
    this.isShopOwner()
  },
  async onShow() {
    this.setData({
      dsho_id: wx.getStorageSync('dsho_id'),
      orgId: wx.getStorageSync('orgId')
    })
    //二次打开才触发
    if (!this.data.first) {
      this.data.first = true
      return
    }
    await this.getdisshop()
    //店号变更操作
    if (this.data.disshop != app.local.get('sho_id')) {
      this.setData({
        disshop: app.local.get('sho_id')
      })
      this.createShareUrl()
      this.getBelongShop()
    }
  },


  createShareUrl() {

    this.data.shareShop = `https://${util.apidomain}?disshop=${app.local.get('sho_id')}&discard=${wx.getStorageSync('cardid')}`
    if (wx.getSystemInfoSync().environment == 'wxwork')
      this.data.shareShop += '&from=qymall'
    this.setData({
      card: wx.getStorageSync('cardid'),
    })
  },
  /**根据店号判断是否是职能员工是得话返回店号GZW */
  getUseShop(shopId) {
    if (shopId === '') {
      return 'GZW'
    }
    let len = shopId.length
    if (shopId === '9930' || (shopId.lastIndexOf('Z') + 1) === len || shopId.indexOf('SP') === 0) {
      return 'GZW'
    }
    return shopId;
  },
  // 跳转我的店铺
  toMyShop() {
    if (!this.data.disshop) {
      wx.showModal({
        title: '提示',
        content: '分销门店为空将无法分销及记录奖金',
        showCancel: false,
        success: res => {
          this.disshop_change()
        }
      })
      return
    }
    app.toH5(`?disshop=${app.local.get('sho_id')}&shoId=${app.local.get('sho_id')}&discard=${wx.getStorageSync('cardid')}`)
  },
  async getdisshop() {
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/agentShop', {
      sid: wx.getStorageSync('sid')
    }, res2 => {})

    if (res.success) {
      let disshop = this.getUseShop(res.data);
      app.local.set('sho_id', disshop);
      wx.setStorageSync('disshop', disshop || '')
      app.local.set('isStaff', true);
      wx.setStorageSync('discard', wx.getStorageSync('cardid'));
      this.setData({
        disshop: disshop
      })
      return 1
    } else {
      if (res && res.msg && (res.msg.indexOf('缓存') != -1 || res.msg.indexOf('sid') != -1)) return
      app.local.set('sho_id', '')
      this.setData({
        disshop: '',
        show_change: true
      })
      wx.showModal({
        title: '提示',
        content: res.msg || '该本地化店暂未开通，将重新选择',
        showCancel: false,
        success: res1 => {
          if (res.msg == '非在职员工') {
            wx.navigateBack({
              delta: 1
            })
            return
          }

          this.disshop_change()
        }
      })
    }
    return 0
  },
  async getBelongShop() {
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/belongShopAndCompany', {
      sid: wx.getStorageSync('sid')
    }, res => {})
    console.log(res)
    if (res.success) {
      let oldshop = res.data.shoId //this.getUseShop(res.data)
      this.setData({
        belongShop: res.data.shoId,
        belongOrgId: res.data.mdmOrgId
      })
      //检测是否本地化
      app.globalData.oneceOrgId = this.data.belongOrgId
      let res2 = await app.reqGet('ms-sanfu-wap-shop/checkShopIsOnline', {
        shoId: oldshop
      }, res => {})
      console.log(res2)
      if (res2.data) {
        if (oldshop != app.local.get('sho_id')) {
          this.setData({
            show_reset: true,
            olddisshop: oldshop
          })
        }
      } else {
        this.setData({
          show_change: true,
        })
      }
    }
  },
  async getScoreTotal() {
    app.globalData.oneceOrgId = this.data.belongOrgId
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/achievement/total', {
      sid: wx.getStorageSync('sid')
    })
    let item = res.data || {}
    this.setData({
      scoreList: [
        [{
          title: '本月销售(次日更新)',
          value: item.totalSaleMoney || 0
        }, {
          title: '本月佣金(次日更新)',
          value: item.totalBonus || 0
        }]
      ],
    })
    if (!res.success) {
      app.util.reqFail(res)
    }
  },
  async getNewsFlag() {
    let res = await app.reqGet('ms-sanfu-wap-customer-distribution/distribution/work/haveNew', {
      sid: wx.getStorageSync('sid')
    })
    if (res.success) {
      this.setData({
        newFlags: res.data
      })
    }
  },
  toSingle() {
    if (!this.data.disshop) {
      wx.showModal({
        title: '提示',
        content: '分销门店为空将无法分销及记录奖金',
        showCancel: false,
        success: res => {
          this.disshop_change()
        }
      })
      return
    }
    wx.navigateTo({
      url: '/pages/distributershop/single/single',
      success: () => {
        this.data.newFlags.good = 0
        this.setData({
          newFlags: this.data.newFlags
        })
      }
    })
  },
  toGroup() {
    if (wx.getStorageSync('orgId') != this.data.belongOrgId && this.data.belongOrgId) {
      app.local.set('orgId', this.data.belongOrgId)
      wx.showToast({
        title: '已切换至' + (this.data.belongOrgId == 102 ? '百货' : '服饰'),
        icon: 'none'
      })
      app.local.remove('payShoId')
    }
    if (!this.data.disshop) {
      wx.showModal({
        title: '提示',
        content: '分销门店为空将无法分销及记录奖金及记录奖金',
        showCancel: false,
        success: res => {
          this.disshop_change()
        }
      })
      return
    }
    wx.navigateTo({
      url: '/pages/distributershop/group/group',
      success: () => {
        this.data.newFlags.goodSh = 0
        this.setData({
          newFlags: this.data.newFlags
        })
      }
    })
  },
  toActivity() {
    wx.navigateTo({
      url: '/pages/distributershop/active/active',
      success: () => {
        this.data.newFlags.act = 0
        this.setData({
          newFlags: this.data.newFlags
        })
      }
    })
  },
  toClass() {
    app.toH5('user/disshop-class')
    this.data.newFlags.microClass = 0
    this.setData({
      newFlags: this.data.newFlags
    })
  },
  toNetShop() {
    const shops = 'CQZ,DBZ,FZZ,HBZ,HFZ,NJZ,WHZ,ZBZ,GZW'
    if (shops.indexOf(this.data.belongShop) != -1) {
      wx.showModal({
        content: '仅门店人员可以分销门店特供商品',
        showCancel: false
      })
      return
    }
    if (!this.data.belongShop) {
      wx.showModal({
        content: '所属店本地化未开通，请校验【商户号、微信支付、本地化】开通后重试',
        showCancel: false
      })
      return
    }
    this.moreTip()
    console.log(this.data.belongShop);

  },
  toSelect() {
    app.toH5('user/disshop-score?' + '&baseType=' + (this.data.belongOrgId == 101 ? 'fs' : 'bh'))
  },
  async share() {
    await this.getdisshop()
    let info = await app.reqGet('ms-sanfu-wap-customer/index/baseInfo', {
      sid: wx.getStorageSync('sid')
    })
    if (info.success) {
      this.data.nickName = info.data.nickname
    }
    if (!this.data.disshop) {
      wx.showModal({
        title: '提示',
        content: '分销门店为空将无法分销及记录奖金及记录奖金',
        showCancel: false,
        success: res => {
          this.disshop_change()
        }
      })
      return
    }
    // this.workTrace(5)
    this.traceRecord({
      "operateType": 1,
      "scene": 5,
      "shoId": app.local.get('sho_id')
    })
    Share().then(el => {
      console.log('微信好友')
    }).catch(el => {
      this.showCode()
    })
  },
  refresh() {
    this.data.tmppath = false
    this.showCode()
  },
  async showCode() {
    let sys = wx.getSystemInfoSync();


    await this.clearImgTemp()
    this.setData({
      showqrcode: false
    })
    if (this.data.tmppath && this.data.tmppath.length > 0) {
      return
    }
    wx.showLoading()
    let canvasWidth = app.util.rpx2px(520)
    console.log(canvasWidth)
    let canvasHeight = app.util.rpx2px(650)
    let rpx = app.util.rpx2px(1)
    let img
    img = await this.loadImg('https://img.sanfu.com/sf_access/uploads/PzMzFaImrHeu3p5OHHDt1D5jkS7tHIUF.jpg')
    //绘制二维码
    //背景图

    // const ctx = wx.createCanvasContext('shareCanvas')
    CV.createCanvas("shareCanvas1", canvasWidth, canvasHeight)

    let ctx = CV.ctx
    //整体圆角
    //整体圆角
    CV.radiusClip({
      radius: 16 * rpx,
      fillStyle: 'white',
      strokeStyle: 'rgba(0,0,0,0)'
    })

    ctx.restore()
    ctx.beginPath()
    ctx.save();


    if (img) {
      ctx.arc(70 * rpx, 70 * rpx, 40 * rpx, 0, 2 * Math.PI)
      ctx.strokeStyle = '#f1f1f1'
      ctx.stroke()
      ctx.clip()
      ctx.drawImage(img.path, 30 * rpx, 30 * rpx, 80 * rpx, 80 * rpx)
    }

    ctx.restore()
    // ctx.closePath();
    // ctx.beginPath()


    //写入文字
    CV.textWrap({
      x: 120 * rpx,
      y: 35 * rpx,
      width: canvasWidth - 65,
      height: 20,
      line: 2,
      color: '#000',
      size: 17,
      align: 'left',
      baseline: 'top',
      text: this.data.nickName ? this.data.nickName + '的微信商城' : '三福商城',
      bold: false
    })
    CV.text({
      textAlign: 'center',
      width: 520 * rpx,
      size: 18,
      color: '#000',
      text: '长按识别小程序码',
      x: canvasWidth / 2,
      y: canvasHeight - 30,
      bold: true
    })
    // 小程序码
    let baseres = await app.getTemQRcode(this.data.shareShop)
    let base64 = ''
    if (baseres.success) {
      base64 = baseres.data.base64Img
    } else {
      wx.hideLoading()
      this.setData({
        showqrcode: true
      })
      wx.showToast({
        title: '二维码生成失败！' + wx.getStorageSync('sid') + baseres.msg,
        icon: 'none',
        duration: 3000
      })
      return
    }
    const qrImgSize = 420 * rpx
    let img_base64 = this.loadBase64(base64)
    CV.ctx.drawImage(img_base64, (canvasWidth - qrImgSize) / 2, 130 * rpx, qrImgSize, qrImgSize)
    // CV.canvas.stroke()
    console.log('ready')

    CV.draw(false, draw => {
      setTimeout(() => {

        wx.canvasToTempFilePath({
          canvasId: 'shareCanvas1',
          width: canvasWidth,
          height: canvasHeight,
          // destWidth: canvasWidth / rpx / 630 * 720,
          // destHeight: canvasHeight / rpx / 630 * 720,
          quality: 1,
          success: res => {
            console.log(res)
            this.setData({
              tmppath: res.tempFilePath
            })

          },
          complete: res => {}
        })
      }, 300)

    })
    wx.hideLoading()


  },
  // 更换区域店
  async shop_change() {
    if (!this.data.disshop) {
      wx.showModal({
        title: '提示',
        content: '分销门店为空将无法分销及记录奖金及记录奖金',
        showCancel: false,
        success: res => {
          this.disshop_change()
        }
      })
      return
    }
    let shops = await app.getAllShop()
    let location = ''
    for (const i in shops) {
      if (shops[i].shoId == this.data.disshop) {
        this.shop = shops[i]
        break
      }
    }
    if (this.shop) {
      app.local.set('dsho_id', this.shop.districtShoId || 'GZQ')

      app.local.set('orgId', this.shop.orgId)
      wx.showToast({
        title: '已切换至' + (this.shop.orgId == 102 ? '百货' : '服饰'),
        icon: 'none'
      })
      this.setData({
        dsho_id: this.shop.districtShoId || 'GZQ',
        orgId: this.shop.orgId
      })
      app.local.set('payShoId', this.shop.shoId)
    } else {
      wx.showToast({
        icon: 'none',
        title: '未匹配到对应区域'
      })
    }
  },
  //更换分销门店
  disshop_change() {
    wx.navigateTo({
      url: '/pages/distributershop/change_disshop/change_disshop',
    })
  },
  //还原分销门店
  disshop_reset() {
    wx.showModal({
      title: '提示',
      content: '是否还原分销门店为：' + this.data.olddisshop,
      success: async res => {
        if (res.confirm) {
          wx.showLoading({
            title: '请稍等...'
          })
          let res2 = await app.reqPost('ms-sanfu-wap-customer-distribution/distribution/agentShop/update', {
            shoId: '',
            sid: wx.getStorageSync('sid'),
          })
          wx.hideLoading()
          if (res2.success) {
            wx.showToast({
              title: res2.msg,
              mask: true,
              duration: 2000
            })
            app.local.set('sho_id', this.data.olddisshop)
            app.local.set('disshop', this.data.olddisshop)
            this.setData({
              disshop: this.data.olddisshop,
              show_reset: false,
            })
            this.createShareUrl()
          }
        }
      }
    })
  },
  toSns() {
    if (!this.data.disshop) {
      wx.showModal({
        title: '提示',
        content: '分销门店为空将无法分销及记录奖金及记录奖金',
        showCancel: false,
        success: res => {
          this.disshop_change()
        }
      })
      return
    }
    wx.switchTab({
      url: '/pages/find/find'
    })

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function(options) {
    let shop = app.local.get('sho_id')
    let card = wx.getStorageSync('cardid')
    let endUrl = this.data.shareShop
    // 设置菜单中的转发按钮触发转发事件时的转发内容
    let title = this.data.nickName ? (this.data.nickName + '的微信商城') : '三福商城'
    var shareObj = {
      title: title, // 默认是小程序的名称(可以写slogan等)
      path: '/pages/pcweb/pcweb?shareUrl=' + encodeURIComponent(endUrl), // 默认是当前页面，必须是以‘/’开头的完整路径
      imageUrl: 'https://img.sanfu.com/sf_access/uploads/PzMzFaImrHeu3p5OHHDt1D5jkS7tHIUF.jpg',
      complete: function() {
        // 转发结束之后的回调（转发成不成功都会执行）
      }
    };
    app.sr.track('page_share_app_message', {
      "from_type": options.from,
      "share_title": title,
      "share_path": '/pages/pcweb/pcweb?shareUrl=' + encodeURIComponent(endUrl),
      "share_image_url": 'https://img.sanfu.com/sf_access/uploads/PzMzFaImrHeu3p5OHHDt1D5jkS7tHIUF.jpg' // more...
    })
    // 返回shareObj
    return shareObj;
  },
  toServices() {
    wx.navigateTo({
      url: '../services/index/index'
    })
  },
  showtip(e) {
    //查看提示信息
    let i = e.currentTarget.dataset.i
    let msg
    if (i == 0)
      msg = '本月发货的订单销售额(发货订单)，展示金额为【实际销售额*0.1】，次日更新'
    else if (i == 1)
      msg = '今日发货的订单销售额(发货订单)，展示金额为【实际销售额*0.1】，当日更新'
    if (msg) {
      wx.showModal({
        title: '提示',
        content: msg,
        showCancel: false
      })
    }
  },
  moreShare() {
    wx.navigateTo({
      url: '../shareGoods/shareGoods'
    })
  },
  ...shareCanvas,
  ...common,
  test() {
    this.setData({
      menu: [{
          tab: '分销管理',
          data: [{
              name: '我的店铺',
              icon: 'iconweixinshangchengyuan',
              tap: 'toMyShop'
            },
            {
              name: '分享店铺',
              icon: 'iconshare',
              tap: 'share'
            },
            // {},
            {
              name: '会员服务',
              icon: 'iconv',
              tap: 'toServices'
            }
          ]
        },
        {
          tab: '素材库',
          data: [{
              //   name: '单品推荐',
              //   icon: 'iconsingle',
              //   tap: 'toSingle',
              //   flag: 'good'
              // }, {
              name: '组合推荐',
              icon: 'icongroup',
              tap: 'toGroup',
              flag: 'goodSh'

            },
            {
              name: '活动推荐',
              icon: 'iconactive',
              tap: 'toActivity',
              flag: 'act'
            },
            {
              name: '好物圈',
              icon: 'iconsns',
              tap: 'toSns'
            }
          ]
        },
        {
          tab: '其他',
          data: [
            {
              name: '分销微课堂',
              icon: 'iconclass',
              tap: 'toClass',
              flag: 'microClass'
            },
            {
              name: '门店特供商品',
              icon: 'iconnetshop',
              tap: 'toNetShop'
            },
            {
              name: '业绩查询',
              icon: 'icondaogoufenxiao',
              tap: 'toSelect'
            },
            {
              name: '更多分享',
              icon: 'iconDG-fenxiang',
              tap: 'moreShare'
            },
            {
              name: '清单分享',
              icon: 'iconsingle',
              tap: 'openInput',
              flag: 'good'
            },
            {
              name: '扫商品条码',
              icon: 'uni-icon uni-icon-scan',
              tap: 'toScan',
            },
            {
              name: '员工企微',
              icon: 'f7 iconqrcode',
              tap: 'toStaffCode',
            }
          ]
        }
      ]
    })
  },
  async isShopOwner() {
    if (this.isAdd) return
    const res = await app.reqGet('ms-sanfu-wap-customer-distribution/goods/isShopOwner', {
      sid: wx.getStorageSync('sid')
    })
    if (res.success && res.data != 1) {
      this.isAdd = 1
      this.data.menu[2].data.splice(2, 0, {
        name: '商品图片审核',
        icon: 'iconnetshop',
        tap: 'verifyImg'
      })
      this.setData({
        menu: this.data.menu
      })
    }
  },
  verifyImg() {
    app.toH5('/pages/distributershop/verifyImgList/verifyImgList')
  },
  moreTip() {
    Alert({
      context: this,
      selector: '#sanfu-alert1',
      alertText: '系统提醒',
      content: this.data.tips,
      left: false,
      right: '继续',
      confirm: e => {
        app.toH5('noGroundingSearch?disshop=' + this.data.belongShop + '&shopId=' + this.data.belongShop + '&discard=' + wx.getStorageSync('cardid') + '&baseType=' + (this.data.belongOrgId == 101 ? 'fs' : 'bh'))
      }
    })
  },
  bindInput(e) {
    // 输入改变赋值。
    const key = e.currentTarget.dataset.key
    this.setData({
      [key]: e.detail.value
    })
  },
  openInput(e) {
    Alert({
      alertText: '清单分享',
      context: this,
      selector: '#sanfu-alert2',
      slot: true,
      left: false,
      confirm: () => {
        const val = this.data.goodids
        let ele = ''
        let arr = []
        for (let i = 0; i < val.length; i++) {
          if (!/^[0-9]*$/.test(val[i])) {
            if (ele.length > 0) {
              arr.push(ele)
            }
            ele = ''
          } else {
            ele += val[i]
            if (i === val.length - 1) {
              arr.push(ele)
            }
          }
        }
        if (arr.length > 0) {
          app.toH5('/pages/goods/cartShare/cartShare?type=toShare&gooids=' + arr.join())
        } else {
          wx.showToast({
            icon: 'none',
            title: '请正确输入货号'
          })
        }
      }
    })
  },
  toScan(){
    app.toH5(`pagescan`);
  },
  toStaffCode(){
    app.toH5('/pages/distributershop/staffCode/staffCode')
  }
})
