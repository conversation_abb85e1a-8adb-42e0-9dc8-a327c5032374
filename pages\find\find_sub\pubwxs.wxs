var filters = {
    toFix: function (value) {//此处2为保留两位小数
      if (value != null && value != "") {
        return value.toFixed(2);
      } else {
        return "0.00";
      }
    },
    toInt: function (value) {//此处0位保留两位小数
      if (value != null && value != "") {
        return parseInt(value);
      } else {
        return "0";
      }
    },
    toJson: function (value) { //转为json
      if (value != null && value != "") {
        return JSON.parse(value);//返回json
      }
      else {
        return null;
      }
    },
    toString: function (value) { //空字符串过滤掉null
      if (value != null && value != "" && value !="null") {
        return value;//返回json
      }
      else {
        return "";
      }
    },
    toDate: function (value) { //转为日期格式MM/DD
      if (value != null && value != "") {
        return value.substring(5, 7) + '/' + value.substring(8, 10);
      } else {
        return "";
      }
    },
    toDatecn: function (vdate, num) { //日期增加多少天，返回MM月dd日
      var redate = getDate(vdate);
      redate = redate.setDate(redate.getDate() + num);
      if (redate != null && redate != "") {
        var rdate = getDate(redate);
        return rdate.getMonth() + 1 + '月' + rdate.getDate() + '日';
      } else {
        return "";
      }
    },
    toDatecn_dian: function (vdate, num) { //日期增加多少天，返回MM月dd日.replace("T","").replace("Z","")
      // var redate = getDate(vdate);
      // //redate = redate.setDate(redate.getDate() + num);
      // if (redate != null && redate != "") {
      //   //var rdate = getDate(redate);
      //   return redate.getFullYear()+"."+ redate.getMonth() + 1 + '.' + redate.getDate() ;
      // } else {
      //   return "";
      // }
  
      if (vdate != null && vdate != "") {
        return vdate.substring(0, 4) + "." + vdate.substring(5, 7) + '.' + vdate.substring(8, 10);
      } else {
        return "";
      }
    },
    toDistance: function (value) { //转为距离
      if (value != null && value != "") {
        if(parseFloat(value)>1000){
          return parseFloat(parseFloat(value) / 1000).toFixed(2) + 'Km';
        }
        else{
          return parseInt(value) + 'M';
        }
      } else {
        return "";
      }
    },
    toNumber: function (value) { //转为数量
      if (value != null && value != "") {
        var v=parseFloat(value);
        if(v>10000){
          return parseFloat(parseFloat(value) / 10000).toFixed(2) + 'w';
        }else if(v>1000){
          return parseFloat(parseFloat(value) / 1000).toFixed(2) + 'k';
        }else{
          return parseFloat(parseFloat(value)).toFixed(0);
        }
      } else {
        return "0";
      }
    },
    toDatelong: function (value) { //转为日期格式YYYY-MM-DD
      if (value != null && value != "") {
        return value.substring(0, 4) + "." + value.substring(5, 7) + '.' + value.substring(8, 10);
      } else {
        return "";
      }
    },
    toDateMMddhhss: function (value) { //转为日期格式YYYY-MM-DD
      if (value != null && value != "") {
        return value.substring(5, 7) + "-" + value.substring(8, 10) + ' ' + value.substring(11, 13) + ':' + value.substring(14, 16);
      } else {
        return "";
      }
    },
    toDatehhss: function (value) { //转为日期格式HH:MM
      if (value != null && value != "") {
        return value.substring(11, 13) + ':' + value.substring(14, 16);
      } else {
        return "";
      }
    },
    toDatehhsstimestamp: function (value) { //转为日期格式HH:MM
      if (value != null && value != "") {
        var rdate = getDate(value * 1000);
        return rdate.getHours() + ':' + rdate.getMinutes();
      } else {
        return "";
      }
    },
    toDateMMddhhsstimestamp: function (value) { //转为日期格式YYYY-MM-DD
      if (value != null && value != "") {
        var rdate = getDate(value * 1000);
        return ((rdate.getMonth() + 1) < 10 ? '0' : '') + (rdate.getMonth() + 1) + "-" + (rdate.getDate() < 10 ? '0' : '') + rdate.getDate() + ' ' + rdate.getHours() + ':' + rdate.getMinutes();
      } else {
        return "";
      }
    },
    toDateyyyyMMddhhsstimestamp: function (value) { //转为日期格式YYYY-MM-DD
      if (value != null && value != "") {
        var rdate = getDate(value * 1000);
        return rdate.getFullYear() + '-' +((rdate.getMonth() + 1) < 10 ? '0' : '') + (rdate.getMonth() + 1) + "-" + (rdate.getDate() < 10 ? '0' : '') + rdate.getDate() + ' ' + rdate.getHours() + ':' + rdate.getMinutes();
      } else {
        return "";
      }
    },
    toDateYYYYMMddhhss: function (value) { //转为日期格式YYYY-MM-DD
      if (value != null && value != "") {
        return value.substring(0, 4) + "-" + value.substring(5, 7) + "-" + value.substring(8, 10) + ' ' + value.substring(11, 13) + ':' + value.substring(14, 16);
      } else {
        return "";
      }
    },
    toDateYYYYMMdddian: function (value) { //转为日期格式YYYY.MM.DD
      if (value != null && value != "") {
        return value.substring(0, 4) + "." + value.substring(5, 7) + "." + value.substring(8, 10);
      } else {
        return "";
      }
    },
    toDatelongs: function (vdate, num) { //日期增加多少天，返回MM月dd日
      var redate = getDate(vdate);
      redate = redate.setDate(redate.getDate() + num);
      if (redate != null && redate != "") {
        var rdate = getDate(redate);
        return vdate.substring(0, 4) + '-' + ((rdate.getMonth() + 1) < 10 ? '0' : '') + (rdate.getMonth() + 1) + '-' + (rdate.getDate() < 10 ? '0' : '') + rdate.getDate() + '';
      } else {
        return "";
      }
    },
    toComment: function (value) { //评论格式转换
      if (value != null && value != "") {
        return value.replace('<t>', '<text>').replace('</t>', '</text>');
      }
      else {
        return "";
      }
    },
    toSplit: function (value) {//拆分成数组
      if (value != null && value != "" && value != "null,") {
        return value.split(',');
      }
      else {
        return null;
      }
    },
    toSplitOne: function (value) {//拆分成数组，并返回第一个地址
      if (value != null && value != "" && value != "null,") {
        return value.split(',')[0];
      }
      else {
        return "";
      }
    },
    toLength: function (value, len) {
      if (value != null) {
        if (value.length > len) {
          return value.substring(0, len);
        }
      } else {
        return "";
      }
    },
    toCheck: function (dlist, value) {//判断数组中是否包含某值，-1表示不包含
      var inum = -1;
      if (dlist != null && dlist.length > 0 && value != "") {
        for (var i = 0; i < dlist.length; i++) {
          if (dlist[i] == value) {
            inum = i;
            break;
          }
        }
      }
      return inum;
    },
    toStringTop:function(value,vlen){
      if (value != null && value!="") {
        if(value.length<=vlen){
          return value;
        }
        else{
          return value.substring(0,vlen)+"..."
        }
      } else {
        return "";
      }
    },
    toTimespan: function (value) { //日期转时间戳
      if (value != null) {
        value = value.replace('T', ' ');
        //var date = new Date(value);
        return Date.parse(value);
      } else {
        return 0;
      }
    },
    toDatecomparison: function (date1, date2) {//日期比较函数,日期1大于日期2返回True,否则返回False
      if (date1 != null && date2 != null) {
        if (parseFloat(date1) > parseFloat(date2)) {
          return true;
        }
        else {
          return false;
        }
      } else {
        return false;
      }
    },
    toCheckDatein:function(sdate,edate){
      if (sdate!=null && edate!=null){
        var nowdate = Date.now();
        var ssdate = Date.parse(sdate);
        var eedate = Date.parse(edate);
        if(ssdate-nowdate>0 && eedate-nowdate>0){
          return true;
        }
      }
      return false;
    },
    toGetDateInfo: function (indate) {
      var result;
      var minute = 1000 * 60;
      var hour = minute * 60;
      var day = hour * 24;
      var halfamonth = day * 15;
      var month = day * 30;
      var nowdate = Date.now();
      var cdate = Date.parse(indate);
      var diffValue = nowdate - cdate;
      //console.log(nowdate + "-" + diffValue + "-" + cdate);
      if (diffValue < 0) {
        return;
      }
      var monthC = diffValue / month;
      var weekC = diffValue / (7 * day);
      var dayC = diffValue / day;
      var hourC = diffValue / hour;
      var minC = diffValue / minute;
  
      if (monthC >= 1) {
        if (monthC <= 12)
          result = '' + parseInt(monthC) + '月前';
        else {
          result = '' + parseInt(monthC / 12) + '年前';
        }
      }
      else if (weekC >= 1) {
        result = "" + parseInt(weekC) + '周前';
      }
      else if (dayC >= 1) {
        result = "" + parseInt(dayC) + '天前';
      }
      else if (hourC >= 1) {
        result = "" + parseInt(hourC) + '小时前';
      }
      else if (minC >= 1) {
        result = "" + parseInt(minC) + '分钟前';
      } else {
        result = '刚刚';
      }
      return result;
    },
   jpg2jpeg:function(src, size) {
    if (!src) return
    var reg = getRegExp('\ +', "g");
    var src1 = src.replace(reg, '')
    // src = src.replace('a',"")
    if (src1.indexOf('/img.sanfu.com') != -1)
      return src1.indexOf('.jpg') != -1 ? src1.replace('.jpg', '.jpeg') : src1
    else if (src1.indexOf('snsimg.sanfu.com') != -1) {
      if (size > 0)
        return src1 + '?imageView2/4/w/' + size
      else
        return src1 + '?imageView2/4/w/450'
    } else return src1
  }
}
  
  
  module.exports = {
    toFix: filters.toFix,
    toInt: filters.toInt,
    toJson: filters.toJson,
    toDate: filters.toDate,
    toDatelong: filters.toDatelong,
    toComment: filters.toComment,
    toString: filters.toString,
    toSplit: filters.toSplit,
    toSplitOne: filters.toSplitOne,
    toCheck: filters.toCheck,
    toTimespan: filters.toTimespan,
    toDatecomparison: filters.toDatecomparison,
    toDistance: filters.toDistance,
    toDatecn: filters.toDatecn,
    toDatelongs: filters.toDatelongs,
    toDateMMddhhss: filters.toDateMMddhhss,
    toDateYYYYMMddhhss: filters.toDateYYYYMMddhhss,
    toGetDateInfo: filters.toGetDateInfo,
    toNumber: filters.toNumber,
    toCheckDatein: filters.toCheckDatein,
    toDatecn_dian: filters.toDatecn_dian,
    toDateMMddhhsstimestamp: filters.toDateMMddhhsstimestamp,
    toDateyyyyMMddhhsstimestamp: filters.toDateyyyyMMddhhsstimestamp,
    toDatehhss: filters.toDatehhss,
    toDateYYYYMMdddian: filters.toDateYYYYMMdddian,
    toStringTop: filters.toStringTop,
    toDatehhsstimestamp: filters.toDatehhsstimestamp,
    toLength:filters.toLength,
    jpg2jpeg:filters.jpg2jpeg
  }