/* pages/distributershop/services/userInfo/userInfo.wxss */
page {
  background: #f3f3f3;
}

.top-box {
  padding: 30rpx;
  display: flex;
  align-items: center;
}

.top-box .img {
  height: 120rpx;
  width: 120rpx;
  border-radius: 50%;
}

.item-box {
  background: #fff;
  padding: 0 30rpx;
  margin-bottom: 10rpx;
}

.item-box .item {
  padding: 20rpx 10rpx;
  border-bottom: 2rpx solid #f3f3f3;
  font-size: 30rpx;
  color: #333;
}

.item-box .item:last-of-type {
  border: none;
}
