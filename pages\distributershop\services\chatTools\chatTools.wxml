<!--pages/distributershop/services/chatTools/chatTools.wxml-->
<button bindtap="auth" style="background: green;height: 100rpx;line-height: 100rpx;">登录</button>
<button bindtap="checkQy" style="background: #ff78b3;height: 100rpx;line-height: 100rpx;">qy登录{{qyLogin}}</button>
<button bindtap="getContext" style="background: #fffc9d;height: 100rpx;line-height: 100rpx;">获取环境{{qyEntry}}</button>
<button bindtap="getCurExternalContact" style="background: #9aff8f;height: 100rpx;line-height: 100rpx;">获取userid</button>
<button bindtap="openOptions" style="background: green;height: 100rpx;line-height: 100rpx;">打开菜单</button>

<view>userId:{{userId}}</view>
<view>sid:{{sid}}</view>
<button bindtap="sendChatMessage" style="background: #8aa3ff;height: 100rpx;line-height: 100rpx;">发送小程序</button>
<button bindtap="sendChatMessage2" style="background: #f692ff;height: 100rpx;line-height: 100rpx;">发送文本</button>
<button bindtap="sendChatMessage3" style="background: #ff81cf;height: 100rpx;line-height: 100rpx;">发送图片</button>
<auth id="sanfu-auth"></auth>