.goods {
  display: flex;
  /* box-shadow: 2rpx 2rpx 12rpx #eee; */
  position: relative;
  overflow: hidden;
}

.imgbox {
  position: relative;
  display: flex;
}

.goo-img {
  overflow: hidden;
  background: #fefefe;
  position: relative;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  height: 100%;
}

.goo-item1 {
  background: #fff;
  border-radius: 18rpx;
  width: 100%;
  margin-left: 0;
  overflow: hidden;
  margin-bottom: 24rpx;
  flex-direction: row;
  justify-content: space-between;
  /* padding: 18rpx; */
}

.goo-item1.big-1 {
  padding: 0;
  border-radius: 0;
  margin: 0;
  box-shadow: none;
}

.goo-item2 {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  /* padding-bottom: 12rpx; */
  margin-bottom: 18rpx;
  justify-content: space-between;
  flex-direction: column;
}

.goo-item2.nleft2:nth-of-type(2n+1) {
  margin-left: 0 !important;
}

.goo-item2.nleft2 .goo-price-box {
  width: calc(100% - 55rpx);
}

.goo-item2.nleft3:nth-of-type(3n+1) {
  margin-left: 0 !important;
}

.goo-item2.nleft4:nth-of-type(4n+1) {
  margin-left: 0 !important;
}


.goo-item1 .goo-img {
  width: 150rpx;
  height: 150rpx;
  margin-right: 12rpx;
  border-radius: 12rpx;
}

.goo-item1.big-3 .goo-img {
  min-width: 240rpx;
  width: 240rpx;
  height: 240rpx;
  background: #f1f1f1;
  margin-right: 16rpx;
  border-radius: 18rpx 0 0 18rpx;
  position: relative;
}

.goo-item1.big-1 .goo-img {
  min-width: 110rpx;
  width: 110rpx;
  height: 110rpx;
  background: #f1f1f1;
  margin-right: 16rpx;
  border-radius: 16rpx;
  position: relative;
  border: 1px solid #F2F6FC;
}

.goo-item3 {
  /* width: 100%; */
  display: flex;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  padding-bottom: 12rpx;
  justify-content: space-between;
  flex-direction: column;
}


.goo-msgs {
  padding: 0 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  flex: 1 1 0%;
  overflow: hidden;
}

.goo-item1 .goo-msgs {
  padding: 0 16rpx 0 8rpx;
  justify-content: unset;
  display: flex;
  flex-flow: column;
  justify-content: space-between;
}

.goo-name {
  color: #333333;
  margin: 16rpx 0 20rpx;
  /* overflow: hidden; */
  font-size: 24rpx;
  line-height: 1.35;
  max-width: 100%;
  min-height: 40rpx;
}

.goo-item2.nleft3 .goo-name {
  margin: 4rpx 0 2rpx 0;
  font-size: 22rpx;
}

.goo-item1.big-1 .goo-name {
  font-size: 24rpx;
  line-height: 1.1;
  margin-top: 0;
}

.goo-item1 .goo-name {
  font-weight: bold;
  margin: 0 0 16rpx 0;
}

.title-line1 {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.title-line2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
}

.goo-name .presell-tag {
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
  background: #fe82b9;
  color: #fff;
  margin-right: 2rpx;
  display: inline;
}

.goo-item1.big-3 .goo-name {
  font-size: 24rpx;
  color: #333333;
  font-weight: 700;
  margin: 16rpx 0 8rpx;
}

.goo-price-box {
  color: #E60012;
  font-size: 24rpx;
  display: flex;
  align-items: flex-end;
  line-height: 1;
  font-weight: 700;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.goo-price-box .price {
  /* margin-left: 4rpx;
  font-size: 32rpx;
  color: #E60012;
  font-weight: bold; */
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1;
}

.goo-item1.big-1 .goo-price-box .price {
  font-size: 30rpx;
}

.goo-item1 .goo-price-box .price {
  font-size: 36rpx;
}

.goo-item1 .goo-price-box .rmbTxt {
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 2rpx;
}

.goo-item2.nleft3 .goo-price-box .price {
  font-size: 26rpx;
  margin-left: 0;
}

.goo-item2.nleft3 .goodTitleTag {
  margin-right: 4rpx;
  padding: 0rpx 4rpx;
  font-size: 20rpx;
  zoom: 0.8;
  vertical-align: middle;
}

.goo-price-box .del-price {
  color: rgba(204, 204, 204, 1);
  font-size: 24rpx;
  margin-left: 4rpx;
  text-decoration: line-through;
  font-weight: 400;
}

.goo-item2.nleft3 .goo-price-box .del-price {
  font-size: 20rpx;
}


.goo-item1.big-3 .goo-price-box {
  font-size: 24rpx;
  margin-top: 0;
  margin-bottom: 0;
  /* margin-bottom: 15rpx; */
}

.goo-item1.big-3 .price {
  /* font-size: 40rpx; */
}

.goo-tag {
  width: 28%;
  position: absolute;
  top: 3%;
  right: 3%;
  z-index: 2;
  height: 0;
}

.goo-item2 .goo-tag {
  width: 35%;
}

.goo-item3 .goo-tag {
  width: 35%;
}

.price-active-tag {
  z-index: 9;
  background: -webkit-gradient(linear, left top, right top, from(rgba(254, 130, 185, 0.7)), to(rgba(254, 85, 150, 0.9)));
  background: linear-gradient(90deg, rgba(254, 130, 185, 0.7), rgba(254, 85, 150, 0.9));
  border-radius: 0 10px 0 0;
  padding: 10rpx 22rpx;
  position: absolute;
  bottom: 0;
  color: #fff;
  font-weight: 700;
  font-size: 30rpx;
}

.price-active-tag2 {
  background: #ff7931;
  border-radius: 4rpx;
  padding: 2rpx 6rpx;
  color: #fff;
  font-size: 20rpx;
  align-self: center;
  margin-right: 8rpx;
  display: flex;
}

.goo-item2.nleft3 .price-active-tag2 {
  font-size: 20rpx;
}

.goodTitleTag {
  background: #E60012;
  border-radius: 8rpx;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  margin-right: 8rpx;
  align-self: flex-end;
}


.goo-item1 .goodDistance {
  margin: 16rpx 0 8rpx;
}

.txtCon {
  /* margin-top: 10px; */
  /* margin-top: 8rpx; */
  display: flex;
  align-items: revert;
  position: relative;
  flex-flow: column;
}

.price-active-box {
  align-self: center;
  margin-bottom: 4rpx;
}

.goo-price-con {
  display: flex;
  align-items: baseline;
}

.rmbTxt {
  display: inline-block;
  font-size: 24rpx;
  height: 24rpx;
  line-height: 24rpx;
}