@import "/font/iconSF.wxss";

.goods-item {
  box-sizing: border-box;
  background: #fff;
  box-shadow: none;
  position: relative;
}

.goods-item.shadow-light {
  box-shadow: 0 2rpx 12rpx 0 #eee;
}

.goods-item.shadow-medium {
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.1);
}

.goods-item.shadow-dark {
  box-shadow: 0 2rpx 16rpx 0 rgba(0, 0, 0, 0.2);
}

.goods-image {
  position: relative;
  width: 100%;
  padding-top: 100%;
  background: #fbfbfb;
  overflow: hidden;
}

.goods-image .img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods-image .goods-tag {
  position: absolute;
  width: 22%;
  height: 22%;
  min-width: 60rpx;
  min-height: 60rpx;
  left: unset;
  top: 0;
  right: 0;
  z-index: 1;
}

.goods-info {
  padding: 4rpx 8rpx 6rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.goods-name {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  white-space: normal;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 两行文本截断 */
.goods-name.line-2 {
  -webkit-line-clamp: 2;
}

.goods-name.line-hide {
  display: none;
}


.goods-promo {
  max-width: 100%;
  height: 28rpx;
  border-radius: 8rpx;
  border: 1px solid rgba(230, 0, 18, 1);
  color: rgba(230, 0, 18, 1);
  padding: 0 8rpx;
  margin-bottom: 6rpx;
  display: flex;
  align-items: center;
  width: fit-content;
}

.goods-promo>text {
  font-size: 20rpx;
  height: 20rpx;
  line-height: 20rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


.goods-price {
  margin-bottom: 8rpx;
  display: flex;
  align-items: baseline;
  line-height: 1;
  flex-wrap: wrap;

}

.goods-price .current-price {
  font-size: 28rpx;
  font-weight: bold;
  color: #e60012;
}

.goods-price .original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 8rpx;
}

.goods-price .plus {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  background: linear-gradient(to left, #e60012, #ff7956);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 40rpx;
  margin-left: auto;
  transform: rotate(45deg);
}

.price-tag {
  background: #e60012;
  border-radius: 8rpx;
  color: #fff;
  font-size: 20rpx;
  padding: 0rpx 6rpx;
  vertical-align: middle;
  zoom: 0.8;
  line-height: 1;
  flex-shrink: 0;
}

.goods-price .price-tag {
  zoom: 1;
  margin-right: 8rpx;
  display: inline-block;
  padding: 4rpx 8rpx;
  align-self: center;
  margin-bottom: -2rpx;
}

.goods-sales {
  font-size: 24rpx;
  color: #999;
}

.gooods-explanation {
  color: rgba(243, 152, 0, 1);
  box-sizing: border-box;
  text-align: left;
  font-size: 20rpx;
  margin: 2rpx 0;
  line-height: 1;
}

/* 布局1样式 */
.layout-1-item {
  display: flex;
}

.layout-1-item .goods-name {
  font-size: 28rpx;
}

.layout-1-item .goods-image {
  width: 240rpx;
  height: 240rpx;
  padding-top: 0;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.layout-1-item .goods-image .img {
  width: 240rpx;
  position: relative;
  border-radius: 8rpx;
}

.layout-1-item .goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10rpx 20rpx 10rpx 0;
}

.layout-1-item .goods-info .goods-promo {
  margin-top: auto;
  max-width: calc(100% - 20rpx);
}


.layout-1-item .goods-info .current-price {
  font-size: 36rpx;
  line-height: 20rpx;
}

.layout-1-item .goods-info .plus {
  margin-right: 20rpx;
}



/* 布局2样式 */
.layout-2-item {}

.layout-2-item .goods-info,
.layout-20-item .goods-info {
  padding: 16rpx 16rpx 12rpx;
}

.layout-2-item .goods-name,
.layout-20-item .goods-name {
  margin-bottom: 20rpx;
}


.layout-2-item .current-price,
.layout-20-item .current-price {
  line-height: 20rpx;
  font-size: 32rpx;
}


/* 布局3样式 */
.layout-3-item {}

.layout-3-item .goods-info .goods-name {
  font-size: 22rpx;
  margin-bottom: 6rpx;
}

.layout-3-item .goods-info .goods-price {
  margin-bottom: 0;
}

.layout-3-item .goods-info .goods-price .current-price {
  font-size: 28rpx;
  font-weight: bold;
  color: #e60012;
}

.layout-3-item .goods-info .goods-price .original-price {
  font-size: 20rpx;
  margin-left: 6rpx;
  color: #999;
  text-decoration: line-through;
}

.layout-3-item .goods-info .goods-sales {
  font-size: 20rpx;
}

.layout-3-item .plus {
  height: 32rpx;
  width: 32rpx;
  font-size: 32rpx;
}

/* 横向滚动布局样式 */
.layout-scroll-item {
  width: 176rpx;
  min-width: 176rpx;
  border-radius: 16rpx;
  overflow: hidden;
  display: inline-block;
}

.layout-scroll-item .goods-info .goods-name {
  font-size: 22rpx;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}


.layout-scroll-item .goods-info .goods-price {
  margin-bottom: 0;
  flex-wrap: wrap;
}

.layout-scroll-item .goods-info .goods-price .current-price {
  font-size: 28rpx;
}

.layout-scroll-item .goods-info .goods-price .original-price {
  margin-left: 4rpx;
  font-size: 20rpx;
}

.layout-scroll-item .goods-image {
  width: 176rpx;
  height: 176rpx;
  padding-top: 0;
}

.layout-scroll-item .goods-image .img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.layout-scroll-item .plus {
  height: 32rpx;
  width: 32rpx;
  font-size: 32rpx;
}

/* 20瀑布流布局样式 */
.layout-20-item .goods-image {
  padding-top: 130%;
}
