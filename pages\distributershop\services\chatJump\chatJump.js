// pages/distributershop/services/chatTools/chatTools.js
import Auth from '../../../../auth/index';
const app = getApp()
Page({
  data: {
    qyLogin: false,
    qyEntry: false,
    userId: '',

  },
  onLoad: async function(options) {
    await app.waitSid()
    this.getCard()
  },
  async getCard() {
    if (!await this.checkQy()) return
    this.setLoading(1)
    await this.getCurExternalContact()
    this.setLoading(0)
    if (!this.data.userId) {
      wx.hideLoading()
      wx.showToast({
        title: '未获取到userid' + this.data.userId + '11111',
        icon: 'none'
      })
      return
    }
    this.setLoading(1)
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/customer/getCusIdByExterUserId', {
      exterUserId: this.data.userId
    }, res => {
      this.setLoading(0)
      if (res.success && res.data) {
        wx.redirectTo({
          url: '../user/user?cusid=' + res.data
        })
      } else {
        app.util.reqFail(res)
      }
    })
  },
  auth() {
    Auth()
  },
  checkQy(type) {
    return new Promise(resolve => {
      let sys = wx.getSystemInfoSync()
      if (sys.environment == 'wxwork') {
        let caniuse = wx.qy.canIUse('getEnterpriseUserInfo')
        if (this.data.qyLogin) resolve(true)
        else {
          wx.qy.login({
            success: res2 => {
              if (res2.code) {
                app.reqPost('ms-sanfu-wechat-customer/customer/wxWork/wxMiniAppLogin', {
                  sid: wx.getStorageSync('sid'),
                  appid: app.globalData2.appid,
                  code: res2.code
                }, res => {
                  if (res.success) {
                    wx.setStorageSync('qysid', res.userId)
                    this.setData({
                      qyLogin: true,
                      qysid: res.userId
                    })
                    // this.data.userId = res.userId
                    resolve(true)
                  } else {
                    resolve(false)
                    app.util.reqFail(res)
                  }
                })
              } else {
                resolve(false)
                wx.showModal({
                  content: '登录失败' + JSON.stringify(res),
                  showCancel: false
                })
              }
            },
            fail: res => {
              resolve(false)
              wx.showModal({
                content: '登录失败' + JSON.stringify(res),
                showCancel: false
              })
            }
          })
        }

      } else {
        if (!type)
          wx.showModal({
            content: '请在企业微信内打开',
            showCancel: false
          })
        resolve(false)
      }
    })

  },
  testqy2() {
    wx.qy.selectEnterpriseContact({
      fromDepartmentId: 0, // 必填，-1表示打开的通讯录从自己所在部门开始展示, 0表示从最上层开始
      mode: "multi", // 必填，选择模式，single表示单选，multi表示多选
      type: ["department", "user"], // 必填，选择限制类型，指定department、user中的一个或者多个
      success: function(res) {
        console.log(res)
        var selectedDepartmentList = res.result.departmentList; // 已选的部门列表
        for (var i = 0; i < selectedDepartmentList.length; i++) {
          var department = selectedDepartmentList[i];
          var departmentId = department.id; // 已选的单个部门ID
          var departemntName = department.name; // 已选的单个部门名称
        }
        var selectedUserList = res.result.userList; // 已选的成员列表
        for (var i = 0; i < selectedUserList.length; i++) {
          var user = selectedUserList[i];
          var userId = user.id; // 已选的单个成员ID
          var userName = user.name; // 已选的单个成员名称
          var userAvatar = user.avatar; // 已选的单个成员头像
        }
      }
    });
  },
  async syncExternal() {
    wx.vibrateShort()
    if (!await this.checkQy()) return
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/wechatWork/syncExternal', {
      sid: wx.getStorageSync('sid'),
      userId: this.data.userId
    }, res => {
      wx.hideLoading()
      if (res.success) {
        this.data.list[0].page = 1
        this.data.list[0].has_more = 0
        this.data.list[0].data = []
        this.data.list[2].page = 1
        this.data.list[2].has_more = 0
        this.data.list[2].data = []
        this.getList()
      }
      app.util.reqFail(res)
    })

  },
  async getExternal() {
    wx.vibrateShort()
    if (!await this.checkQy()) return
    wx.qy.selectExternalContact({
      filterType: 0, //0表示展示全部外部联系人列表，1表示仅展示未曾选择过的外部联系人。默认值为0；除了0与1，其他值非法。
      success: (res) => {
        console.log(res)
        var userIds = res.userIds.join(',') // 返回此次选择的外部联系人userId列表，数组类型
        this.saveExternal(userIds)
      },
      fail: res => {
        if (res.errMsg != 'qy.selectExternalContact:fail cancel')
          wx.showModal({
            content: JSON.stringify(res),
            showCancel: false
          })
      }
    });
  },
  getCurExternalContact() {
    return new Promise(res => {
      wx.qy && wx.qy.getCurExternalContact({
        success: (res2) => {
          // wx.showModal({
          //   content: JSON.stringify(res2)
          // })
          this.data.userId = res2.userId //返回当前外部联系人userId
          this.setData({
            userId: res2.userId
          })
          res(res2.userId)
        },
        fail: res2 => {
          wx.hideLoading()
          wx.showModal({
            content: JSON.stringify(res2)
          })
        }
      })
    })
  },
  saveExternal(ids) {
    wx.showLoading()
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/wechatWork/saveExternal', {
      sid: wx.getStorageSync('sid'),
      userIds: ids
    }, res => {
      wx.hideLoading()
      if (res.success) {
        // wx.showToast({
        //   title: '添加成功'
        // })
        this.data.list[this.data.currentTab].page = 1
        this.data.list[this.data.currentTab].has_more = 0
        this.data.list[this.data.currentTab].data = []
        this.getList()
      } else {
        app.util.reqFail(res)
      }
    })
  },
  async openOptions(e) {
    let id = this.data.userId
    if (!id) return
    wx.vibrateShort()
    if (!await this.checkQy()) return
    wx.showActionSheet({
      itemList: ['查看个人信息', '创建会话', '发送注册', '444', '555'],
      success: res => {
        if (res.tapIndex == 0)
          this.openUserProfile(id)
        else if (res.tapIndex == 1)
          this.openEnterpriseChat(id)
        else if (res.tapIndex == 2)
          this.sendChatMessage(id)
        else if (res.tapIndex == 3)
          this.shareToExternalContact(id)
        else if (res.tapIndex == 4)
          this.shareToExternalChat(id)

      },
    })
  },
  openUserProfile(id) {
    //打开个人信息页
    wx.qy.openUserProfile({
      type: 2, //1表示该userid是企业成员，2表示该userid是外部联系人
      userid: id, //可以是企业成员，也可以是外部联系人
      success: function(res) {
        // 回调
      }
    });
  },
  openEnterpriseChat(id) {
    //创建会话
    wx.qy.openEnterpriseChat({
      // 注意：userIds和externalUserIds至少选填一个，且userIds+externalUserIds总数不能超过2000，如果externalUserIds有微信联系人，则总数不能超过40人。
      userIds: 'zhangsan;lisi;wangwu', //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
      externalUserIds: id, // 参与会话的外部联系人列表，格式为userId1;userId2;…，用分号隔开。
      groupName: '讨论组', // 必填，会话名称。单聊时该参数传入空字符串""即可。
      success: function(res) {
        // 回调
      },
      fail: function(res) {
        // 失败处理
      }
    });
  },
  sendChatMessage(id) {
    // 通过聊天工具栏向当前会话发送消息，支持多种消息格式，包括文本(“text”)，图片(“image”)，视频(“video”)，文件(“file”)、H5(“news”）和小程序(“miniprogram”)。
    wx.qy.sendChatMessage({
      msgtype: "miniprogram", //消息类型，必填
      // text: {
      //   content: "你好", //文本内容
      // },
      // image: {
      //   mediaid: "", //图片的素材id
      // },
      // video: {
      //   mediaid: "", //视频的素材id
      // },
      // file: {
      //   mediaid: "", //文件的素材id
      // },
      // news: {
      //   link: "", //H5消息页面url 必填
      //   title: "", //H5消息标题
      //   desc: "", //H5消息摘要
      //   imgUrl: "", //H5消息封面图片URL
      // },
      miniprogram: {
        appid: "wxfe13a2a5df88b058", //小程序的appid
        title: "注册", //小程序消息的title
        imgUrl: "/static/logo.jpg", //小程序消息的封面图
        page: "/pages/register/register.html", //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
      },
      success: function(res) {
        wx.showModal({
          content: 'success' + JSON.stringify(res)
        })
        //todo:
      },
      fail: res => {
        wx.showModal({
          content: 'fail' + JSON.stringify(res)
        })
      }
    });
  },
  shareToExternalContact() {
    // 客户群发 此接口支持企业成员把小程序，传递到群发助手进行发送。
    // 为了防止滥用，同一个成员每日向一个客户最多可群发一条消息，每次群发最多可选200个客户。
    wx.qy.shareToExternalContact({
      appid: "wx8bd80126147df384", //小程序的appid
      title: "this is title", //小程序消息的title
      imgUrl: "/appData/pic/pic1.jpg", //小程序消息的封面图
      page: "/index/page.html", //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
      success: function(res) {
        //todo:
      }
    });
  },
  shareToExternalChat() {
    // 客户群群发 此接口支持企业成员把小程序，传递到群发助手进行发送。
    // 为了防止滥用，同一个成员每日向一个客户群最多可群发一条消息，每次群发最多可选200个最近活跃的客户群。
    wx.qy.shareToExternalChat({
      appid: "wx8bd80126147df384", //小程序的appid
      title: "this is title", //小程序消息的title
      imgUrl: "/appData/pic/pic1.jpg", //小程序消息的封面图
      page: "/index/page.html", //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
      success: function(res) {
        //todo:
      }
    });
  },
  async getContext() {
    if (!await this.checkQy(true)) return
    this.setData({
      qyEntry: true //返回进入小程序的入口类型
    })
    // 值	说明
    // contact_profile	从联系人详情进入
    // single_chat_tools	从单聊会话的工具栏进入
    // group_chat_tools	从群聊会话的工具栏进入
    // chat_attachment	从聊天附件栏进入
    // normal	除以上场景之外进入，例如工作台，聊天会话等
    wx.qy.getContext({
      success: (res) => {
        this.setData({
          qyEntry: res.entry //返回进入小程序的入口类型
        })
      },
      fail: res2 => {
        wx.showModal({
          content: JSON.stringify(res2)
        })
      }
    })
  }
})
