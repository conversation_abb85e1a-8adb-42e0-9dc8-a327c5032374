/* pages/distributershop/shareGoods/shareGoods.wxss */
page {
  display: flex;
  flex-direction: column;
}

.top {
  position: sticky;
  top: 0;
  background: #fff;
  min-height: 110rpx;
}

.searchbox {
  height: 70rpx;
  background-color: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
  padding-left: 20rpx;
  border: 2rpx solid #8b3dff;
  margin: 0 10rpx;
}

.searchbox input {
  flex: 1;
  height: 100%;
  border: none;
  min-height: auto;
  display: inline-block;
  color: #fff;
  font-size: 30rpx;
  color: #333;
}

.searchbox .confirm {
  background: #8b3dff;
  padding: 0 20rpx;
  height: 100%;
  line-height: 70rpx;
  color: #fff;
}

.goods-item {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
  height: 200rpx;
  padding-left: 20rpx;
}

.goods-item .imgs {
  width: 200rpx;
  height: 200rpx;
  position: relative;
}

.goods-item .imgs swiper {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.goods-item .imgs swiper image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.imgsCount {
  position: absolute;
  z-index: 9;
  bottom: 20rpx;
  right: 20rpx;
  background: rgba(40, 40, 40, 0.7);
  padding: 12rpx 25rpx 12rpx 25rpx;
  font-size: 26rpx;
  color: #fff;
  border-radius: 10rpx;
}

.goods-item .right {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.goods-item .right text {
  flex: 1;
  padding: 0 20rpx;
}

.goods-item .right view {
  display: flex;
  height: 80rpx;
}

.goods-item .right view view {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin: 0 16rpx;
  background: #4bd847;
  color: #fff;
  border-radius: 8rpx;
}

.share-title {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.share-title>text {
  font-size: 36rpx;
  color: #333;
  display: flex;
  align-items: center;
  font-weight: 700;
}

.shopInput {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}

.shopInput input {
  border: 2rpx solid #8b3dff;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  height: auto;
  width: 200rpx;
  font-size: 28rpx;
}

.shopInput view {
  border-radius: 12rpx;
  background: #8b3dff;
  padding: 12rpx 24rpx;
  color: #fff;
  margin-left: 16rpx;
  font-size: 28rpx;
}

.shop {
  display: flex;
  border-bottom: 2rpx solid #aaacad;
  align-items: center;
  background: #ebf1e1;
  height: 70rpx;
}

.shop:nth-of-type(2n+1) {
  background: #e3ecf1;
}

.shop .title {
  padding: 0 20rpx;
  flex: 1;
}

.shop .share {
  display: flex;
  align-items: center;
  background: #4bd847;
  color: #fff;
  height: 100%;
  padding: 0 40rpx;
  border-radius: 0;
}

.shop .delete {
  display: flex;
  align-items: center;
  background: #f00;
  color: #fff;
  height: 100%;
  padding: 0 40rpx;
}

.popup-box {
  width: 600rpx;
  min-height: 400rpx;
  background: #fff;
  margin-bottom: 100rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
}

.popup-box .title {
  flex: 1;
  padding: 30rpx;
  padding-bottom: 0;
  font-size: 32rpx;
}

.popup-box textarea{
  border: 2rpx solid #74c3ff;
  height: 200rpx;
  width: 560rpx;
  padding: 12rpx;
  border-radius: 8rpx;
  margin: 20rpx;
}

.popup-box .btns {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.popup-box .btns view,
.popup-box .btns button {
  height: 80rpx;
  flex: 1;
  background: #4bd847;
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  border-radius: 0;
}

.popup-box .btns view {
  background: #abb;
  position: relative;
}

.popup-box .btns view:first-of-type {
  background: #aaa;
}

.toshare {
  border-radius: 12rpx;
  background: #4bd847;
  padding: 12rpx 24rpx;
  color: #fff;
  margin-left: 16rpx;
  font-size: 28rpx;
}


.reset {
  border-radius: 12rpx;
  background: #ff4f0a;
  padding: 12rpx 24rpx;
  color: #fff;
  margin-left: 16rpx;
  font-size: 28rpx;
}

.previewBox {
  width: 490rpx;
  padding: 20rpx;
  border-radius: 20rpx;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #f1f1f1;
}

.previewBox .title {
  margin-bottom: 10rpx;
  font-size: 30rpx;
}

.previewBox .imgbox {
  width: 450rpx;
  height: 360rpx;
  overflow: hidden;
  border-radius: 8rpx;
  position: relative;
}

.previewBox .imgbox1::before{
  content: '点此上传图片';
  background: #99bed0;
  width: 100%;
  height: 100%;
  position: absolute;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.previewBox .imgbox image{
  width: 450rpx;
  height: 360rpx;
  z-index: 2;
}