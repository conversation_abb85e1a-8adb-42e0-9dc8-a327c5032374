const app = getApp()
import Share from '../../../../components/common/share/index.js'
import shareCanvas from '../../../../utils/shareCanvas.js'
Page({
  data: {
    cusid: '',
    discard: '',
    current: 0,
    list: [],
    has_more: 0,
    page: 1,
    loadingText: ['点击加载更多', '正在加载...', '没有更多了~'],
    shareItem: '',
    showqrcode: true,
  },
  onLoad: function(options) {
    if (options.cusid) {
      this.setData({
        discard: options.discard,
        cusid: options.cusid
      })
    }
    this.getList()
  },
  onShow: function() {},
  onPullDownRefresh: function() {
    this.setData({
      list: [],
      has_more: 0,
      page: 1
    })
    this.getList()
    wx.stopPullDownRefresh()
  },
  ...shareCanvas,
  getList: async function() {
    if (this.data.has_more != 0) return
    this.setData({
      has_more: 1
    })
    let res = await app.reqGet('ms-sanfu-wap-goods/listRecommendShopPromo4Guide', {
      sid: wx.getStorageSync('sid'),
      shoId: app.local.get('sho_id') || wx.getStorageSync('sho_id'),
      cusId: this.data.cusid
    })
    if (res.success) {
      let data = res.data.length > 0 && res.data || []
      this.data.list = [...this.data.list, ...data]
      this.data.has_more = 2

      this.setData({
        list: this.data.list,
        has_more: this.data.has_more
      })
    } else {
      this.setData({
        has_more: 0
      })
      app.util.reqFail(res)
    }
  },
  closeQR() {
    this.setData({
      showqrcode: true
    })
  },
  showShare(e) {
    console.log('showShare')
    let i = e.currentTarget.dataset.index
    let item = this.data.list[i]
    let isQy = 0
    let endUrl = `/pages/goodsList/goodsList?promo_dtl_id_1=${item.promo_dtl_id}&navTitleName=${item.promo_name}`
    if (wx.getSystemInfoSync().environment == 'wxwork') {
      endUrl += '&from=qymall'
      isQy = 1
    }
    this.data.shareItem = {
      title: item.promo_name,
      path: endUrl + `&disshop=${app.local.get('sho_id')||''}&discard=${wx.getStorageSync('discard')||''}`,
      imageUrl: item.imgs[0],
    }
    Share().then(el => {
      console.log('微信好友')
    }).catch(el => {
      this.sharecanvas({
        title1: item.promo_name,
        title2: `${item.startTime} - ${item.endTime}`,
        wxpath: endUrl,
        imgList: [item.imgs[0] || ''],
      })
    })
  },
  saveImage: function() {
    // console.log(1111)
    let that = this
    wx.saveImageToPhotosAlbum({
      filePath: that.data.tmppath,
      success: function(data) {
        wx.showToast({
          title: "保存成功"
        })
      },
      fail(err) {
        wx.hideLoading()
        wx.hideToast()
        if (err.errMsg === "saveImageToPhotosAlbum:fail:auth denied" || err.errMsg === "saveImageToPhotosAlbum:fail auth deny" || err.errMsg === "saveImageToPhotosAlbum:fail authorize no response") {
          // 这边微信做过调整，必须要在按钮中触发，因此需要在弹框回调中进行调用

          wx.showModal({
            title: '提示',
            content: '需要您授权保存相册',
            success: res => {
              if (res.confirm)
                wx.openSetting({
                  success(settingdata) {
                    console.log("settingdata", settingdata)
                    if (settingdata.authSetting['scope.writePhotosAlbum']) {
                      wx.showToast({
                        title: '获取权限成功,再次点击图片即可保存',
                        icon: 'none',
                        duration: 3000

                      })
                    } else {
                      wx.showToast({
                        title: '获取权限失败，将无法保存到相册哦~',
                        icon: 'none',
                        duration: 3000

                      })

                    }
                  },

                })
            }
          })
        } else if (err.errMsg == "saveImageToPhotosAlbum:fail cancel") {
          wx.showToast({
            title: '已取消保存~',
            icon: 'none',
            duration: 2000

          })
        } else {
          wx.showToast({
            title: '保存失败：' + err.errMsg,
            icon: 'none',
            duration: 5000
          })
        }
        console.log(err, that.data.tmppath)
      }
    })
  },
  onShareAppMessage: function(options) {
    let shareObj = this.data.shareItem
    if (!shareObj || options.from == 'menu') {
      let pages = getCurrentPages()
      pages = pages[pages.length - 1]
      let route = pages.route + `?cusid=${this.data.cusid}&disshop=${app.local.get('sho_id')||''}&discard=${wx.getStorageSync('discard')||''}`
      if (wx.getSystemInfoSync().environment == 'wxwork')
        route += '&from=qymall'
      shareObj = {
        title: '活动推荐',
        path: route
      }
    }
    // 返回shareObj
    return shareObj;
  },
  toList(e) {
    let id = e.currentTarget.dataset.id
    let title = e.currentTarget.dataset.title || ''
    app.toH5(`goods/goodsList?promo_dtl_id_1=${id}&navTitleName=${title}`);
  }

})
