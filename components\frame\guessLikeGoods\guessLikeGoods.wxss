.goo-container {
  width: 100%;
  margin-top: 18rpx;
  padding: 0;
  position: relative;
}

.leftfall,
.rightfall {
  position: relative;
  width: calc(50% - 15rpx);
  display: inline-block;
  vertical-align: top;
}

.goo-explanation {
  position: relative;
  margin-top: 4rpx;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #F39800;
  font-size: 20rpx;
  width: calc(100% - 50rpx);
}

.goo-explanation>text {
  text-align: left;
  font-size: 20rpx;
  color: #FF4133;
  padding: 0 8rpx;
  background: rgba(255, 65, 51, 0.04);
  box-sizing: border-box;
  height: 20rpx;
  border-radius: 5rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.guessTitle {
  font-size: 30rpx;
  color: #333;
  break-after: avoid;
  font-weight: bolder;
  text-align: center;
  margin-bottom: 30rpx;
  height: 30rpx;
  line-height: 30rpx;
  letter-spacing: 5rpx;
  position: relative;
}

.mlr20 {
  margin: auto 20rpx;
  font-size: 28rpx;
  color: #333333;
}

.guessTitle .mlr20 {
  font-size: 30rpx;
}

.addBtn {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(to left, #E60012, #FF7956);
  border-radius: 50%;
  line-height: 48rpx;
  text-align: center;
  position: relative;
  position: absolute;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.addBtnTxt {
  transform: rotate(45deg);
  display: inline-block;
  color: #fff;
  font-size: 40rpx;
  position: absolute;
  left: 0;
  right: 0;
  cursor: progress;
  top: 0;
  bottom: 0;
  margin: auto;
}


.flipVertical {
  transform: scaleX(-1);
  width: 40rpx;
  height: 28rpx;
}

.guessTabIcon {
  width: 40rpx;
  height: 28rpx;
}
