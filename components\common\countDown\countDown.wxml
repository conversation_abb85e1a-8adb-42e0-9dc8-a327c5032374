<wxs module="handler" src="./countDown.wxs"></wxs>
<view wx:if="{{type==1}}" class="qiangBox">
  <image class="seckillIcon" src="https://img.sanfu.com/sf_access/uploads/hbpoVxJ8UKZZIFRWBT0wxnwzzcZhcfar.png"></image>
  <view class="main">
    <block wx:if="{{days}}">
      <text class="count-down-item">{{days}}天</text>
      <text class="dot">:</text>
    </block>
    <text class="count-down-item">{{hours<10?'0':''}}{{hours}}</text>
    <text class="dot">:</text>
    <text class="count-down-item">{{minutes<10?'0':''}}{{minutes}}</text>
    <text class="dot">:</text>
    <text class="count-down-item">{{seconds<10?'0':''}}{{seconds}}</text>
  </view>
</view>
<block wx:elif="{{type==2}}"> <block wx:if="{{days}}"> {{days}}天</block>{{hours<10?'0':''}}{{hours||0}}时{{minutes<10?'0':''}}{{minutes||0}}分{{seconds<10?'0':''}}{{seconds||0}}秒</block>

<view class="type3" wx:elif="{{type==3}}">
  <view class="type3-item">{{hours<10?'0':''}}{{hours}}</view>
  <text class="type3-dot">:</text>
  <view class="type3-item">{{minutes<10?'0':''}}{{minutes}}</view>
  <text class="type3-dot">:</text>
  <view class="type3-item">{{seconds<10?'0':''}}{{seconds}}</view>
</view>

<block wx:else>
  <slot wx:if="{{ useSlot }}" />
  <block wx:else>{{ formattedTime }}</block>
</block>
