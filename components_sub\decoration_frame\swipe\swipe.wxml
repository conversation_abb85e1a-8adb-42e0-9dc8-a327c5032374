<wxs src="../../../utils/utils.wxs" module="util"></wxs>
<view wx:if="{{unitData.list&&unitData.list.length&&show}}" class="img-box" style="z-index:{{index+1}};margin: {{config.pTB}}rpx {{config.pLR}}rpx;width:calc(100% - {{2 * config.pLR||0}}rpx);{{styles}}">
  <!-- 样式1：普通轮播 -->
  <swiper wx:if="{{config.sType == 1 || !config.sType}}" style="width: 100%; height: {{config.h}}rpx;border-radius: {{config.radius}}rpx;overflow:hidden;" autoplay="{{config.interval!=0}}" circular interval="{{config.interval || 4500}}" duration="{{config.duration || 300}}" class="l-box" bindchange="swiperChange" indicator-dots="{{config.indicators==1}}" indicator-color="rgba(0,0,0,.3)" indicator-active-color="#ffffff" current="{{cindex}}">
    <swiper-item wx:for="{{unitData.list}}" wx:for-item="item1" wx:key="index">
      <image lazy-load mode="aspectFill" style="width: 100%;height: 100%;border-radius: {{config.radius}}rpx;" src="{{util.jpg2jpeg(item1.imgUrl)}}" bindtap="toH5" data-i="{{index}}">
      </image>
    </swiper-item>
  </swiper>

  <!-- 样式2：背景轮播 -->
  <view wx:elif="{{config.sType == 2}}" class="type2-wrap" style="height: {{config.h}}rpx;">
    <image wx:for="{{unitData.list}}" wx:key="index" class="bg-img {{index==cindex?'active':''}}" src="{{util.jpg2jpeg(item.imgUrl)}}" style="transition: all {{config.duration/1.2 || 250}}ms" />
    <view class="swipe-box" style="border-radius: {{config.radius}}rpx; width: {{config.subW}}rpx; height: {{config.subHType>=2?'calc('+config.h + 'rpx - '+(config.subHType==2?116:20)+'rpx - ' + (config.subBottom + config.subTop) + 'rpx - '+ (menuButton.t) +'px)':config.subH + 'rpx'}}; bottom: {{config.subBottom}}rpx;border-radius: 20rpx;">
      <swiper class="swipe" style="border-radius: {{config.radius}}rpx; height:{{config.h}}rpx;width: {{750 - config.pLR * 2}}rpx; left:-{{(750 - config.subW)/2 - config.pLR}}rpx; top: calc({{config.subHType>=2?'-'+(config.subHType==2?116:20)+'rpx - ' + config.subTop + 'rpx - '+ (menuButton.t) +'px':config.subH + 'rpx'}});" circular interval="{{config.interval || 4500}}" duration="{{config.duration || 300}}" bindchange="swiperChange" indicator-dots="{{false}}" autoplay="{{config.interval!=0}}">
        <swiper-item wx:for="{{unitData.list}}" wx:key="index">
          <image lazy-load mode="aspectFill" style="width: 100%;height: 100%;" src="{{util.jpg2jpeg(item.imgUrl)}}" bindtap="toH5" data-i="{{index}}">
          </image>
        </swiper-item>
      </swiper>
    </view>
  </view>

  <!-- 样式3：渐变轮播 -->
  <view wx:elif="{{config.sType == 3}}" class="type3-wrap" style="height: {{config.h}}rpx;border-radius: {{config.radius}}rpx;">
    <view class="fade-swiper" style="height: 100%;">
      <image wx:for="{{unitData.list}}" wx:key="index" class="fade-img {{index==cindex?'active':''}}" src="{{util.jpg2jpeg(item.imgUrl)}}" style="transition: opacity {{config.duration || 300}}ms ease-in-out;{{index!=cindex?'pointer-events: none;':''}}" bindtap="toH5" data-i="{{index}}">
      </image>
    </view>
  </view>


  <!-- 样式4：背景轮播 -->
  <view wx:elif="{{config.sType == 4}}" class="type2-wrap" >
    <view wx:for="{{unitData.list}}" wx:key="index" class="bg-img {{index==cindex?'active':''}}" style="transition: all {{config.duration/2 || 250}}ms;background: {{item.hexColor}}" />
    <view style="height: calc({{menuButton.t + 'px + ' + (config.subHType === 2 ? 116 : config.subHType === 3 ? 20 : 0) + 'rpx'}});margin-bottom: {{config.subTop}}rpx;"></view>
    <view class="swipe-box4" style="border-radius: {{config.radius}}rpx; width: {{config.subW}}rpx;   height:{{config.subH}}rpx;">
      <swiper class="swipe" style="border-radius: {{config.radius}}rpx;width:100%;" circular interval="{{config.interval || 4500}}" duration="{{config.duration || 300}}" bindchange="swiperChange" indicator-dots="{{false}}" autoplay="{{config.interval!=0}}">
        <swiper-item wx:for="{{unitData.list}}" wx:key="index">
          <image lazy-load mode="aspectFill" style="width: 100%;height: 100%;" src="{{util.jpg2jpeg(item.imgUrl)}}" bindtap="toH5" data-i="{{index}}">
          </image>
        </swiper-item>
      </swiper>
    </view>
    <view style="height: {{config.subBottom}}rpx;"></view>
  </view>


  <view wx:if="{{ config.sType != 2}}" style="position: relative; z-index: 10;bottom: {{ config.sType === 4 && config.subBottom}}rpx">
    <!-- 渐变轮播指示器 -->
    <view class="fade-indicators" wx:if="{{unitData.list.length > 1&&config.indicators==1}}">
      <view wx:for="{{unitData.list}}" wx:key="index" class="fade-dot {{index==cindex?'active':''}}" bindtap="swiperChange" data-index="{{index}}">
      </view>
    </view>

    <!-- 指示器样式1 -->
    <view class="l-swiper-dot" wx:elif="{{unitData.list.length>1&&config.indicators==2}}">
      <view class="l-swiper-dot-item {{index==cindex?'l-swiper-active':''}}" wx:for="{{unitData.list}}" wx:key="index" id="dot{{index}}"></view>
    </view>

    <!-- 指示器样式2 -->
    <view class="swiper-dot" wx:elif="{{unitData.list.length>1&&config.indicators==3}}">
      <view class="swiper-dot-item {{index==cindex?'active':''}}" wx:for="{{unitData.list}}" wx:key="index"></view>
    </view>
  </view>
</view>
