const app = getApp()
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    config: {
      type: Object,
      value: {}
    },
    unitData: {
      type: Object,
      value: {
        list: []
      },
    },
    pid: {
      type: null,
      value: ''
    },
    index: {
      type: null,
      value: ''
    }
  },

  data: {
    currentPage: 0
  },
  lifetimes: {
    attached() {}
  },

  methods: {

    // 处理导航项点击
    handleItemClick(e) {
      const item = e.currentTarget.dataset.item
      try {
        app.sf.track('mallpage_click', {
          track_cfg_id: this.data.pid,
          track_title: this.data.config.unitName || '导航',
          text: item.trackMemo
        })
      } catch (e) {}
      if (item.link) {
        app.toH5(item.link)
      }
    },

    handlePageChange(e) {
      console.log(e)
      this.setData({ currentPage: e.currentPage })
    }
  }
})
