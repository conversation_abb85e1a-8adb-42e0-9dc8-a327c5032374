.loop-box {
  position: absolute;
  left: 40rpx;
  top: 100rpx;
  z-index: 666;
  overflow: hidden;
  /* background: #ff0; */
}

@keyframes ani {
  0% {
    transform: translateY(-100%);
    /* opacity: 0; */
  }

  5% {
    transform: translateY(0);
    opacity: 1;
  }

  95% {
    transform: translateY(0);
    opacity: 1;
  }

  100% {
    transform: translateY(100%);
    /* opacity: 0; */
  }
}

.loop {
  animation: ani 6s infinite linear;
  background: #e38596;
  color: #fff;
  padding: 6rpx 20rpx;
  line-height: 1;
  border-radius: 24rpx;
  font-size: 30rpx;
  transform: translateY(100%);
  /* opacity: 0; */
  z-index: 667;
}
