import QR from './lib/qrcode.js'
import BAR from './lib/barcode.js'
const app = getApp()
Component({
  options: {
    addGlobalClass: true
  },
  /**
   * @property {id} canvas ID
   * @property {value} 二维码/条形码内容
   * @property {type} 类型 二维码：qrcode,条形码barcode
   * @property {width} 组件样式，同 css 样式
   * @property {height} 组件样式，同 css 样式
   * @property {isImg} 是否展示为图片  canvas需要cover-view覆盖
   * @event {Function} callback 加载完成触发
   */
  properties: {
    id: {
      type: String,
      value: 'qr'
    },
    value: {
      type: String,
      value: '',
      observer: function(newVal, oldVal) {
        if (newVal) {
          this.createCode()
        }
      }
    },
    type: {
      type: String,
      value: 'qrcode'
    },
    width: {
      type: Number,
      value: 300
    },
    height: {
      type: Number,
      value: 300
    },
    fg: {
      type: String,
      value: '#000000'
    },
    bg: {
      type: String,
      value: '#ffffff'
    },
    isImg: {
      type: Boolean,
      value: false
    },
    canOpenImg: {
      type: <PERSON><PERSON>an,
      value: false
    }
  },
  data: {
    imgSrc: ''
  },
  attached() {
    this.createCode()
  },
  ready() {},
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {},
    hide: function() {},
    resize: function() {}
  },
  methods: {
    // 生成码
    createCode: async function() {
      if (!this.data.value) return
      const sys = wx.getSystemInfoSync()
      const rpx = sys.pixelRatio
      console.log(sys);
      if (!this.ctx) {
        this.ctx = await this.createCanvas(this.data.id, this.data.width * rpx, this.data.height * rpx)
      }
      console.log(this.ctx)

      // 获取设备像素比
      let pixelRatio = wx.getSystemInfoSync().pixelRatio;

      // 在生成二维码或条形码之前，设置canvas的宽高为实际显示大小的pixelRatio倍
      let canvasWidth = this.data.width * pixelRatio;
      let canvasHeight = this.data.height * pixelRatio;
      this.canvas.width = canvasWidth;
      this.canvas.height = canvasHeight;

      //调用插件中的draw方法，绘制二维码图片
      if (this.data.type === 'qrcode') {
        QR.api.draw(
          this.data.value,
          this.ctx,
          this.data.width * rpx,
          this.data.height * rpx,
          () => {
            if (this.data.isImg) this.canvasToTempImage(this.data.id)
            this.triggerEvent('callback')
          },
          this.data.fg,
          this.data.bg
        )
      } else {
        BAR.code128(
          this.data.value,
          this.ctx,
          this.data.width * rpx,
          this.data.height * rpx,
          () => {
            console.log('success')
            if (this.data.isImg) this.canvasToTempImage(this.data.id)
            this.triggerEvent('callback')
          },
          this.data.fg,
          this.data.bg
        )
      }
    },
    createCanvas(canvasId, width, height) {
      console.log(canvasId, width, height)
      if (!canvasId) {
        console.warn('初始化canvas-id不能为空')
        return
      }
      return new Promise(resolve => {
        this.createSelectorQuery()
          .select('#' + canvasId)
          .fields({
            node: true,
            size: true
          })
          .exec(res => {
            const canvas = res[0].node
            this.canvas = canvas
            const ctx = canvas.getContext('2d')
            canvas.width = width //本地图像的width
            canvas.height = height //本地图像的height

            resolve(ctx)
          })
      })
    },
    //获取临时缓存照片路径，存入data中
    canvasToTempImage: function(canvasId) {
      wx.nextTick(() => {
        wx.canvasToTempFilePath({
            canvas: this.canvas,
            success: res => {
              console.log('success')
              console.log(res)
              var tempFilePath = res.tempFilePath
              this.setData({
                imgSrc: tempFilePath
              })
            },
            fail: function(res) {
              console.log('fail')
              console.log(res)
            }
          },
          this
        )
      })
    },
    openImg() {
      if (!this.data.canOpenImg) return
      console.log(this.data.imgSrc);
      wx.previewImage({
        current: this.data.imgSrc, // 当前显示图片的http链接
        urls: [this.data.imgSrc] // 需要预览的图片http链接列表
      })
    }
  }
})
