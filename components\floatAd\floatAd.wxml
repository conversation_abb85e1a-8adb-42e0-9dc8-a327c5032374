<wxs src="../../utils/utils.wxs" module="utils"></wxs>
<view wx:if="{{isslot&&imgUrl}}" bindtap="tapbox" style="z-index: {{zIndex}};">
  <slot></slot>
</view>
<view wx:elif="{{imgUrl}}" style="position: fixed;right: {{marginRight}}rpx;bottom: {{marginBottom}}vh;z-index: {{zIndex}};display: flex;align-items: center;flex-direction: column;" bindtap="tapbox">
  <image src="{{utils.jpg2jpeg(imgUrl)}}" style="width: {{floatWide}}rpx;height: {{floatLength}}rpx;"></image>
  <view class="title">{{title}}</view>
  <view wx:if="{{enableClose}}" class="uni-icon uni-icon-closeempty close" catchtap="close"></view>
</view>
