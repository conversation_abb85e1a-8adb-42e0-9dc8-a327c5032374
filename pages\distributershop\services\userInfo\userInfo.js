const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cusid: '',
    info: {},//用户信息
  },
  onLoad: function(options) {
    if (options.cusid) {
      this.data.cusid= options.cusid
    } else {
      wx.showModal({
        content: '接收参数有误，请重试',
        showCancel: false,
        success: res => {
          wx.navigateBack({
            delta: 1
          })
        }
      })
      return
    }
  },
  onShow: function() {},
  onReady(){
    this.getInfo()
  },
  getInfo: function() {
    app.reqGet('ms-sanfu-wap-customer-distribution/assistant/customer/infomation', {
      sid: wx.getStorageSync('sid'),
      qysid: wx.getStorageSync('qysid'),
      curCusId: this.data.cusid
    }, res => {
      if (res.success) {
        this.setData({
          info:res.data
        })
      } else {
        app.util.reqFail(res)
      }
    })
  }
})
