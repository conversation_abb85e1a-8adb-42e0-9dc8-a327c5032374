const app = getApp()
Component({
  options: {
    virtualHost: true,
    multipleSlots: true
  },
  /**
   * Transition 自定义标题导航
   ** slot 普通填充（置顶)
      slot status  状态栏
      slot title  标题栏 ——需要showTitle开启
      slot placeholder  填充
   * @navStyle {String} 固定标题样式
   * @placeholderStyle {String} 填充样式
   * @titleStyle {String} 填充样式
   * @statusStyle {String} 填充样式
   * @isBack {Boolean} 显示返回按钮
   * @title {String} 标题内容
   * slotTitle {Boolean} 自定义标题（左右按按钮留边距)
   * fullTitle {Boolean} 完整自定义标题
   * @titleAlign {String} 标题对其方向[center|left|right]
   * @showTitle {Boolean} showTitle = [false|true] 显示标题栏
     @type {String} 类型  transparent为渐变，去除占位 需要额外处理
     .navInit
   */
  properties: {
    navStyle: {
      type: String,
      value: ""
    },
    navStyleTop: { //
      type: String,
      value: ""
    },
    placeholderStyle: {
      type: String,
      value: ""
    },
    titleStyle: {
      type: String,
      value: ""
    },
    defaultColor: {
      type: String,
      value: ""
    },
    defaultTitleColor: {
      type: String,
      value: ""
    },
    statusStyle: {
      type: String,
      value: ""
    },
    title: {
      type: String,
      value: ""
    },
    titleAlign: {
      type: String,
      value: "center"
    },
    showTitle: {
      type: Boolean,
      value: true
    },
    isBack: {
      type: Boolean,
      value: true
    },
    hideHome: {
      type: Boolean,
      value: false
    },
    slotTitle: {
      type: Boolean,
      value: false
    },
    fullTitle: {
      type: Boolean,
      value: false
    },
    type: { // transparent 未开发
      type: String,
      value: 'normal'
    },
    autoColor: {
      type: Boolean,
      value: false
    },
    hidetop: {
      type: Boolean,
      value: false
    },
  },
  data: {
    icon1: '\u{e664}',
    statusBarHeight: 0, //状态栏
    navBarHeight: 0, //主栏 不含状态栏
    menuWidth: 0, //胶囊宽度
    menuHeight: 0, //胶囊高度
    showHome: false,
    options: {
      threshold: 0.37,
      initialRatio: 0.8, // 相交比例
    },
    opacity: 1
  },
  attached() {

    let navbarInfo = wx.getStorageSync('navbarInfo') || {}
    if (navbarInfo.menuHeight > 0 && navbarInfo.navBarHeight > 0) {
      this.setData({
        statusBarHeight: navbarInfo.statusBarHeight,
        navBarHeight: navbarInfo.navBarHeight,
        menuWidth: navbarInfo.menuWidth,
        menuHeight: navbarInfo.menuHeight,
      })
    } else {
      if (wx.canIUse('getMenuButtonBoundingClientRect') && wx.canIUse('getSystemInfoSync')) {
        try {
          let sys = wx.getSystemInfoSync() || {}
          let menu = wx.getMenuButtonBoundingClientRect() || {}
          navbarInfo.statusBarHeight = sys.statusBarHeight || 20
          navbarInfo.navBarHeight = (menu.top - navbarInfo.statusBarHeight) * 2 + menu.height
          if (!sys.screenWidth && wx.canIUse('getWindowInfo')) {
            sys.screenWidth = wx.getWindowInfo().windowWidth
          }
          navbarInfo.menuWidth = (sys.screenWidth - menu.right) * 1.5 + menu.width
          navbarInfo.menuHeight = navbarInfo.navBarHeight * 0.7 > menu.height ? navbarInfo.navBarHeight * 0.7 : menu.height
          wx.setStorageSync('navbarInfo', navbarInfo)
          console.log(sys, menu)
          console.log('navbarInfo', navbarInfo)
        } catch (e) {
          navbarInfo = {
            statusBarHeight: 20,
            navBarHeight: 40,
            menuWidth: 0,
            menuHeight: 34
          }
        }
      } else {
        navbarInfo = {
          statusBarHeight: 20,
          navBarHeight: 40,
          menuWidth: 0,
          menuHeight: 34
        }
      }
      this.setData({
        statusBarHeight: navbarInfo.statusBarHeight || 20,
        navBarHeight: navbarInfo.navBarHeight || 40,
        menuWidth: navbarInfo.menuWidth || 0,
        menuHeight: navbarInfo.menuHeight || 34,
      })
    }
    this.triggerEvent('navInit', {
      statusBarHeight: this.data.statusBarHeight,
      navBarHeight: this.data.navBarHeight,
      menuWidth: this.data.menuWidth,
      menuHeight: navbarInfo.menuHeight,
      t: this.data.statusBarHeight + this.data.navBarHeight
    })
    let allpage = getCurrentPages()
    if (allpage.length == 1) {
      this.setData({
        showHome: true
      })
    }
  },
  detached() {},
  methods: {
    observereach(e) {
      this.setData({
        opacity: 1
      })
    },
    observernov(e) {
      this.setData({
        opacity: 0
      })
    },

    getHeight() {
      return {
        statusBarHeight: this.data.statusBarHeight,
        navBarHeight: this.data.navBarHeight,
        menuWidth: this.data.menuWidth,
        menuHeight: this.data.menuHeight,
      }
    },
    BackPage() {
      wx.navigateBack({
        delta: 1
      })
    },
    toHome() {
      wx.switchTab({
        url: '/pages/index/index',
        fail: () => {
          wx.switchTab({
            url: '/pages/ucenter_index/ucenter_index',
          })
        }
      })
    }
  }
});
