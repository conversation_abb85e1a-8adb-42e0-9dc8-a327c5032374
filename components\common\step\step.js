Component({
  options: {
    addGlobalClass: true,
  },
  /**
   * @index
   * @list {Array}展示列表
   */
  properties: {
    status: {
      type: Number,
      value: 0
    },
    list: {
      type: Array,
      value: []
    },
    list2: {
      type: Array,
      value: []
    },
    styles: {
      type: String,
      value: ''
    },
    color: {
      type: String,
      value: '#E60012'
    },
    color2: {
      type: String,
      value: '#E4E7ED'
    },
    color3: {
      type: String,
      value: '#fff'
    },
    color4: {
      type: String,
      value: '#c0c4cc'
    },
    fontStyle: {
      type: String,
      value: 'color:#909399;'
    },
    activefontStyle: {
      type: String,
      value: ''
    },
  },
  data: {},
  attached() {},
  detached() {},
  methods: {}
});
