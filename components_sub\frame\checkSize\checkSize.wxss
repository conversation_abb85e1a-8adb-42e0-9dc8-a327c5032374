@import "/font/icon_f7.wxss";

.select-wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.select-top {
  width: 100%;
  position: relative;
  padding: 30rpx;
  padding-bottom: 0;
  display: flex;
  align-items: center;
}

.select-top .goodImg {
  width: 200rpx;
  height: 200rpx;
  border-radius: 0.5em;
  margin-right: 30rpx;
}

.select-top .select-top-content {
  flex: 1;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}


.select-top .money-wrap {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
  font-weight: bold;
  color: #E60012;
}

.select-top .money-tag {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.select-top .money {
  font-size: 40rpx;
}

.select-top .barcode {
  font-size: 24rpx;
  color: #999999;
}


.select-top .select-info {
  color: #303133;
  margin-top: 16rpx;
  font-size: 26rpx;
}


.select-top .closeView {
  position: absolute;
  right: 0rpx;
  top: 0;
  padding: 24rpx 16rpx 24rpx 24rpx;
  font-size: 40rpx;
  color: #a0a0a0;
}


.promo-wrap {
  background: #ffe5e5;
  color: #E60012;
  margin: 0 30rpx;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  line-height: 1.25;
  margin-top: 16rpx;
}

.promo-wrap.disable {
  background: #F6F6F6;
  color: #747474;
  display: flex;
  align-items: center;
  line-height: 1;
}

.promo-wrap.disable .uni-icon {
  font-size: 30rpx;
  padding-top: 2rpx;
  margin-right: 6rpx;
  margin-bottom: -2rpx;
}


.select-main {
  margin-top: 10rpx;
  flex: 1;
  position: relative;
}

.select-main .scroll {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  padding: 0 30rpx;
}

.service-wrap {
  position: relative;
  padding: 12rpx 24rpx;
  background: #F2F2F2;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666666;
}

.service-wrap .uni-icon {
  font-size: 30rpx;
  padding-top: 2rpx;
  margin-right: 6rpx;
  margin-bottom: -2rpx;
  color: #747474;
}

.service-wrap .img {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.service-wrap .right-text {
  color: #999999;
  margin-left: auto;
}

.groupItem {
  background: #fef4e5;
  border-radius: 8rpx;
  padding: 8rpx 24rpx;
  font-size: 24rpx;
  color: #F39800;
  margin-top: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.groupItem:first-of-type {
  margin-top: 24rpx;
}

.groupItem .del {
  font-size: 24rpx;
  background: #fdeace;
  color: #F39800;
  border-radius: 50px;
  padding: 4rpx;
}

.main-title {
  margin-top: 48rpx;
  color: #333333;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1;
}


.select-item-wrap {
  margin-top: 8rpx;
  display: flex;
  flex-wrap: wrap;
  width: 720rpx;
}

.select-item {
  display: flex;
  align-items: center;
  border-radius: 8rpx;
  position: relative;
  color: #333;
  background: #F2F2F2;
  min-width: 104rpx;
  font-size: 24rpx;
  margin-top: 20rpx;
  margin-right: 24rpx;
  padding: 0 24rpx 0 0;
  border: 1px solid transparent;
  line-height: 1;
}


.select-item .img {
  width: 64rpx;
  height: 64rpx;
  border-radius: 8rpx 4rpx 4rpx 8rpx;
  margin-right: 24rpx;
  overflow: hidden;
}

.select-item-wrap .select-item.max:nth-of-type(3n) {
  margin-right: 0;
}

.select-item.max {
  flex-direction: column;
  padding: 0;
}

.select-item.max .img {
  width: 210rpx;
  height: 210rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  margin-right: 0;
}


.select-item.active {
  border-color: #E60012;
  background: rgba(230, 0, 18, 0.1);
}

.select-item.disable {
  background: #f5f5f5;
  color: #c0c0c0;
}

.select-item.disable .img {
  filter: contrast(0.7);
}

.tag {
  background: #E60012;
  position: absolute;
  top: -14rpx;
  right: -2rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  padding: 2rpx 8rpx;
  color: #fff;
  z-index: 2;
}

.tag.disable {
  background: #D3D1D1;
}

.tag.green{
  background:  #05be39;
}


.select-item.mini {
  padding: 8rpx 12rpx;
  justify-content: center;
}

.select-item.mini .tag {
  top: -16rpx;
  font-size: 20rpx;
}


.select-item.max .iconfangda {
  background: rgba(100, 100, 100, 0.35);
  color: #fff;
  border-radius: 50%;
  font-size: 30rpx;
  width: 40rpx;
  height: 40rpx;
  padding-top: 4rpx;
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  text-align: center;
}

.goo-color-item-titlebox {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;
  height: 64rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.active .goo-color-item-titlebox {
  background: rgba(230, 0, 18, 0.1);
}


.confirm {
  text-align: center;
  font-size: 30rpx;
  color: #fff;
  margin-left: 30rpx;
  width: 690rpx;
  display: flex;
  align-items: center;
  height: 76rpx;
  line-height: 1;
  box-sizing: content-box;
  padding: 12rpx 0;
}

.confirm .confirm-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  flex: 1;
  background: linear-gradient(to left, #FAC900, #F39800);
  border-radius: 30px;
  font-size: 32rpx;
  font-weight: 500;
}

.confirm .confirm-item:nth-of-type(2) {
  margin-left: 30rpx;
}

.amount-wrap {
  position: relative;
  padding: 48rpx 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.amount-wrap .color {
  height: 56rpx;
  line-height: 56rpx;
}

.rightBuy {
  background: linear-gradient(to left, #E60012, #FF7956) !important;
}


.sAmount {
  color: #666666;
  font-size: 24rpx;
  font-weight: 400;
  margin-left: 16rpx;
}




.limitTxt {
  text-align: right;
  margin-top: 10rpx;
  color: #E60012;
  font-size: 24rpx;
}

.multiple-wrap{
  color: #202020;
  font-size: 26rpx;
}

.multiple-wrap .sficon,.multiple-wrap .uni-icon{
   text-shadow:none;
   color:#000;
}

.multiple-wrap .sficon{
  margin-right: 12rpx;
  position: relative;
}

.multiple-wrap .sficon::after{
  content: '+';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-60%);
  color: #303133;
  font-size: 20rpx;
  zoom: 0.8;
}