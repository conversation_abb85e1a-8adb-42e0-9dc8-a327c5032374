<!--pages/distributershop/verifyImgDetail/verifyImgDetail.wxml-->
<sf-loading wx:if="{{loading}}" />
<swiper circular duration="500" style="height: 750rpx;" indicator-dots indicator-active-color="#e60012">
  <swiper-item wx:for="{{viewDetail.mainImgs}}" wx:key="index">
    <image mode="aspectFill" src="{{item}}" bindtap="openImg" data-index="{{index}}" style="width: 750rpx;height: 750rpx;">
    </image>
  </swiper-item>
</swiper>
<view wx:if="{{viewDetail.goodsPropsDto}}" class=" goodsDetailMiddle2">
  <view class="goodMessage">商品信息</view>
  <view class="goodContent">
    <view class="content">货号：</view>
    <view class="content">{{viewDetail.goodsSn}}</view>
    <view class="cl"></view>
    <view class="content">价格：</view>
    <view class="content">￥{{viewDetail.goodsPropsDto.price||''}}</view>
    <view class="cl"></view>
    <view class="content">季节：</view>
    <view class="content">{{viewDetail.goodsPropsDto.season}}</view>
    <view class="cl"></view>
    <view class="content">系列：</view>
    <view class="content">{{viewDetail.goodsPropsDto.netcollection}}</view>
    <view class="cl"></view>
    <view class="content">分类：</view>
    <view class="content">{{viewDetail.goodsPropsDto.category}}</view>
    <view class="cl"></view>
    <view class="content">风格：</view>
    <view class="content">{{viewDetail.goodsPropsDto.goostyle}}</view>
    <view class="cl"></view>
  </view>
</view>
<view wx:if="{{viewDetail}}" class="goodsDetailMiddle2">
  <view class="goodMessage">图文详情</view>
</view>
<wxParse wx:if="{{viewDetail}}" content="{{viewDetail.contents}}"></wxParse>
<view style="height: 170rpx; box-sizing: content-box;padding-bottom: env(safe-area-inset-bottom);"></view>
<view wx:if="{{viewDetail}}" class="footer">
  <block wx:if="{{viewDetail.isPass==0}}">
    <view class="tip">请认真核对图片和实物是否相符</view>
    <view class="btn btn1" hover-class="hover3" hover-start-time="20" hover-stay-time="100" bindtap="verifyImg" data-type="1">否决</view>
    <view class="btn btn2" hover-class="hover3" hover-start-time="20" hover-stay-time="100" bindtap="verifyImg" data-type="2">通过</view>
  </block>
  <view wx:else class="btn" style="background: {{viewDetail.isPass==2?'#e60012':'#a0a0a0'}};">审核{{viewDetail.isPass==2?'已':'未'}}通过</view>
</view>
