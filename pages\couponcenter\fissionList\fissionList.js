// pages/fissionList/fissionList.js
const app = getApp()
import Auth from '../../../auth/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    linkedCouponFissionList: [],
    isLoad: true,
    sid: wx.getStorageSync('sid'),
  },

  toDetail: function(e){
    console.log(e)
    let id = e.currentTarget.id
    wx.navigateTo({
      url: '/pages/couponcenter/fissionDetail/fissionDetail?id=' + id,
    })
  },

  async getFissionList(){
    let res = await app.reqGet('ms-sanfu-wechat-coupon/coupon/listCouponFission', {
      sid: wx.getStorageSync("sid"),
      shoId: wx.getStorageSync("dsho_id")
    })
    if (res.success) {
      let linkedCouponFissionList = res.data || []
      this.setData({
        linkedCouponFissionList,
        isLoad: false
      })
    } else {
      this.setData({
        isLoad: false
      })
      app.util.reqFail(res)
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      id: options.id
    })
    app.getLocation(1, (status) => {
      this.getFissionList()
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {}
})