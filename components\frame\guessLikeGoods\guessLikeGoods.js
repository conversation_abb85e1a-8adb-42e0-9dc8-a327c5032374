const app = getApp()
const computedBehavior = require('miniprogram-computed').behavior
import IntersectionObserver from '../../../utils/intersection-observer.js';
Component({
  options: {
    addGlobalClass: true
  },
  behaviors: [computedBehavior],
  properties: {
    shoID: {
      type: String,
      value: ''
    },
    showTitle: {
      type: Boolean,
      value: false
    },
    autoRefresh: {
      type: Boolean,
      value: false
    },
    lazy: {
      type: Boolean,
      value: false
    },
    onlyGetData: {
      type: Boolean,
      value: false
    },
    pageName: {
      type: String,
      value: ''
    }
  },
  data: {
    tabs: ['猜你喜欢'],
    list: [],
    has_more: 4,
    loadingText: ['点击加载更多', '正在加载...', '刷新推荐~'],
    skuShow: false,
    oldVersion: false
  },
  attached() {
    if (this.data.onlyGetData) return
    // 复写页面onReachBottom
    const allpage = getCurrentPages()
    const page = allpage[allpage.length - 1]
    const pageBottom = page.onReachBottom
    page.onReachBottom = () => {
      typeof pageBottom == 'function' && pageBottom()
      this.dealList()
    }
    if (!this.data.lazy) this.getList()
    const version = wx.getSystemInfoSync().SDKVersion
    if (app.util.compareVersion(version, '2.30.4') < 0) {
      this.setData({
        oldVersion: true
      })
    }
  },
  ready() {
    if (this.data.onlyGetData) return
    if (this.data.lazy) {
      //判断加载
      this.ob2 = new IntersectionObserver({
        selector: '#showonscreen',
        observeAll: true,
        context: this,
        delay: 10,
        threshold: 0.1,
        initialRatio: 0,
        viewport: {
          bottom: 300,
          top: 0
        },
        onFinal: args => {
          if (!args) return
          this.getList()
          this.ob2 && this.ob2.disconnect && this.ob2.disconnect()
        },
      })
    }
    this.ob2 && this.ob2.connect && setTimeout(() => {
      this.ob2.connect()
    }, 1500)
  },
  watch: {
    'shoID': function(newVal) {
      if (this.data.onlyGetData) return
      if (newVal && this.first) {
        this.data.has_more = 4
        this.allList = []
        this.getList()
      }
    },
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {
      if (this.data.onlyGetData) return
      if (this.data.autoRefresh && !this.limit2) {
        this.getList()
      }
      this.limit2 = false
    },
    hide: function() {
      if (this.data.onlyGetData) return
      if (this.data.autoRefresh && !this.limit2) {
        this.data.has_more = 4
        this.allList = []
      }
      this.ob2 && this.ob2.disconnect && this.ob2.disconnect()
    },
    resize: function() {},
  },
  methods: {
    startObserver() {
      const observer = this.selectComponent('#observer')
      if (observer && observer.startObserver) {
        observer.startObserver({
            selector: `.gitem`,
            observeAll: true,
            context: this,
            initialRatio: 0
          },
          1
        )
      }
    },
    observer(e) {
      const item = e.detail.item
      // 最近5个有相同的去重不再曝光
      // 用于记录最近曝光的5个商品ID，避免重复曝光
      if (!this._recentExposedGoods) {
        this._recentExposedGoods = []
      }
      // 检查当前商品是否已在最近5个曝光列表中
      let index = this._recentExposedGoods.findIndex(e => e === item.goodsSn)
      if (index > -1) {
        // 如果已曝光，则不再重复曝光，直接返回
        this._recentExposedGoods.splice(index, 1)
        return
      }
      // 如果未曝光，则加入曝光列表
      this._recentExposedGoods.push(item.goodsSn)
      // 保持列表最多只存5个
      if (this._recentExposedGoods.length > 5) {
        this._recentExposedGoods.shift()
      }
      // console.log(this._recentExposedGoods)
      // console.log('observer', item)
      app.report.exposeGoods({
        gooid: item.goodsSn,
        goodsName: item.goodsName,
        img: item.mImg,
        price_original: item.qiangPrice || item.groupBuyPrice ? item.salePrice || item.scPrice : item.scPrice,
        price_current: item.qiangPrice || item.groupBuyPrice || item.memberPrice || item.salePrice,
        text: '猜你喜欢-' + this.data.pageName
      })
    },
    async getList(pageSize, type, showImg = false) {
      this.first = 1
      if (this.data.has_more != 4 && (this.data.list.length > 0 || this.data.has_more != 0) && this.allList && this.allList.length > 0) return
      this.data.has_more = 1
      if (!this.data.onlyGetData)
        this.setData({
          has_more: 1
        })
      let url
      url = 'ms-sanfu-wap-goods/goods/recommend/ai'
      let request_id = wx.getStorageSync('cardid') + '_' + Date.now()
      // url = 'ms-sanfu-wap-goods/listGuessLikeGoods'
      const res = await app.reqGet(url, {
        sid: wx.getStorageSync('sid'),
        shoId: wx.getStorageSync('dsho_id'),
        sceneid: request_id,
        cid: 0
      })
      if (res.success) {
        let list = [],
          list1 = [],
          list2 = []
        for (let i in res.data) {
          if (res.data[i].type == 2) {
            list2.push(res.data[i])
          } else {
            list1.push(res.data[i])
          }
        }
        let j1 = 0
        for (let i in list2) {
          if (i > 0 && i % 3 == 0 && j1 < list1.length) {
            list.push(list1[j1])
            j1++
          }
          list.push(list2[i])
        }
        for (j1; j1 < list1.length; j1++) {
          list.push(list1[j1])
        }

        if (list.length % 2 != 0) list.pop()
        for (let i in list) {
          list[i].i = i
        }

        this.data.list = []
        this.allList = list
        this.data.has_more = 0
        // 插入入口列表
        console.log('showImgshowImgshowImg',showImg);
        if (showImg) {
          await this.getSecondPage()
          if (this.sList && this.sList.length) {
            let l = this.allList.length
            let i = 0
            while (this.sList.length > 0) {
              let item = this.sList.splice(0, 1)[0]
              item.type = 'img'
              this.allList.splice(i, 0, item)
              i = i + ((pageSize - 1) || 8)
            }
          }
        }
        if (!this.data.onlyGetData) {
          this.dealList(pageSize, type)
        } else {
          return await this.dealList(pageSize, type)
        }
      }
    },
    dealList2(e) {
      if (this.data.has_more == 2) {
        this.data.has_more = 4
        this.allList = []
        this.getList()
        return
      }
      this.dealList()
    },
    async dealList(pageSize = 10, type) {
      // type 1 换一换   showImg 合并图文

      if (this.data.has_more != 0) return
      if (this.allList && this.allList.length > 0) {
        let list = this.allList.splice(0, pageSize)
        if (!this.data.onlyGetData) {
          this.setData({
            list: [...this.data.list, ...list],
            has_more: this.allList.length > 0 ? 0 : 2
          })
          this.startObserver()
        } else {
          this.data.list = [...this.data.list, ...list]
          this.data.has_more = this.allList.length > 0 ? 0 : 2
          return {
            list: type ? list : this.data.list,
            has_more: this.data.has_more
          }
        }
      } else {
        if (!this.data.onlyGetData) {
          this.setData({
            has_more: 2
          })
        } else {
          this.data.has_more = 2
          return {
            has_more: this.data.has_more
          }
        }
      }
    },
    async getData(pageSize, type, refresh, showImg) {
      // type 1 换一换
      if (refresh) this.clearList()
      if (!this.allList || !this.allList.length) {
        return await this.getList(pageSize, type, showImg)
      } else return await this.dealList(pageSize, type, showImg)
    },
    clearList() {
      this.allList = []
      this.setData({
        list: [],
        has_more: 0
      })
    },
    async getSecondPage() {
      // 获取二级页信息透出
      if (this.loadSecond) return
      // const res = await app.reqGet('ms-sanfu-wap-goods/listGylSecondPage') 
      const res = await app.reqGet('ms-sanfu-wap-goods/listGyliEntrance', {
        shoId: wx.getStorageSync('dsho_id')
      })
      this.loadSecond = true
      this.sList = res.data || []
    },
    toGoodsDisplay(e) {
      this.limit2 = true
      let gooid = e.currentTarget.dataset.gooid
      let item = e.currentTarget.dataset.item
      app.toH5(`goods/goodsDisplay?goods_sn=${gooid}&g=1&f=猜你喜欢-${this.data.pageName}`);
    },
    showSelectToast(e) { // 打开选择弹框
      let item = e.currentTarget.dataset.item;

      app.$bus.emit('setSkuGoods', {
        text: '猜你喜欢-' + this.data.pageName,
        goods: item
      })
    }
  },
});
