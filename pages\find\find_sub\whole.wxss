/* 引用字体包 */


/*公共属性配置*/
/*主题渐变色*/
.theme-bg {
	background: linear-gradient(270deg, rgb(242, 40, 113), rgb(255, 106, 143)) !important;
}

.theme-white {
	background: #FFFFFF;
}

/* 行距 */
.line-height-05 {
	line-height: 0.5em;
}

.line-height-06 {
	line-height: 0.6em;
}

.line-height-07 {
	line-height: 0.7em;
}

.line-height-08 {
	line-height: 0.8em;
}

.line-height-09 {
	line-height: 0.9em;
}

.line-height-1 {
	line-height: 1em;
}

/* 边框线 */
.border-bottom-246 {
	border-bottom: solid rgb(246, 246, 246) 1rpx;
}

/* 阴影 */
.son-boxshadow {
	box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.06);
}

/* 按钮 */
.theme-btn-1 {
	border: none;
	color: #FFFFFF;
	line-height: 60rpx;
	font-size: 24rpx;
	border-radius: 40rpx;
	background-image: var(--themePink);
	width: 140rpx;
	padding: 0;
	margin: 0;
}

.theme-btn-2 {
	border: none;
	line-height: 60rpx;
	font-size: 24rpx;
	border-radius: 40rpx;
	border: solid #d6d6d6 1rpx;
	background: #ffffff;
	width: 140rpx;
	padding: 0;
	margin: 0;
}

.theme-btn-small {
	border: none;
	color: #FFFFFF;
	line-height: 60rpx;
	font-size: 26rpx;
	border-radius: 40rpx;
	background-image: var(--themePink);
	width: 140rpx;
	padding: 0;
	margin: 0;
	text-align: center;
}

.manjian {
	width: 70rpx;
	display: inline-block;
	text-align: center;
	line-height: 36rpx;
	font-size: 22rpx;
	color: #f32871;
	border-radius: 8rpx;
	border: solid #f32871 1rpx;
	box-sizing: border-box;
}

.tejia {
	width: 70rpx;
	display: inline-block;
	text-align: center;
	line-height: 36rpx;
	font-size: 22rpx;
	color: #ff8237;
	border-radius: 8rpx;
	border: solid #ff8237 1rpx;
	box-sizing: border-box;
}

button {
	position: relative !important;
	margin: 0;
	padding: 0;
	border: none;
	background: none;
}

button::after {
	border: none;
}

.button-hover {
	background: none;
	color: initial;
}

input {
	outline: none;
	border: none;
	list-style: none;
}

/* 文字居中 */
.text-align-centetr {
	text-align: center;
}

/*字体大小*/
.theme-fs-20 {
	font-size: 20rpx !important;
}

.theme-fs-22 {
	font-size: 22rpx;
}

.theme-fs-24 {
	font-size: 24rpx;
}

.theme-fs-26 {
	font-size: 26rpx;
}

.theme-fs-28 {
	font-size: 28rpx;
}

.theme-fs-30 {
	font-size: 30rpx;
}

.theme-fs-32 {
	font-size: 32rpx;
}

.theme-fs-34 {
	font-size: 34rpx;
}

.theme-fs-36 {
	font-size: 36rpx;
}

.theme-fs-38 {
	font-size: 38rpx;
}

.theme-fs-40 {
	font-size: 40rpx;
}

/*字体颜色*/

.theme-c {
	color: rgb(255, 39, 121);
}

.theme-c-f32871 {
	color: rgb(243, 40, 113);
}

.theme-c-green {
	color: #1ec522;
}

.theme-c-53 {
	color: rgb(53, 53, 53);
}

.theme-c-136 {
	color: rgb(136, 136, 136);
}

.theme-c-178 {
	color: rgb(178, 178, 178);
}

/* 背景颜色 */
.theme-bg-53 {
	background: rgb(53, 53, 53);
}

.theme-bg-178 {
	background: rgb(178, 178, 178);
}

.theme-c-225 {
	background: rgb(225, 225, 225);
}

.theme-c-240 {
	background: rgb(240, 240, 240);
}

.theme-c-248 {
	background: rgb(248, 248, 248);
}

.theme-c-255 {
	color: rgb(255, 255, 255);
}

.theme-bg-255 {
	background: rgb(255, 255, 255);
}

/*PingFang字体Medium字体*/

.theme-medium {
	font-family: 'PingFangSC-Medium';
}

/*分割线*/
.hr {
	height: 1px;
	background: #e1e1e1;
}

.foot-bar {
	min-height: 98rpx;
}

.border-radius-10 {
	border-radius: 10rpx;
}

.border-radius-12 {
	border-radius: 12rpx;
}

/*边缘化距离配置*/
.padding-36 {
	padding: 36rpx;
}

.ml_36 {
	margin-left: 36rpx;
}

.mr_36 {
	margin-right: 36rpx;
}

.pl_36 {
	padding-left: 36rpx;
}

.pr_36 {
	padding-right: 36rpx;
}

.pd-lr-36 {
	padding-right: 36rpx;
	padding-left: 36rpx;
}

.mg-lr-36 {
	margin-right: 36rpx;
	margin-left: 36rpx;
}

.pd-lr-45 {
	padding-right: 45rpx;
	padding-left: 45rpx;
}

.mg-lr-45 {
	margin-right: 45rpx;
	margin-left: 45rpx;
}

.pd-lr-56 {
	padding-right: 56rpx;
	padding-left: 56rpx;
}

.mg-lr-56 {
	margin-right: 56rpx;
	margin-left: 56rpx;
}

.mp100 {
	width: 100%;
}

.mp50 {
	width: 50%;
}

.mt_1 {
	margin-top: 1rpx;
}
.mt_5 {
	margin-top: 5rpx;
}
.mt_10 {
	margin-top: 10rpx;
}

.mt_20 {
	margin-top: 20rpx;
}

.mt_30 {
	margin-top: 30rpx;
}

.mt_40 {
	margin-top: 40rpx;
}

.mt_50 {
	margin-top: 50rpx;
}

.mt_60 {
	margin-top: 60rpx;
}

.mt_70 {
	margin-top: 70rpx;
}

.mt_80 {
	margin-top: 80rpx;
}

.mt_90 {
	margin-top: 90rpx;
}

.mt_100 {
	margin-top: 100rpx;
	height: 50rpx;
}

.mr_10 {
	margin-right: 10rpx;
}

.mr_20 {
	margin-right: 20rpx;
}

.mr_30 {
	margin-right: 30rpx;
}

.mr_40 {
	margin-right: 40rpx;
}

.mr_50 {
	margin-right: 50rpx;
}

.mr_60 {
	margin-right: 60rpx;
}

.mr_70 {
	margin-right: 70rpx;
}

.mr_80 {
	margin-right: 80rpx;
}

.mr_90 {
	margin-right: 90rpx;
}

.mr_100 {
	margin-right: 100rpx;
}

.mb_1 {
	margin-bottom: 1rpx;
}

.mb_10 {
	margin-bottom: 10rpx;
}

.mb_20 {
	margin-bottom: 20rpx;
}

.mb_30 {
	margin-bottom: 30rpx;
}

.mb_40 {
	margin-bottom: 40rpx;
}

.mb_50 {
	margin-bottom: 50rpx;
}

.mb_60 {
	margin-bottom: 60rpx;
}

.mb_70 {
	margin-bottom: 70rpx;
}

.mb_80 {
	margin-bottom: 80rpx;
}

.mb_90 {
	margin-bottom: 90rpx;
}

.mb_100 {
	margin-bottom: 100rpx;
}

.ml_10 {
	margin-left: 10rpx;
}

.ml_20 {
	margin-left: 20rpx;
}

.ml_30 {
	margin-left: 30rpx;
}

.ml_40 {
	margin-left: 40rpx;
}

.ml_50 {
	margin-left: 50rpx;
}

.ml_60 {
	margin-left: 60rpx;
}

.ml_70 {
	margin-left: 70rpx;
}

.ml_80 {
	margin-left: 80rpx;
}

.ml_90 {
	margin-left: 90rpx;
}

.ml_100 {
	margin-left: 100rpx;
}

.ml_150 {
	margin-left: 150rpx;
}

.ml_320 {
	margin-left: 320rpx;
}

.pt_10 {
	padding-top: 10rpx;
}

.pt_20 {
	padding-top: 20rpx;
}

.pt_30 {
	padding-top: 30rpx;
}

.pt_40 {
	padding-top: 40rpx;
}

.pt_50 {
	padding-top: 50rpx;
}

.pt_60 {
	padding-top: 60rpx;
}

.pt_70 {
	padding-top: 70rpx;
}

.pt_80 {
	padding-top: 80px;
}

.pt_90 {
	padding-top: 90rpx;
}

.pt_100 {
	padding-top: 100rpx;
}

.pr_10 {
	padding-right: 10rpx;
}

.pr_20 {
	padding-right: 20rpx;
}

.pr_30 {
	padding-right: 30rpx;
}

.pr_40 {
	padding-right: 40rpx;
}

.pr_50 {
	padding-right: 50rpx;
}

.pr_60 {
	padding-right: 60rpx;
}

.pr_70 {
	padding-right: 70rpx;
}

.pr_80 {
	padding-right: 80rpx;
}

.pr_90 {
	padding-right: 90rpx;
}

.pr_100 {
	padding-right: 100rpx;
}

.pb_10 {
	padding-bottom: 10rpx;
}

.pb_20 {
	padding-bottom: 20rpx;
}

.pb_30 {
	padding-bottom: 30rpx;
}

.pb_40 {
	padding-bottom: 40rpx;
}

.pb_50 {
	padding-bottom: 50rpx;
}

.pb_60 {
	padding-bottom: 60rpx;
}

.pb_70 {
	padding-bottom: 70rpx;
}

.pb_80 {
	padding-bottom: 80rpx;
}

.pb_90 {
	padding-bottom: 90rpx;
}

.pb_100 {
	padding-bottom: 100rpx;
}

.pl_10 {
	padding-left: 10rpx;
}

.pl_20 {
	padding-left: 20rpx;
}

.pl_30 {
	padding-left: 30rpx;
}

.pl_40 {
	padding-left: 40rpx;
}

.pl_50 {
	padding-left: 50rpx;
}

.pl_60 {
	padding-left: 60rpx;
}

.pl_70 {
	padding-left: 70rpx;
}

.pl_80 {
	padding-left: 80rpx;
}

.pl_90 {
	padding-left: 90rpx;
}

.pl_100 {
	padding-left: 100rpx;
}

/*隐藏显示*/
.off {
	display: none;
}

.on {
	display: block;
}

/*透明度*/

/*————————*/
/*私有样式*/
.index-nav .cu-item {
	font-size: 38rpx;
	font-weight: 500;
	color: rgb(178, 178, 178);
	height: 90rpx;
	display: inline-block;
	line-height: 90rpx;
	margin: 0 10rpx;
	padding: 0;
	letter-spacing: 4rpx;
	position: relative;
}

.index-nav .cu-item.cu-item.cur {
	color: rgb(53, 53, 53);
	font-family: 'PingFangSC-Medium';
	border: none;
}

.index-nav .cu-item.cu-item.cur:after {
	content: "";
	width: 40%;
	height: 6rpx;
	position: absolute;
	bottom: 0;
	left: 30%;
	border-radius: 100rpx;
	display: inline-block;
	background: rgb(243, 40, 113);
}

.index-nav .icon-sousuo {
	line-height: 90rpx;
	font-size: 38rpx;
}

.hot-topic,
.hot-topic .fr,
.hot-topic view:nth-child(2) {
	line-height: 40rpx;
}

.hot-topic .hot-topic-l {
	font-size: 26rpx;
	font-weight: 900;
}

/* .hot-topic view:nth-child(2) {
	border: solid rgb(225, 225, 225) 1rpx;
	display: inline-block;
	box-sizing: border-box;
	width: 145rpx;
	font-size: 22rpx;
	border-radius: 40rpx;
	text-align: center;
}

.hot-topic view:nth-child(2) i {
	font-size: 22rpx;
	color: #f02e72;
	margin-right: 5rpx;
} */

.hot-topic view:nth-child(2) text {
	font-size: 22rpx;
}

.hot-topic .icon-right-jf {
	font-size: 22rpx;
}

.card-swiper2 {
	height: 180rpx;
	margin: 0 0 30rpx 37rpx;
}

.card-swiper2 swiper-item {
	width: 351rpx !important;
	left: 0rpx;
	padding-right: 20rpx;
	box-sizing: border-box;
	/* padding: 40rpx 0rpx 70rpx; */
	overflow: initial;
}

.card-swiper2 swiper-item .swiper-item {
	width: 100%;
	display: block;
	height: 100%;
	position: relative;
	border-radius: 10rpx;
	/* transform: scale(1);
  transition: all 0.2s ease-in 0s; */
	overflow: hidden;
}

.card-swiper2 swiper-item .swiper-item.cur:after {
	content: "参与有奖";
	position: absolute;
	z-index: 2;
	left: 0;
	top: 0;
	text-align: center;
	line-height: 34rpx;
	width: 100rpx;
	height: 34rpx;
	color: #FFFFFF;
	font-size: 20rpx;
	/* padding: 4rpx 10rpx; */
	background: linear-gradient(270deg, rgb(242, 40, 113), rgb(255, 106, 143));
	border-bottom-right-radius: 10rpx;

}

.card-swiper2 .swiper-item image {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 1;
	border-radius: 10rpx;
}

.card-swiper2 .swiper-item view {
	position: relative;
	z-index: 3;
	height: 100%;
	text-align: center;
	color: rgb(255, 255, 255);
	font-size: 30rpx;
	line-height: 180rpx;
	border-radius: 10rpx;
}

.card-swiper2 .swiper-item view:after {
	content: "";
	position: absolute;
	display: block;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 2;
	background: rgba(0, 0, 0, .4);
}

.card-swiper2 .swiper-item view text {
	position: relative;
	z-index: 3;
	display: block;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: 0 10rpx;
}

/*热门话题卡片公共属性*/
/* .card-pbl {
	olumn-count:2;
} */
.card-pbl .card-pbl-son {
	width: calc(100% - 37rpx);
	background: #FFFFFF;
	border-radius: 15rpx;
	margin-bottom: 20rpx;
    float: left;
    margin-left: 25rpx;
}

.card-pbl .card-pbl-sonr{
	width: calc(100% - 38rpx);
	background: #FFFFFF;
	border-radius: 15rpx;
	margin-bottom: 20rpx;
    float: right;
    margin-right: 25rpx;
}

/* .card-pbl .card-pbl-son:nth-child(2n) {
	float: right;
} */

.card-pbl .card-pbl-text text {
	font-size: 24rpx;
}
.card-pbl .card-pbl-text navigator {
	font-size: 24rpx;
	line-height: 1.8em;
	font-weight: 900;
}

.card-pbl .card-pbl-header image {
	width: 330rpx;
	height: 330rpx;
	float: left;
	border-radius: 12rpx 12rpx 0 0;
}

.card-pbl .card-pbl-con .card-pbl-text,.linetwo {
	/* height: 74rpx; */
	margin-top: 10rpx;
	margin-bottom: 10rpx;
	display: -webkit-box;
	display: -moz-box;
	/* white-space: pre-wrap;
	word-wrap: break-word; */
	overflow: hidden;	
	/* text-overflow: ellipsis; */
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	/*显示行数*/
  font-weight: normal;
  line-height:36rpx;
  font-size: 26rpx;
  
}



.card-pbl .card-pbl-con .card-pbl-user,
.card-pbl .card-pbl-con .card-pbl-user text,
.card-pbl .card-pbl-con .card-pbl-user image,
.card-pbl .card-pbl-con .card-pbl-flowers,
.card-pbl .card-pbl-con .card-pbl-flowers .icon-xihuan,
.card-pbl .card-pbl-con .card-pbl-flowers text {
	display: inline;
	line-height: 38rpx;
}

.card-pbl-flowers {
  display: flex;
  align-items: center;
}

.card-pbl .card-pbl-con .card-pbl-user image,
.card-pbl .card-pbl-con .card-pbl-flowers .icon-xihuan {
	float: left;
	/* margin-bottom: 20rpx; */
}

.card-pbl .card-pbl-con .card-pbl-user image {
	margin-right: 16rpx;
	width: 38rpx;
	height: 38rpx;
	border-radius: 50%;
}

.card-pbl .card-pbl-con .card-pbl-flowers .icon-xihuan {
	margin-right: 10rpx;
	font-size: 26rpx;
	color: #f02f72;
}

.card-pbl .card-pbl-con .card-pbl-user text {
	color: rgb(136, 136, 136);
	font-size: 24rpx;
}

.card-pbl .card-pbl-con .card-pbl-flowers text {
	color: #f02f72;
	font-size: 24rpx;
}

.card-pbl .card-pbl-con .card-pbl-footer {
	line-height: 30rpx;
  height: 30rpx;
	display: inline-block;
	width: 100%;
	margin-bottom: 20rpx;
}

/*热门话题卡片类型二*/
.type2 .title-medium {
	font-size: 30rpx;
	color: #FFFFFF;
	margin-top: 20rpx;
}

.type2 .card-pbl-text {
	color: #FFFFFF;
	font-size: 26rpx;
}

/*热门话题卡片类型三*/
.type3 .card-pbl-type3 image {
	width: 140rpx;
	height: 140rpx;
	border-radius: 50%;
	margin-top: 40rpx;
}

.type3 .card-pbl-type3 .theme-c-53 {
	margin-top: 10rpx;
}

.type3 .card-pbl-type3 .theme-c-178 {
	margin-top: 16rpx;
}

.type3 .card-pbl-type3 .theme-c-136 {
	width: 90%;
	margin: 30rpx auto 0;
}

.type3 .card-pbl-type3 button {
	border: none;
	background: linear-gradient(270deg, rgb(242, 40, 113), rgb(255, 106, 143));
	color: #FFFFFF;
	font-size: 26rpx;
	border-radius: 40rpx;
	width: 200rpx;
	height: 60rpx;
	margin-top: 29rpx;
	margin-bottom: 30rpx;
	margin-left: auto;
	margin-right: auto;
}

/* 发布按钮 */
.release {
	position: fixed;
	bottom: 130rpx;
	right: 36rpx;
	z-index: 100;
}

.release image {
	width: 100rpx;
	height: 100rpx;
}

/*search*/
.search {
	min-height: 110rpx;
}
.cu-bar .search-form {
	margin: 0 10rpx;
}

.search-con .search-tit image {
	width: 27rpx;
	height: 32rpx;
	float: right;
}

.search-con .search-cache text {
	line-height: 60rpx;
	display: inline-block;
	padding: 0 30rpx;
	background: #F8F8F8;
	font-size: 30rpx;
	border-radius: 30rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	letter-spacing: 2rpx;
}

.search-con .search-more image {
	width: 17rpx;
	height: 10rpx;
	position: relative;
	top: -3rpx;
}

/* 发布模态框 */
.fbmtk {
    background: #f6f6f6;
    padding-bottom: 0rpx;
}

.fbmtk view,.fbmtk button,.fbmtk navigator  {
	font-size: 34rpx;
	line-height: 120rpx;
	background: #FFFFFF;
}

.fbmtk view:last-child {
	margin-top: 6rpx;
}

.fbmtk view:not(:last-child) {
	border-bottom: solid #f6f6f6 1rpx;
}
