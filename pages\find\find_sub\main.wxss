page {
  background-color: #F8F8F8;
  font-size: 28rpx;
  color: rgb(53, 53, 53);
  font-family: 'PingFangSC-Regular';
  /* Color 可以自定义相关配色 */
  --red: #e54d42;
  --orange: #f37b1d;
  --blue: #0081ff;
  --grey: #8799a3;
  --gray: #aaa;
  --black: #333;
  --white: #fff;
  --grey248: #f8f8f8;
  --theme_color_pink: #f32871;
  --gradualRed: linear-gradient(90deg, #f43f3b, #ec008c);
  --gradualOrange: linear-gradient(90deg, #ff9700, #ed1c24);
  --gradualPink: linear-gradient(90deg, #ec008c, #6739b6);
  --gradualBlue: linear-gradient(90deg, #0081ff, #1cbbb4);
  --themePink: linear-gradient(90deg, #ff6a8f, #f22871);

}

view,
scroll-view,
swiper,
button,
input,
textarea,
label,
navigator,
image {
  box-sizing: border-box;
}

.round {
  border-radius: 5000rpx;
}

.radius {
  border-radius: 12rpx 12rpx 0 0;
}

/* ==================
          图片
 ==================== */

image {
  max-width: 100%;
  display: inline-block;
  position: relative;
  z-index: 0;
}

image.loading::before {
  content: "";
  background-color: #F6F6F6;
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -2;
}

image.loading::after {
  content: "\e7f1";
  font-family: "cuIcon";
  position: absolute;
  top: 0;
  left: 0;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  right: 0;
  bottom: 0;
  z-index: -1;
  font-size: 32rpx;
  margin: auto;
  color: #ccc;
  -webkit-animation: icon-spin 2s infinite linear;
  animation: icon-spin 2s infinite linear;
  display: block;
}

.response {
  width: 100%;
}


.cu-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 30rpx;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  margin-left: initial;
  transform: translate(0rpx, 0rpx);
  margin-right: initial;
}

.cu-btn::after {
  display: none;
}

.cu-btn:not([class*="bg-"]) {
  background-color: #f0f0f0;
}

.cu-btn[class*="line"] {
  background-color: transparent;
}

.cu-btn[class*="line"]::after {
  content: " ";
  display: block;
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1rpx solid currentColor;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 12rpx;
  z-index: 1;
  pointer-events: none;
}

.cu-btn.round[class*="line"]::after {
  border-radius: 1000rpx;
}

.cu-btn[class*="lines"]::after {
  border: 6rpx solid currentColor;
}

.cu-btn[class*="bg-"]::after {
  display: none;
}

.cu-btn.sm {
  padding: 0 20rpx;
  font-size: 20rpx;
  height: 48rpx;
}

.cu-btn.lg {
  padding: 0 40rpx;
  font-size: 32rpx;
  height: 80rpx;
}

.cu-btn.icon.sm {
  width: 48rpx;
  height: 48rpx;
}

.cu-btn.icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 500rpx;
  padding: 0;
}

button.icon.lg {
  width: 80rpx;
  height: 80rpx;
}

.cu-btn.shadow-blur::before {
  top: 4rpx;
  left: 4rpx;
  filter: blur(6rpx);
  opacity: 0.6;
}

.cu-btn.button-hover {
  transform: translate(1rpx, 1rpx);
}

.block {
  display: block;
}

.cu-btn.block {
  display: flex;
}

.cu-btn[disabled] {
  opacity: 0.6;
  color: #fff;
}

/* ==================
          头像
 ==================== */

.cu-avatar {
  font-variant: small-caps;
  margin: 0;
  padding: 0;
  display: inline-flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  background-color: #ccc;
  color: #fff;
  white-space: nowrap;
  position: relative;
  width: 60rpx;
  height: 60rpx;
  background-size: cover;
  background-position: center;
  vertical-align: middle;
}

/* ==================
          列表
 ==================== */

.grayscale {
  filter: grayscale(1);
}

.cu-list+.cu-list {
  margin-top: 30rpx;
}

.cu-list>.cu-item {
  transition: all 0.6s ease-in-out 0s;
  transform: translateX(0rpx);
}

.cu-list>.cu-item.move-cur {
  transform: translateX(-260rpx);
}

.cu-list>.cu-item .move {
  position: absolute;
  right: 0;
  display: flex;
  width: 260rpx;
  height: 100%;
  transform: translateX(100%);
}

.cu-list>.cu-item .move view {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
}

.cu-list.menu-avatar {
  overflow: hidden;
}

.cu-list.menu-avatar>.cu-item {
  position: relative;
  display: flex;
  padding-right: 30rpx;
  height: 140rpx;
  background-color: #fff;
  justify-content: flex-end;
  align-items: center;
}

.cu-list.menu-avatar>.cu-item>.cu-avatar {
  position: absolute;
  left: 30rpx;
}

.cu-list.menu-avatar>.cu-item .flex .text-cut {
  max-width: 510rpx;
}

.cu-list.menu-avatar>.cu-item .content {
  position: absolute;
  left: 146rpx;
  width: calc(100% - 96rpx - 60rpx - 120rpx - 20rpx);
  line-height: 1.6em;
}

.cu-list.menu-avatar>.cu-item .content.flex-sub {
  width: calc(100% - 96rpx - 60rpx - 20rpx);
}

.cu-list.menu-avatar>.cu-item .content>view:first-child {
  font-size: 30rpx;
  display: flex;
  align-items: center;
}

.cu-list.menu-avatar>.cu-item .content .cu-tag.sm {
  display: inline-block;
  margin-left: 10rpx;
  height: 28rpx;
  font-size: 16rpx;
  line-height: 32rpx;
}

.cu-list.menu-avatar>.cu-item .action {
  width: 100rpx;
  text-align: center;
}

.cu-list.menu-avatar>.cu-item .action view+view {
  margin-top: 10rpx;
}

.cu-list.menu-avatar.comment>.cu-item .content {
  position: relative;
  left: 0;
  width: auto;
  flex: 1;
}

.cu-list.menu-avatar.comment>.cu-item {
  padding: 30rpx 30rpx 30rpx 120rpx;
  height: auto;
}

.cu-list.menu-avatar.comment .cu-avatar {
  align-self: flex-start;
}

.cu-list.menu>.cu-item {
  position: relative;
  display: flex;
  padding: 0 30rpx;
  min-height: 100rpx;
  background-color: #fff;
  justify-content: space-between;
  align-items: center;
}

.cu-list.menu>.cu-item:last-child:after {
  border: none;
}

.cu-list.menu>.cu-item:after {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 200%;
  height: 200%;
  border-bottom: 1rpx solid #ddd;
  border-radius: inherit;
  content: " ";
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
}

.cu-list.menu>.cu-item.grayscale {
  background-color: #F6F6F6;
}

.cu-list.menu>.cu-item.cur {
  background-color: #fcf7e9;
}

.cu-list.menu>.cu-item.arrow {
  padding-right: 90rpx;
}

.cu-list.menu>.cu-item.arrow:before {
  content: "\e6a3";
  position: absolute;
  top: 0;
  right: 30rpx;
  bottom: 0;
  display: block;
  margin: auto;
  width: 30rpx;
  height: 30rpx;
  color: var(--grey);
  text-align: center;
  font-size: 34rpx;
  font-family: cuIcon;
  line-height: 30rpx;
}

.cu-list.menu>.cu-item button.content {
  padding: 0;
  background-color: transparent;
  justify-content: flex-start;
}

.cu-list.menu>.cu-item button.content:after {
  display: none;
}

.cu-list.menu>.cu-item .cu-avatar-group .cu-avatar {
  border-color: #fff;
}

.cu-list.menu>.cu-item .content>view:first-child {
  display: flex;
  align-items: center;
}

.cu-list.menu>.cu-item .content>text[class*=icon] {
  display: inline-block;
  margin-right: 10rpx;
  width: 1.6em;
  text-align: center;
}

.cu-list.menu>.cu-item .content>image {
  display: inline-block;
  margin-right: 10rpx;
  width: 1.6em;
  height: 1.6em;
  vertical-align: middle;
}

.cu-list.menu>.cu-item .content {
  font-size: 30rpx;
  line-height: 1.6em;
  flex: 1;
}

.cu-list.menu>.cu-item .content .cu-tag.sm {
  display: inline-block;
  margin-left: 10rpx;
  height: 28rpx;
  font-size: 16rpx;
  line-height: 32rpx;
}

.cu-list.menu>.cu-item .action .cu-tag:empty {
  right: 10rpx;
}

.cu-list.menu {
  display: block;
  overflow: hidden;
}

.cu-list.menu.sm-border>.cu-item:after {
  left: 30rpx;
  width: calc(200% - 120rpx);
}

.cu-list.grid>.cu-item {
  position: relative;
  display: flex;
  padding: 20rpx 0 30rpx;
  transition-duration: 0s;
  flex-direction: column;
}

.cu-list.grid>.cu-item:after {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 200%;
  height: 200%;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: inherit;
  content: " ";
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
}

.cu-list.grid>.cu-item text {
  display: block;
  margin-top: 10rpx;
  color: #888;
  font-size: 26rpx;
  line-height: 40rpx;
}

.cu-list.grid>.cu-item [class*=icon] {
  position: relative;
  display: block;
  margin-top: 20rpx;
  width: 100%;
  font-size: 48rpx;
}

.cu-list.grid>.cu-item .cu-tag {
  right: auto;
  left: 50%;
  margin-left: 20rpx;
}

.cu-list.grid {
  background-color: #fff;
  text-align: center;
}

.cu-list.grid.no-border>.cu-item {
  padding-top: 10rpx;
  padding-bottom: 20rpx;
}

.cu-list.grid.no-border>.cu-item:after {
  border: none;
}

.cu-list.grid.no-border {
  padding: 20rpx 10rpx;
}

.cu-list.grid.col-3>.cu-item:nth-child(3n):after,
.cu-list.grid.col-4>.cu-item:nth-child(4n):after,
.cu-list.grid.col-5>.cu-item:nth-child(5n):after {
  border-right-width: 0;
}

.cu-list.card-menu {
  overflow: hidden;
  margin-right: 30rpx;
  margin-left: 30rpx;
  border-radius: 20rpx;
}

/* ==================
          操作条
 ==================== */

.cu-bar {
  display: flex;
  position: relative;
  align-items: center;
  /* min-height: 98rpx; */
  justify-content: space-between;
}

.cu-bar .action {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: center;
  max-width: 100%;
}

.cu-bar .action.border-title {
  position: relative;
  top: -10rpx;
}

.cu-bar .action.border-title text[class*="bg-"]:last-child {
  position: absolute;
  bottom: -0.5rem;
  min-width: 2rem;
  height: 6rpx;
  left: 0;
}

.cu-bar .action.sub-title {
  position: relative;
  top: -0.2rem;
}

.cu-bar .action.sub-title text {
  position: relative;
  z-index: 1;
}

.cu-bar .action.sub-title text[class*="bg-"]:last-child {
  position: absolute;
  display: inline-block;
  bottom: -0.2rem;
  border-radius: 6rpx;
  width: 100%;
  height: 0.6rem;
  left: 0.6rem;
  opacity: 0.3;
  z-index: 0;
}

.cu-bar .action.sub-title text[class*="text-"]:last-child {
  position: absolute;
  display: inline-block;
  bottom: -0.7rem;
  left: 0.5rem;
  opacity: 0.2;
  z-index: 0;
  text-align: right;
  font-weight: 900;
  font-size: 36rpx;
}

.cu-bar.justify-center .action.border-title text:last-child,
.cu-bar.justify-center .action.sub-title text:last-child {
  left: 0;
  right: 0;
  margin: auto;
  text-align: center;
}

.cu-bar .action:first-child {
  margin-left: 36rpx;
  font-size: 34rpx;
  font-family: 'PingFangSC-Medium';
}

.cu-bar .action text.text-cut {
  text-align: left;
  width: 100%;
}

.cu-bar .cu-avatar:first-child {
  margin-left: 20rpx;
}

.cu-bar .action:first-child>text[class*="icon"] {
  margin-left: -0.3em;
  margin-right: 0.3em;
}

.cu-bar .action:last-child {
  margin-right: 36rpx;
}

.cu-bar .action>text[class*="icon"],
.cu-bar .action>view[class*="icon"] {
  font-size: 36rpx;
}

.cu-bar .action>text[class*="icon"]+text[class*="icon"] {
  margin-left: 0.5em;
}

.cu-bar .content {
  position: absolute;
  text-align: center;
  width: calc(100% - 340rpx);
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  height: 60rpx;
  font-size: 32rpx;
  line-height: 60rpx;
  cursor: none;
  pointer-events: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.cu-bar.ios .content {
  bottom: 7px;
  height: 30px;
  font-size: 32rpx;
  line-height: 30px;
}

.cu-bar.btn-group {
  justify-content: space-around;
}

.cu-bar.btn-group button {
  padding: 20rpx 32rpx;
}

.cu-bar.btn-group button {
  flex: 1;
  margin: 0 20rpx;
  max-width: 50%;
}

.cu-bar .search-form {
  background-color: rgba(255, 255, 255, .2);
  line-height: 80rpx;
  height: 80rpx;
  font-size: 30rpx;
  color: #FFFFFF;
  flex: 1;
  display: flex;
  align-items: center;
  margin: 0 20rpx;
}

.cu-bar .search-form+.action {
  margin-right: 30rpx;
}

.cu-bar .search-form input {
  flex: 1;
  padding-right: 20rpx;
  height: 80rpx;
  color: #FFFFFF;
  line-height: 80rpx;
  font-size: 30rpx;
}

.cu-bar .search-form [class*="icon"] {
  margin: 0 22rpx 0 30rpx;
}

.cu-bar .search-form [class*="icon"]::before {
  top: 0rpx;
}

.cu-bar.fixed,
.nav.fixed {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1024;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.foot {
  position: fixed;
  width: 100%;
  bottom: 0;
  z-index: 1024;
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar {
  padding: 0;
  height: calc(100rpx + env(safe-area-inset-bottom) / 2);
  padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

.cu-tabbar-height {
  min-height: 100rpx;
  height: calc(100rpx + env(safe-area-inset-bottom) / 2);
}

.cu-bar.tabbar.shadow {
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar .action {
  font-size: 22rpx;
  position: relative;
  flex: 1;
  text-align: center;
  padding: 0;
  display: block;
  height: auto;
  line-height: 1;
  margin: 0;
  background-color: inherit;
  overflow: initial;
}

.cu-bar.tabbar.shop .action {
  width: 140rpx;
  flex: initial;
}

.cu-bar.tabbar .action.add-action {
  position: relative;
  z-index: 2;
  padding-top: 50rpx;
}

.cu-bar.tabbar .action.add-action [class*="icon"] {
  position: absolute;
  width: 70rpx;
  z-index: 2;
  height: 70rpx;
  border-radius: 50%;
  line-height: 70rpx;
  font-size: 50rpx;
  top: -35rpx;
  left: 0;
  right: 0;
  margin: auto;
  padding: 0;
}

.cu-bar.tabbar .action.add-action::after {
  content: "";
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  top: -50rpx;
  left: 0;
  right: 0;
  margin: auto;
  box-shadow: 0 -3rpx 8rpx rgba(0, 0, 0, 0.08);
  border-radius: 50rpx;
  background-color: inherit;
  z-index: 0;
}

.cu-bar.tabbar .action.add-action::before {
  content: "";
  position: absolute;
  width: 100rpx;
  height: 30rpx;
  bottom: 30rpx;
  left: 0;
  right: 0;
  margin: auto;
  background-color: inherit;
  z-index: 1;
}

.cu-bar.tabbar .btn-group {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 10rpx;
}

.cu-bar.tabbar button.action::after {
  border: 0;
}

.cu-bar.tabbar .action [class*="icon"] {
  width: 100rpx;
  position: relative;
  display: block;
  height: auto;
  margin: 0 auto 10rpx;
  text-align: center;
  font-size: 40rpx;
}

.cu-bar.tabbar .action .icon-cu-image {
  margin: 0 auto;
}

.cu-bar.tabbar .action .icon-cu-image image {
  width: 50rpx;
  height: 44rpx;
  display: inline-block;
}

.cu-bar.tabbar .submit {
  align-items: center;
  display: flex;
  justify-content: center;
  text-align: center;
  position: relative;
  flex: 2;
  align-self: stretch;
}

.cu-bar.tabbar .submit:last-child {
  flex: 2.6;
}

.cu-bar.tabbar .submit+.submit {
  flex: 2;
}

.cu-bar.tabbar.border .action::before {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0.5);
  transform-origin: 0 0;
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
  z-index: 3;
}

.cu-bar.tabbar.border .action:last-child:before {
  display: none;
}

.cu-bar.input {
  /* padding-right: 20rpx; */
  background-color: #fff;
}

.cu-bar.input input {
  overflow: initial;
  line-height: 70rpx;
  height: 70rpx;
  min-height: 70rpx;
  flex: 1;
  font-size: 30rpx;
  margin: 0 20rpx;
  background: #FFFFFF;
  border-radius: 40rpx;
  padding: 0rpx 20rpx;
}

.cu-bar.input .action {
  margin-left: 20rpx;
}

.cu-bar.input .action [class*="icon"] {
  font-size: 48rpx;
}

.cu-bar.input input+.action {
  margin-right: 20rpx;
  margin-left: 0rpx;
}

.cu-bar.input .action:first-child [class*="icon"] {
  margin-left: 0rpx;
}

.cu-custom {
  display: block;
  position: relative;
}

.cu-custom .cu-bar .content {
  width: calc(100% - 440rpx);
}

.cu-custom .cu-bar {
  min-height: 0px;
  padding-right: 200rpx;
  box-shadow: 0rpx 0rpx 0rpx;
  z-index: 9999;
}

.cu-custom .cu-bar .content image {
  height: 60rpx;
  width: 240rpx;
}

.cu-custom .cu-bar .border-custom {
  position: relative;
  background: rgba(0, 0, 0, 0.15);
  border-radius: 1000rpx;
  height: 30px;
}

.cu-custom .cu-bar .border-custom::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 1rpx solid #fff;
  opacity: 0.5;
}

.cu-custom .cu-bar .border-custom::before {
  content: " ";
  width: 1rpx;
  height: 110%;
  position: absolute;
  top: 22.5%;
  left: 0;
  right: 0;
  margin: auto;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  opacity: 0.6;
  background-color: #fff;
}

.cu-custom .cu-bar .border-custom view {
  display: block;
  flex: 1;
  margin: auto !important;
  text-align: center;
  font-size: 34rpx;
  padding: 10rpx 0rpx;
}

/* ==================
         导航栏
 ==================== */

.nav {
  white-space: nowrap;
}

::-webkit-scrollbar {
  display: none;
}

.nav .cu-item {
  height: 90rpx;
  display: inline-block;
  line-height: 90rpx;
  margin: 0 10rpx;
  padding: 0 20rpx;
}

.nav .cu-item.cur {
  border-bottom: 4rpx solid;
}

/* ==================
         时间轴
 ==================== */

.cu-timeline {
  display: block;
  background-color: #fff;
}

.cu-timeline .cu-time {
  width: 120rpx;
  text-align: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #888;
  display: block;
}

.cu-timeline>.cu-item {
  padding: 30rpx 30rpx 30rpx 120rpx;
  position: relative;
  display: block;
  z-index: 0;
}

.cu-timeline>.cu-item:not([class*="text-"]) {
  color: #ccc;
}

.cu-timeline>.cu-item::after {
  content: "";
  display: block;
  position: absolute;
  width: 1rpx;
  background-color: #ddd;
  left: 60rpx;
  height: 100%;
  top: 0;
  z-index: 8;
}

.cu-timeline>.cu-item::before {
  font-family: "cuIcon";
  display: block;
  position: absolute;
  top: 36rpx;
  z-index: 9;
  background-color: #fff;
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  border: none;
  line-height: 50rpx;
  left: 36rpx;
}

.cu-timeline>.cu-item:not([class*="icon-"])::before {
  content: "\e763";
}

.cu-timeline>.cu-item[class*="icon"]::before {
  background-color: #fff;
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  border: none;
  line-height: 50rpx;
  left: 36rpx;
}

.cu-timeline>.cu-item>.content {
  padding: 30rpx;
  border-radius: 6rpx;
  display: block;
  line-height: 1.6;
}

.cu-timeline>.cu-item>.content:not([class*="bg-"]) {
  background-color: #f1f1f1;
  color: #666;
}

.cu-timeline>.cu-item>.content+.content {
  margin-top: 20rpx;
}


/* ==================
         模态窗口
 ==================== */

.cu-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1110;
  opacity: 0;
  outline: 0;
  text-align: center;
  -ms-transform: scale(1.185);
  transform: scale(1.185);
  backface-visibility: hidden;
  perspective: 2000rpx;
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out 0s;
  pointer-events: none;
}

.cu-modal::before {
  content: "\200B";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.cu-modal.show {
  opacity: 1;
  transition-duration: 0.3s;
  -ms-transform: scale(1);
  transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
}

.cu-dialog {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 750rpx;
  max-width: 100%;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  overflow: hidden;
}

.cu-modal.bottom-modal::before {
  vertical-align: bottom;
}

.cu-modal.bottom-modal .cu-dialog {
  width: 100%;
  border-radius: 0;
}

.cu-modal.bottom-modal {
  margin-bottom: -1000rpx;
}

.cu-modal.bottom-modal.show {
  margin-bottom: 0;
}

.cu-modal.drawer-modal {
  transform: scale(1);
  display: flex;
}

.cu-modal.drawer-modal .cu-dialog {
  height: 100%;
  min-width: 200rpx;
  border-radius: 0;
  margin: initial;
  transition-duration: 0.3s;
}

.cu-modal.drawer-modal.justify-start .cu-dialog {
  transform: translateX(-100%);
}

.cu-modal.drawer-modal.justify-end .cu-dialog {
  transform: translateX(100%);
}

.cu-modal.drawer-modal.show .cu-dialog {
  transform: translateX(0%);
}

/* ==================
         轮播
 ==================== */

swiper.square-dot .wx-swiper-dot {
  background-color: #fff;
  opacity: 0.4;
  width: 10rpx;
  height: 10rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease-in-out 0s;
}

swiper.square-dot .wx-swiper-dot.wx-swiper-dot-active {
  opacity: 1;
  width: 30rpx;
}

swiper.round-dot .wx-swiper-dot {
  width: 10rpx;
  height: 10rpx;
  top: -4rpx;
  transition: all 0.3s ease-in-out 0s;
  position: relative;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active::after {
  content: "";
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  top: 0rpx;
  left: 0rpx;
  right: 0;
  bottom: 0;
  margin: auto;
  background-color: #fff;
  border-radius: 20rpx;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active {
  width: 18rpx;
  height: 18rpx;
  top: 0rpx;
}

.screen-swiper {
  min-height: 375rpx;
}

.screen-swiper image,
.screen-swiper video,
.swiper-item image,
.swiper-item video {
  width: 100%;
  display: block;
  height: 100%;
  margin: 0;
  pointer-events: none;
}

.card-swiper {
  height: 260rpx;
  margin: 10rpx 0rpx 40rpx;
}

.card-swiper swiper-item {
  width: 678rpx !important;
  left: 37rpx;
  box-sizing: border-box;
  /* padding: 40rpx 0rpx 70rpx; */
  overflow: initial;
}

.card-swiper swiper-item .swiper-item {
  width: 100%;
  display: block;
  height: 100%;
  border-radius: 10rpx;
  transform: scale(0.95);
  transition: all 0.2s ease-in 0s;
  overflow: hidden;
}

.card-swiper swiper-item.cur .swiper-item {
  transform: none;
  transition: all 0.2s ease-in 0s;
}

.tower-swiper {
  height: 420rpx;
  position: relative;
  max-width: 750rpx;
  overflow: hidden;
}

.tower-swiper .tower-item {
  position: absolute;
  width: 300rpx;
  height: 380rpx;
  top: 0;
  bottom: 0;
  left: 50%;
  margin: auto;
  transition: all 0.2s ease-in 0s;
  opacity: 1;
}

.tower-swiper .tower-item.none {
  opacity: 0;
}

.tower-swiper .tower-item .swiper-item {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
  overflow: hidden;
}


/* ==================
          布局
 ==================== */

/*  -- flex弹性布局 -- */

.flex {
  display: flex;
}

.basis-xs {
  flex-basis: 20%;
}

.basis-sm {
  flex-basis: 40%;
}

.basis-df {
  flex-basis: 50%;
}

.basis-lg {
  flex-basis: 60%;
}

.basis-xl {
  flex-basis: 80%;
}

.flex-sub {
  flex: 1;
}

.flex-twice {
  flex: 2;
}

.flex-treble {
  flex: 3;
}

.flex-direction {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-center {
  align-items: center;
}

.align-stretch {
  align-items: stretch;
}

.self-start {
  align-self: flex-start;
}

.self-center {
  align-self: flex-center;
}

.self-end {
  align-self: flex-end;
}

.self-stretch {
  align-self: stretch;
}

.align-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

/* grid布局 */

.grid {
  display: flex;
  flex-wrap: wrap;
}

.grid.grid-square {
  /*  overflow: hidden; */
}

.grid.grid-square .cu-tag {
  position: absolute;
  right: -18rpx;
  top: -18rpx;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
}

.grid.grid-square .cu-tag i {
  font-size: 16rpx;
}

.grid.grid-square>view>text[class*="icon"] {
  font-size: 52rpx;
  position: absolute;
  color: var(--grey);
  margin: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.grid.grid-square>view {
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 6rpx;
  position: relative;
  /* overflow: hidden; */
}

.grid.col-1.grid-square>view {
  padding-bottom: 100%;
  height: 0;
  margin-right: 0;
}

.grid.col-2.grid-square>view {
  padding-bottom: calc((100% - 20rpx)/2);
  height: 0;
  width: calc((100% - 20rpx)/2);
}

.grid.col-2.grid-square>view:nth-child(2n) {
  margin-right: 0;
}

.grid.col-3.grid-square>view {
  padding-bottom: calc((100% - 40rpx)/3);
  height: 0;
  width: calc((100% - 40rpx)/3);
}

.grid.col-3.grid-square>view:nth-child(3n) {
  margin-right: 0;
}

.grid.col-4.grid-square>view {
  padding-bottom: calc((100% - 60rpx)/4);
  height: 0;
  width: calc((100% - 60rpx)/4);
}

.grid.col-4.grid-square>view:nth-child(4n) {
  margin-right: 0;
}

.grid.col-5.grid-square>view {
  padding-bottom: calc((100% - 80rpx)/5);
  height: 0;
  width: calc((100% - 80rpx)/5);
}

.grid.col-1>view {
  width: 100%;
}

.grid.col-2>view {
  width: 50%;
}

.grid.col-3>view {
  width: 33.33%;
}

.grid.col-4>view {
  width: 25%;
}

.grid.col-5>view {
  width: 20%;
}

/*  -- 内外边距 -- */
.min-h-666 {
  min-height: 666rpx;
}

.margin-0 {
  margin: 0;
}

.margin-xs {
  margin: 10rpx;
}

.margin-sm {
  margin: 20rpx;
}

.margin {
  margin: 30rpx;
}

.margin-lg {
  margin: 40rpx;
}

.margin-xl {
  margin: 50rpx;
}

.margin-top-xs {
  margin-top: 10rpx;
}

.margin-top-sm {
  margin-top: 20rpx;
}

.margin-top {
  margin-top: 30rpx;
}

.margin-top-lg {
  margin-top: 40rpx;
}

.margin-top-xl {
  margin-top: 50rpx;
}

.margin-right-xs {
  margin-right: 10rpx;
}

.margin-right-sm {
  margin-right: 20rpx;
}

.margin-right {
  margin-right: 30rpx;
}

.margin-right-lg {
  margin-right: 40rpx;
}

.margin-right-xl {
  margin-right: 50rpx;
}

.margin-bottom-xs {
  margin-bottom: 10rpx;
}

.margin-bottom-sm {
  margin-bottom: 20rpx;
}

.margin-bottom {
  margin-bottom: 30rpx;
}

.margin-bottom-lg {
  margin-bottom: 40rpx;
}

.margin-bottom-xl {
  margin-bottom: 50rpx;
}

.margin-left-xs {
  margin-left: 10rpx;
}

.margin-left-sm {
  margin-left: 20rpx;
}

.margin-left {
  margin-left: 30rpx;
}

.margin-left-lg {
  margin-left: 40rpx;
}

.margin-left-xl {
  margin-left: 50rpx;
}

.margin-lr-xs {
  margin-left: 10rpx;
  margin-right: 10rpx;
}

.margin-lr-sm {
  margin-left: 20rpx;
  margin-right: 20rpx;
}

.margin-lr {
  margin-left: 30rpx;
  margin-right: 30rpx;
}

.margin-lr-lg {
  margin-left: 40rpx;
  margin-right: 40rpx;
}

.margin-lr-xl {
  margin-left: 50rpx;
  margin-right: 50rpx;
}

.margin-tb-xs {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.margin-tb-sm {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.margin-tb {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}

.margin-tb-lg {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.margin-tb-xl {
  margin-top: 50rpx;
  margin-bottom: 50rpx;
}

.padding-0 {
  padding: 0;
}

.padding-xs {
  padding: 10rpx;
}

.padding-sm {
  padding: 20rpx;
}

.padding {
  padding: 30rpx;
}

.padding-lg {
  padding: 40rpx;
}

.padding-xl {
  padding: 50rpx;
}

.padding-top-xs {
  padding-top: 10rpx;
}

.padding-top-sm {
  padding-top: 20rpx;
}

.padding-top {
  padding-top: 30rpx;
}

.padding-top-lg {
  padding-top: 40rpx;
}

.padding-top-xl {
  padding-top: 50rpx;
}

.padding-right-xs {
  padding-right: 10rpx;
}

.padding-right-sm {
  padding-right: 20rpx;
}

.padding-right {
  padding-right: 30rpx;
}

.padding-right-lg {
  padding-right: 40rpx;
}

.padding-right-xl {
  padding-right: 50rpx;
}

.padding-bottom-xs {
  padding-bottom: 10rpx;
}

.padding-bottom-sm {
  padding-bottom: 20rpx;
}

.padding-bottom {
  padding-bottom: 30rpx;
}

.padding-bottom-lg {
  padding-bottom: 40rpx;
}

.padding-bottom-xl {
  padding-bottom: 50rpx;
}

.padding-left-xs {
  padding-left: 10rpx;
}

.padding-left-sm {
  padding-left: 20rpx;
}

.padding-left {
  padding-left: 30rpx;
}

.padding-left-lg {
  padding-left: 40rpx;
}

.padding-left-xl {
  padding-left: 50rpx;
}

.padding-lr-xs {
  padding-left: 10rpx;
  padding-right: 10rpx;
}

.padding-lr-sm {
  padding-left: 20rpx;
  padding-right: 20rpx;
}

.padding-lr {
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.padding-lr-lg {
  padding-left: 40rpx;
  padding-right: 40rpx;
}

.padding-lr-xl {
  padding-left: 50rpx;
  padding-right: 50rpx;
}

.padding-tb-xs {
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}

.padding-tb-sm {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

.padding-tb {
  padding-top: 30rpx;
  padding-bottom: 30rpx;
}

.padding-tb-lg {
  padding-top: 40rpx;
  padding-bottom: 40rpx;
}

.padding-tb-xl {
  padding-top: 50rpx;
  padding-bottom: 50rpx;
}

/* -- 浮动 --  */

.cf::after,
.cf::before {
  content: " ";
  display: table;
}

.cf::after {
  clear: both;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

/* ==================
          背景
 ==================== */



.bg-grey {
  background-color: var(--grey);
  color: #fff;
}

.bg-gray {
  background-color: #f0f0f0;
  color: #666;
}

.bg-black {
  background-color: var(--black);
  color: #fff;
}

.bg-white {
  background-color: #fff;
  color: #666;
}

.bg-f8f8f8 {
  background: #f8f8f8;
  font-size: 40rpx;
  color: #353535;
}



.bg-grey.light {
  color: var(--grey);
  background-color: #e7ebed;
}

.bg-gray.light {
  color: #666;
  background-color: #fadbd9;
}

.bg-gray.light {
  color: #888;
  background-color: #f1f1f1;
}

.bg-gradual-red {
  background-image: var(--gradualRed);
  color: #fff;
}

.bg-gradual-orange {
  background-image: var(--gradualOrange);
  color: #fff;
}


.bg-gradual-pink {
  background-image: var(--gradualPink);
  color: #fff;
}

.bg-theme-pink {
  background-image: var(--themePink);
  color: #fff;
  font-size: 40rpx;
}

.bg-theme-248 {
  background: rgb(248, 248, 248);
  color: #353535;
  font-size: 40rpx;
}

.bg-theme-255 {
  background: rgb(255, 255, 255);
  color: #353535;
  font-size: 40rpx;
}

.bg-gradual-blue {
  background-image: var(--gradualBlue);
  color: #fff;
}

.shadow[class*="-red"] {
  box-shadow: 6rpx 6rpx 8rpx rgba(204, 69, 59, 0.2);
}

.shadow[class*="-orange"] {
  box-shadow: 6rpx 6rpx 8rpx rgba(217, 109, 26, 0.2);
}

.shadow[class*="-yellow"] {
  box-shadow: 6rpx 6rpx 8rpx rgba(224, 170, 7, 0.2);
}



.shadow[class*="-blue"] {
  box-shadow: 6rpx 6rpx 8rpx rgba(0, 102, 204, 0.2);
}



.shadow[class*="-pink"] {
  box-shadow: 6rpx 6rpx 8rpx rgba(199, 50, 134, 0.2);
}



.cu-btn.shadow[class*="-grey"] {
  box-shadow: 6rpx 6rpx 8rpx rgba(114, 130, 138, 0.2);
}

.cu-btn.shadow[class*="-gray"] {
  box-shadow: 6rpx 6rpx 8rpx rgba(114, 130, 138, 0.2);
}

.cu-btn.shadow[class*="-black"] {
  box-shadow: 6rpx 6rpx 8rpx rgba(26, 26, 26, 0.2);
}


/* ==================
          文本
 ==================== */

.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-df {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-xxl {
  font-size: 44rpx;
}

.text-sl {
  font-size: 80rpx;
}

.text-xsl {
  font-size: 120rpx;
}

.text-Abc {
  text-transform: Capitalize;
}

.text-ABC {
  text-transform: Uppercase;
}

.text-abc {
  text-transform: Lowercase;
}

.text-price::before {
  content: "¥";
  font-size: 80%;
  margin-right: 4rpx;
}

.text-cut {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.text-bold {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

.text-content {
  line-height: 1.6;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-red,
.line-red,
.lines-red {
  color: var(--red);
}

.text-orange,
.line-orange,
.lines-orange {
  color: var(--orange);
}



.lineone {
  display: -webkit-box;
  display: -moz-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  font-weight: normal;
  line-height: 36rpx;
  font-size: 22rpx;
  max-width: 160rpx;
  height: 36rpx;
  color: rgb(136, 136, 136);

}

/*  -- 内外边距 -- */

.mt-1 {
  margin-top: 1rpx;
}

.mt-10 {
  margin-top: 10rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mt-30 {
  margin-top: 30rpx;
}

.mt-40 {
  margin-top: 40rpx;
}

.mt-50 {
  margin-top: 50rpx;
}

.mt-60 {
  margin-top: 60rpx;
}

.mt-70 {
  margin-top: 70rpx;
}

.mt-80 {
  margin-top: 80rpx;
}

.mt-90 {
  margin-top: 90rpx;
}

.mt-100 {
  margin-top: 100rpx;
}

.mt-110 {
  margin-top: 110rpx;
}

.mt-120 {
  margin-top: 120rpx;
}

.mt-130 {
  margin-top: 130rpx;
}

.mt-140 {
  margin-top: 140rpx;
}

.mt-150 {
  margin-top: 150rpx;
}

.mr-10 {
  margin-right: 10rpx;
}

.mr-20 {
  margin-right: 20rpx;
}

.mr-30 {
  margin-right: 30rpx;
}

.mr-40 {
  margin-right: 40rpx;
}

.mr-50 {
  margin-right: 50rpx;
}

.mr-60 {
  margin-right: 60rpx;
}

.mr-70 {
  margin-right: 70rpx;
}

.mr-80 {
  margin-right: 80rpx;
}

.mr-90 {
  margin-right: 90rpx;
}

.mr-100 {
  margin-right: 100rpx;
}

.mb-1 {
  margin-bottom: 1rpx;
}

.mb-10 {
  margin-bottom: 10rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.mb-30 {
  margin-bottom: 30rpx;
}

.mb-40 {
  margin-bottom: 40rpx;
}

.mb-50 {
  margin-bottom: 50rpx;
}

.mb-60 {
  margin-bottom: 60rpx;
}

.mb-70 {
  margin-bottom: 70rpx;
}

.mb-80 {
  margin-bottom: 80rpx;
}

.mb-90 {
  margin-bottom: 90rpx;
}

.mb-100 {
  margin-bottom: 100rpx;
}

.ml-10 {
  margin-left: 10rpx;
}

.ml-20 {
  margin-left: 20rpx;
}

.ml-30 {
  margin-left: 30rpx;
}

.ml-40 {
  margin-left: 40rpx;
}

.ml-50 {
  margin-left: 50rpx;
}

.ml-60 {
  margin-left: 60rpx;
}

.ml-70 {
  margin-left: 70rpx;
}

.ml-80 {
  margin-left: 80rpx;
}

.ml-90 {
  margin-left: 90rpx;
}

.ml-100 {
  margin-left: 100rpx;
}

.ml-150 {
  margin-left: 150rpx;
}

.ml-320 {
  margin-left: 320rpx;
}

.pt-10 {
  padding-top: 10rpx;
}

.pt-20 {
  padding-top: 20rpx;
}

.pt-30 {
  padding-top: 30rpx;
}

.pt-40 {
  padding-top: 40rpx;
}

.pt-50 {
  padding-top: 50rpx;
}

.pt-60 {
  padding-top: 60rpx;
}

.pt-70 {
  padding-top: 70rpx;
}

.pt-80 {
  padding-top: 80rpx;
}

.pt-90 {
  padding-top: 90rpx;
}

.pt-100 {
  padding-top: 100rpx;
}

.pr-10 {
  padding-right: 10rpx;
}

.pr-20 {
  padding-right: 20rpx;
}

.pr-30 {
  padding-right: 30rpx;
}

.pr-40 {
  padding-right: 40rpx;
}

.pr-50 {
  padding-right: 50rpx;
}

.pr-60 {
  padding-right: 60rpx;
}

.pr-70 {
  padding-right: 70rpx;
}

.pr-80 {
  padding-right: 80rpx;
}

.pr-90 {
  padding-right: 90rpx;
}

.pr-100 {
  padding-right: 100rpx;
}

.pb-10 {
  padding-bottom: 10rpx;
}

.pb-20 {
  padding-bottom: 20rpx;
}

.pb-30 {
  padding-bottom: 30rpx;
}

.pb-40 {
  padding-bottom: 40rpx;
}

.pb-50 {
  padding-bottom: 50rpx;
}

.pb-60 {
  padding-bottom: 60rpx;
}

.pb-70 {
  padding-bottom: 70rpx;
}

.pb-80 {
  padding-bottom: 80rpx;
}

.pb-90 {
  padding-bottom: 90rpx;
}

.pb-100 {
  padding-bottom: 100rpx;
}

.pl-10 {
  padding-left: 10rpx;
}

.pl-20 {
  padding-left: 20rpx;
}

.pl-30 {
  padding-left: 30rpx;
}

.pl-40 {
  padding-left: 40rpx;
}

.pl-50 {
  padding-left: 50rpx;
}

.pl-60 {
  padding-left: 60rpx;
}

.pl-70 {
  padding-left: 70rpx;
}

.pl-80 {
  padding-left: 80rpx;
}

.pl-90 {
  padding-left: 90rpx;
}

.pl-100 {
  padding-left: 100rpx;
}


.mp10 {
  width: 10%;
}

.mp20 {
  width: 20%;
}

.mp30 {
  width: 30%;
}

.mp40 {
  width: 40%;
}

.mp50 {
  width: 50%;
}

.mp60 {
  width: 60%;
}

.mp65 {
  width: 65%;
}

.mp70 {
  width: 70%;
}

.mp80 {
  width: 80%;
}

.mp90 {
  width: 90%;
}

.mp100 {
  width: 100%;
}


/*段落属性配置*/

.line-1 {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* .line-2 ::after{
    content: "展开";
} */

.line-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.sanfuiconr {
  background-image: url(https://snsimg.sanfu.com/sanfu1653557029908.png);
  background-repeat: no-repeat;
  background-position: right center;
  background-size: 100% 100%;
  padding: 0rpx 105rpx 0rpx 10rpx;
  width: calc(285rpx - 10rpx - 105rpx) !important;
  height: 50rpx !important;
  line-height: 50rpx;
  color: #ffffff;
  font-size: 24rpx;
  position: relative;

}

.sanfuicon {
  background-image: url(https://snsimg.sanfu.com/sanfu1652779751942.png);
  background-repeat: no-repeat;
  background-position: right center;
  background-size: 100% 100%;
  padding: 0rpx 10rpx 0rpx 105rpx;
  width: calc(285rpx - 10rpx - 105rpx) !important;
  height: 50rpx !important;
  line-height: 50rpx;
  color: #ffffff;
  font-size: 24rpx;
  position: relative;

}

.sanfuicon,
.sanfuicons {
  width: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  overflow: hidden;
}


.face_info {
  position: relative;
}

.vdaren4 {
  position: absolute;
  z-index: 99;
  left: 76rpx;
  top: 76rpx;
  width: 30rpx !important;
  height: 30rpx !important;
}

.vdaren3 {
  position: absolute;
  z-index: 99;
  left: 106rpx;
  top: 110rpx;
  width: 36rpx !important;
  height: 36rpx !important;
}

.vdaren2 {
  position: absolute;
  z-index: 99;
  left: 62rpx;
  top: 54rpx;
  width: 30rpx !important;
  height: 30rpx !important;
}

.vdaren1 {
  position: absolute;
  z-index: 99;
  left: 26rpx;
  top: 64rpx;
  width: 20rpx !important;
  height: 20rpx !important;
}

.vdaren {
  position: absolute;
  z-index: 99;
  left: 26rpx;
  top: 18rpx;
  width: 20rpx !important;
  height: 20rpx !important;
}

.vdaren2 {
    position: absolute;
    z-index: 99;
    right: -10rpx;
    bottom: -10rpx;
    width: 20rpx !important;
    height: 20rpx !important;
  }