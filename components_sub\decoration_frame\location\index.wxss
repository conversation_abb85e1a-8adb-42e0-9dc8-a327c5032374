.locationIcon {
  height: 28rpx;
  width: 28rpx;
  position: relative;
  margin-right: 8rpx;
  /* overflow: hidden; */
}

.locationIconImg {
  width: 100%;
  height: 28rpx;
  font-size: 28rpx;
}


.locationCity {
  display: inline-block;
  position: relative;
  height: 28rpx;
  font-size: 28rpx;
  line-height: 28rpx;
  margin-right: 14rpx;
  transition: height .3s, width .3s;
}


@keyframes show1 {
  0% {
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  90% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}


.notPositioned {
  opacity: 0;
  width: 356rpx;
  height: 86rpx;
  position: absolute;
  left: 24rpx;
  top: 42rpx;
  z-index: 999;
  animation: show1 5s linear 3s;
  animation-fill-mode: forwards;
}

.notPositionedTxt {
  height: 28rpx;
  display: block;
  z-index: 1000;
  position: absolute;
  bottom: 0;
  font-size: 28rpx;
  color: #FFFFFF;
  top: 0;
  margin: auto;
  line-height: 28rpx;
  text-align: center;
  width: 100%;
  background: rgba(0, 0, 0, 0.70);
  padding: 21rpx 0;
  box-sizing: content-box;
  border-radius: 12rpx;
}

.notPositionedTxt::after {
  content: "";
  width: 0;
  height: 0;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-bottom: 6px solid rgba(0, 0, 0, 0.70);
  position: absolute;
  top: -6px;
  left: 20px;
}
