import Share from '../../../components/common/share/index.js'
var app = getApp();
var reqdata = require('./datareq');
var md5 = require('./md5');
var base64 = require('../../../utils/Base64');
require('./findbase.js');
var startPoint;

Component({
  options: {
    virtualHost: true
  },
  data: {
    btmenu: 0, //底部菜单位置
    pheight: app.globalData.pheight,
    StatusBar: app.globalData.StatusBar, //手机状态栏高度
    CustomBar: app.globalData.CustomBar, //手机导航栏高度
    Custom: app.globalData.Custom, //手机导航栏
    authorization: false, //是否授权
    loaded: false, //首页是否加载完成
    latitude: 0,
    longitude: 0,
    tabCur: 0, //以下冒泡相关
    buttonTop: 0,
    buttonLeft: 0,
    buttonRight: 0,
    windowHeight: '', //设备高
    windowWidth: '', //设备宽
    classIsShow: false, //冒泡样式是否显示
    userinfo: null, //用户基本信息
    active: 1, //当关版块
    image: true,
    video: true,
    touches: 0, //显示新手指引顺序
    activityList: [{
      dataIndex: 0, //序号
      datalist: [], //数据列表
      page: 1, //当前页码
      currentpagesize: 0, //当前页面大小
    }, {
      dataIndex: 0, //序号
      datalist: [], //数据列表
      page: 1, //当前页码
      currentpagesize: 0, //当前页面大小
    }, {
      dataIndex: 0, //序号
      datalist: [], //数据列表
      page: 1, //当前页码
      currentpagesize: 0, //当前页面大小
    }],
    rightList: [{
        name: '喜欢',
        image: 'https://snsimg.sanfu.com/sanfu1653036671888.png',
        id: 1
      },
      {
        name: '转发',
        image: 'https://snsimg.sanfu.com/sanfu1653031877882.png',
        id: 2
      },
      {
        name: '购买',
        image: 'https://snsimg.sanfu.com/sanfu1653031715489.png',
        id: 3
      },
      // {
      // 	name: '评论',
      // 	image: 'https://snsimg.sanfu.com/sanfu1653031762430.png',
      // 	id: 4
      // },
    ],
    newsType: [{
      "id": 100015,
      "name": "出街潮搭"
    }, {
      "id": 100016,
      "name": "美妆达人"
    }, {
      "id": 100018,
      "name": "居家休闲"
    }],
    active2: 0, //
    feed: 1, //1表示全屏模式，0表示列表模式
    shop: true,
    packShow: false, //加购商品的展开和收缩
    packShow2: false,
    coloract: 0, //购物车选择颜色
    love: false, //爱心
    video_list: [{
      video_src: 'https://stream7.iqilu.com/10339/upload_transcode/202002/18/20200218093206z8V1JuPlpe.mp4'
    }],
    playIndex: 0, //子swiper当前项
    changeIndex: 0, //feed流过来的充号
    dataIndex: 0, //序号
    datalist: [], //数据列表
    page: 1, //当前页码
    pagesize: 10, //每页大小
    currentpagesize: 0, //当前页面大小
    newsinfo: null, //新闻详情
    newsView: null, //传过来的帖子
    newsid: 0, //传过来的新闻
    scrolltop: 0, //距离头部位置
    newNuser: "1", //是否显示新手操作指南，0表示显示
    buyData: {
      fromChannel: "00"
    },
    playStatus: 0, // 视频播放状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async attached(options) {
    var that = this;
    await app.waitSid(); //等待首页启动
    //获取系统信息
    wx.getSystemInfo({
      success: e => {
        app.globalData.StatusBar = e.statusBarHeight;
        let custom = wx.getMenuButtonBoundingClientRect();
        app.globalData.Custom = custom;
        app.globalData.CustomBar = custom.bottom + custom.top - e.statusBarHeight;
        app.globalData.pwidth = e.screenWidth;
        app.globalData.pheight = e.screenHeight;
        app.globalData.psize = e.screenWidth / 750;
        try {
          if (app.globalData.CustomBar == undefined || parseInt(app.globalData.CustomBar) > 100 || parseInt(app.globalData.CustomBar) == 0) {
            app.globalData.CustomBar = 60;
          }
        } catch (e) {
          app.globalData.CustomBar = 60;
        }
        this.setData({
          CustomBar: app.globalData.CustomBar
        })
      }
    })

    //获取时间
    let nowtime = that.getDateNow(0);
    app.globalData.latestdate = nowtime;
    app.globalData.tzdate = that.getDateNow(7);
    app.globalData.pldate = nowtime;
    app.globalData.dzdate = nowtime;
    app.globalData.cgdate = nowtime;
    app.globalData.htdate = nowtime;

    // console.log(nowtime);
    let latestdate = wx.getStorageSync("sanfu_enddate");
    if (latestdate) {
      app.globalData.latestdate = latestdate;
    }
    let tzdate = wx.getStorageSync("sanfu_tzdate");
    if (tzdate) {
      app.globalData.tzdate = tzdate;
    }

    let pldate = wx.getStorageSync("sanfu_pldate");
    if (pldate) {
      app.globalData.pldate = pldate;
    }

    let dzdate = wx.getStorageSync("sanfu_dzdate");
    if (dzdate) {
      app.globalData.dzdate = dzdate;
    }

    let cgdate = wx.getStorageSync("sanfu_cgdate");
    if (cgdate) {
      app.globalData.cgdate = cgdate;
    }

    let htdate = wx.getStorageSync("sanfu_htdate");
    if (htdate) {
      app.globalData.htdate = htdate;
    }


    let openid = wx.getStorageSync("opid") || '';
    if (openid != null && openid != "") {
      app.globalData.openid = openid;
      app.globalData.session_key = app.globalData2.session_key;
      app.globalData.sanfu_sid = wx.getStorageSync("sid") || '';
      app.globalData.sanfu_id = wx.getStorageSync("cardid") || '';
      app.globalData.unionid = wx.getStorageSync("unionid") || '';

      if (app.globalData.newsID > 0) {
        that.setData({
          newsid: app.globalData.newsID
        })
      }

      if (app.globalData.userInfo == null) {
        let location = wx.getStorageSync('location') || '';
        if (location != undefined && location != '') {
          // console.log(location);
          let locationJson = location;
          if (locationJson != null) {
            app.globalData.latitude = locationJson.lat;
            app.globalData.longitude = locationJson.lon;
            app.globalData.province = locationJson.province;
            app.globalData.city = locationJson.city;
            app.globalData.address = locationJson.str;
            that.setData({
              latitude: locationJson.lat,
              longitude: locationJson.lon
            })
          }
        }
        that.sys_gettoken(); //获取token
        that.setData({
          newNuser: wx.getStorageSync('newNuser') || '0'
        })

      } else {
        that.pageload("");
      }
    } else {
      wx.showModal({
        title: "提示",
        content: "小程序打开异常，请重新打开",
        showCancel: false,
        success: function(e) {
          wx.switchTab({
            url: '/pages/find/find',
          })
        }
      })
    }
  },
  pageLifetimes: {
    show: function() {
      // 页面被展示
      let that = this;
      if (that.data.loaded) {
        if (app.globalData.newsID > 0) {
          that.setData({
            newsid: app.globalData.newsID
          })
          app.globalData.newsID = 0;
          that.getNewsModel();
        }
      }
    },
    hide: function() {
      // 页面被隐藏
    },
    resize: function(size) {
      // 页面尺寸变化
    }
  },
  methods: {



    pageload: function(channid) { //首页加载
      var that = this;
      that.setData({
        userinfo: app.globalData.userInfo,
        psize: app.globalData.pwidth / 375
      });

      if (that.data.newsid == 0) {
        that.getNewsDataList();
      } else {
        that.getNewsModel();
      }
      let shopid = wx.getStorageSync('dsho_id');
      that.setData({
        shopid: shopid
      })
    },
    bindTop: function(e) {
      wx.showToast({
        title: '您划到宇宙尽头啦',
        icon: "none",
        duration: 600
      })
    },
    getNewsDataList: function(e) { //获取数据
      let that = this;
      if (that.data.page == 1 || (that.data.page > 1 && that.data.currentpagesize == that.data.pagesize)) {
        if (!that.data.submit) {
          that.setData({
            submit: true
          })
          if (that.data.page == 1) {
            wx.showLoading({
              title: '正在加载',
            })
          }
          let page = that.data.page;
          let url = "list/content_news_home";
          let ldata = {
            id: that.data.nid,
            sqla: app.globalData.userInfo.id
          }
          if (that.data.active == 1) {
            url = "list/content_news_home";
            ldata = {
              cn_typelist: that.data.newsType[that.data.active2].id,
              sqla: app.globalData.userInfo.id,
              order: "cn_num desc,adddate_timestamp desc",
              page: page,
              pagesize: that.data.pagesize,
            };
            if (that.data.newsid > 0) {
              ldata.id = that.data.newsid;
            }
          } else {
            url = "list/content_news_follow";
            ldata = {
              ct_uid: app.globalData.userInfo.id,
              sqlliked: 1,
              sqla: app.globalData.userInfo.id,
              page: page,
              pagesize: that.data.pagesize,
            }
          }
          reqdata.POST(url, ldata, 5).then(() => {
            wx.hideLoading({
              success: (res) => {},
            })
            that.setData({
              submit: false,
              loaded: true
            })
            var user_data = app.netWorkData.result[5];
            let numlist = []
            if (user_data.code == 1000 && user_data.data.length > 0) {
              var plist = that.data.datalist;
              for (var i = 0; i < user_data.data.length; i++) {
                if (user_data.data[i].u_namebase64 != "" && user_data.data[i].u_namebase64 != "undefined") {
                  user_data.data[i].u_namebase64 = base64.decode(user_data.data[i].u_namebase64);
                }
                if (user_data.data[i].cn_picproportion == undefined || user_data.data[i].cn_picproportion == "") {
                  user_data.data[i].cn_picproportion = 0;
                }
                if (user_data.data[i].cn_piclist != "" && user_data.data[i].cn_piclist != null) {
                  if (user_data.data[i].cn_piclist.indexOf(",") > -1) {
                    user_data.data[i].cn_piclist = user_data.data[i].cn_piclist.split(",");
                  } else {
                    user_data.data[i].cn_piclist = ["" + user_data.data[i].cn_piclist + ""];
                  }
                } else {
                  user_data.data[i].cn_piclist = [];
                }
                if (user_data.data[i].cn_goodsinfo != "" && user_data.data[i].cn_goodsinfo != null) {
                  user_data.data[i].cn_goodsinfo = JSON.parse(user_data.data[i].cn_goodsinfo);
                }
                plist.push(user_data.data[i]);
              }
              that.setData({
                datalist: plist,
                page: page + 1,
                currentpagesize: user_data.data.length
              });
              if (page == 1) {
                that.setData({
                  dataIndex: 0,
                  changeIndex: 0
                })
                that.getStoreInfo();
              }
            } else {
              if (page == 1) {
                that.setData({
                  newsinfo: null,
                  page: 1,
                  currentpagesize: 10,
                  dataIndex: 0
                });
              } else {
                that.setData({
                  currentpagesize: 0
                })
              }
            }
          })
        }
      } else {
        if (that.data.page > 1) {
          wx.showToast({
            title: '您划到宇宙尽头啦',
            icon: "none",
            duration: 600
          })
        }
      }
    },


    hiddenNewUser: function(e) { //隐藏新手操作指南
      wx.setStorageSync('newNuser', "1");
      this.setData({
        newNuser: "1"
      })
    },
    videoLoaded: function(e) {},

    goclick(e) {
      this.active = e.currentTarget.dataset.index
      this.setData({
        active: e.currentTarget.dataset.index,
        datalist: [],
        page: 1,
        currentpagesize: 0
      })
      this.getNewsDataList();
    },
    // 二层切换
    goactive2(e) {
      let that = this;
      let activitylist = that.data.activityList;
      let vindex = e.currentTarget.dataset.index;
      if (vindex != undefined && !that.data.submit) {
        if (that.data.active2 != vindex) { //表示切换
          // let nowdata = {
          //     dataIndex: that.data.dataIndex,
          //     datalist: that.data.datalist,
          //     page: that.data.page,
          //     currentpagesize: that.data.currentpagesize
          // };
          // that.setData({
          //     ['activityList[' + that.data.active2 + ']']: nowdata
          // })
          // let newdata = activitylist[vindex];
          // if (newdata.datalist.length == 0) {
          that.setData({
            newsid: 0,
            dataIndex: 0,
            active2: vindex,
            datalist: [],
            page: 1,
            currentpagesize: 0
          })
          that.getNewsDataList();
          // } else {
          //     that.setData({
          //         dataIndex: newdata.dataIndex,
          //         active2: vindex,
          //         datalist: newdata.datalist,
          //         page: newdata.page,
          //         currentpagesize: newdata.currentpagesize
          //     })
          // }
        }
      }
    },

    // 模态框
    showModal(e) {
      var that = this;
      that.setData({
        modalName: e.currentTarget.dataset.target
      })
    },
    //   showModal2(e) {
    //     var that = this;
    //     that.setData({
    //       modalName: e.currentTarget.dataset.target
    //     })
    //   },
    showModal2(e) {
      var that = this;
      if (e.currentTarget.dataset.target == 'bottomModal') {
        wx.showActionSheet({
          itemList: ['短视频(60秒)', '拍照', '从相册选择'],
          success: res => {
            if (res.tapIndex == 0) {
              this.sppublishBtn()
            } else if (res.tapIndex == 1) {
              e.currentTarget.dataset.type = 'camera'
              this.publishBtn(e)
            } else if (res.tapIndex == 2) {
              e.currentTarget.dataset.type = 'album'
              this.publishBtn(e)
            }
            console.log(res.tapIndex)
          },
          fail(res) {
            console.log(res.errMsg)
          }
        })
        return
      }
      that.setData({
        modalName: e.currentTarget.dataset.target
      })
    },
    hideModal(e) {
      this.setData({
        modalName: null
      })
    },
    hideModal2(e) {
      this.setData({
        modalName2: null
      })
    },
    hideModal3(e) {
      this.setData({
        modalName3: null
      })
    },
    hideModal4(e) {
      this.setData({
        modalName4: null
      })
    },

    viewNews: function(e) { //流过来查看
      let that = this;
      let vindex = e.currentTarget.dataset.index;
      if (vindex != undefined) {
        let n = that.data.datalist[vindex];
        if (n != undefined) {
          that.setData({
            changeIndex: vindex,
            dataIndex: vindex,
            feed: 1
          })
        }
      }
    },
    // 右侧点击
    goclick2(e) {
      let that = this;
      let id = e.currentTarget.dataset.id
      if (id > 2) {}
      if (id == 1) { //收藏
        that.addfavorite(e);
      } else if (id == 2) {
        this.sendShare()
        Share({
          context: this
        }).then(el => {}).catch(el => {
          wx.navigateTo({
            url: `/sfsns/pages/cjbd/share?id=${this.data.datalist[this.data.dataIndex].id}&size=${this.data.datalist[this.data.dataIndex].cn_picproportion}`
          })
        })
      } else if (id == 3) { //购买
        let n = that.data.datalist[that.data.dataIndex];
        if (n.goodsInfo == undefined) {
          let ginfo = n.cn_goodsinfo;
          if (ginfo != "") {
            let goods = ginfo;
            let goodsid = "";
            for (let i = 0; i < goods.length; i++) {
              if (n.cn_videourl != '') {
                goodsid += (goodsid == "" ? "" : ",") + goods[i].goods_sn;
              } else {
                if (goods[i].tags != undefined && goods[i].tags != "") {
                  for (let j = 0; j < goods[i].tags.length; j++) {
                    goodsid += (goodsid == "" ? "" : ",") + goods[i].tags[j].goods_sn;
                  }
                }
              }
            }
            app.reqGet('ms-sanfu-wap-goods/listGoodsDetail?goodSn=' + goodsid + "&shoId=" + that.data.shopid + "&sid=" + app.globalData.sanfu_sid, {}, dare => {
              n.goodsInfo = dare.data;
              that.setData({
                ['datalist[' + that.data.dataIndex + ']']: n
              })
              that.setData({
                modalName3: 'goumai',
                shop: true
              })
            })
          }
        } else {
          that.setData({
            modalName3: 'goumai',
            shop: true
          })
        }
      } else if (id == 4) {
        that.setData({
          modalName4: 'pinglun'
        })
      }
    },


    addfavorite: function(e) { //收藏
      let that = this;
      if (app.globalData.userInfo == null || app.globalData.userInfo.u_sanfu_sid == null || app.globalData.userInfo.u_faceurl == null) {
        this.setData({
          modalName: "authorization"
        })
        return;
      }
      let ninfo = null;
      let vindex = e.currentTarget.dataset.index;
      let vtype = e.currentTarget.dataset.type;
      if (vtype == 3) {
        ninfo = that.data.datalist[that.data.dataIndex];
      } else {
        if (vindex != undefined) {
          ninfo = that.data.datalist[vindex];
        }
      }

      if (ninfo != undefined && !that.data.submit) {
        wx.showToast({
          icon: "loading",
          duration: 1000
        })
        this.setData({
          submit: true
        })

        reqdata.addLiked(ninfo.id, ninfo.cn_uid, ninfo.sqlliked > 0 ? true : false, 9, ninfo.cn_like).then(() => {
          //请求成功的操作
          var user_data = app.netWorkData.result[9];
          this.setData({
            submit: false
          })
          wx.hideToast();
          if (user_data.code == 1000) {
            if (ninfo.sqlliked > 0) {
              wx.showToast({
                title: '取消成功',
                icon: "none",
                duration: 800
              })
              ninfo.sqlliked = 0;
              ninfo.cn_like = parseInt(ninfo.cn_like) - 1;
            } else {
              wx.showToast({
                title: '已放入“我的 - 我的喜欢”',
                icon: "none",
                duration: 800
              })
              reqdata.addFubi(1);
              ninfo.sqlliked = 1;
              ninfo.cn_like = parseInt(ninfo.cn_like) + 1;
            }
            let d = that.data.datalist;
            if (vtype == 3) {
              that.setData({
                ['datalist[' + that.data.dataIndex + ']']: ninfo
              })
            } else {
              that.setData({
                ['datalist[' + vindex + ']']: ninfo
              })

            }
          } else {
            wx.showModal({
              title: '提示',
              content: user_data.msg,
              showCancel: false
            })
          }
        });
      }
    },



    addfans: function(e) { //关注
      let that = this;
      if (app.globalData.userInfo == null || app.globalData.userInfo.u_sanfu_sid == null || app.globalData.userInfo.u_faceurl == null) {
        this.setData({
          modalName: "authorization"
        })
        return;
      }
      let vindex = that.data.dataIndex;
      let ninfo = that.data.datalist[vindex];
      if (ninfo != undefined) {
        if (!that.data.submit) {
          wx.showLoading({
            title: '正在关注',
          })
          this.setData({
            submit: true
          })

          reqdata.addFans(ninfo.cn_uid, "文章页关注", ninfo.sqlfans > 0 ? true : false, 6).then(() => {
            var user_data = app.netWorkData.result[6];
            wx.hideLoading({
              success: (res) => {},
            })
            this.setData({
              submit: false
            })
            if (user_data.code == 1000) {
              if (ninfo.sqlfans) {
                wx.showToast({
                  title: '取关成功',
                  icon: "",
                  duration: 800
                })
                ninfo.sqlfans = 0;
              } else {
                wx.showToast({
                  title: '关注成功',
                  icon: "",
                  duration: 800
                })
                ninfo.sqlfans = 1;
              }
              that.setData({
                ['datalist[' + vindex + ']']: ninfo
              })
            } else {
              wx.showModal({
                title: '提示',
                content: user_data.msg,
                showCancel: false
              })
            }
          });
        }
      }
    },

    viewaddflower: function(e) { //生成海报
      let that = this;
      let ninfo = that.data.datalist[that.data.dataIndex];
      if (ninfo != undefined) {
        ninfo.cn_forward = parseInt(ninfo.cn_forward + 1);
        that.setData({
          ['datalist[' + that.data.dataIndex + ']']: ninfo
        })
      }
    },

    // 菜单
    indexTab: function() {
      wx.redirectTo({
        url: "../index/index",
      })
    },
    depthTab: function() {
      wx.redirectTo({
        url: "../depth/index",
      })
    },
    newsTab: function() {
      wx.redirectTo({
        url: "/sfsns/pages/mine/minenew/minenew",
      })
    },
    // 切换feed模式
    gofeed(e) {
      if (this.data.newNuser === '0') return
      let that = this;
      if (e.currentTarget.dataset.feed == 0) { //切换至列表
        that.setData({
          feed: e.currentTarget.dataset.feed
        })
        if (that.data.dataIndex > 4) {
          let lsize = parseInt((that.data.dataIndex / 2).toFixed(0)) * 290;
          that.setData({
            scrolltop: lsize
          })
          // console.log(lsize);
          // wx.pageScrollTo({
          //     scrollTop: lsize,
          //     duration: 300
          // });
        }
      } else {
        let query = wx.createSelectorQuery();
        let nodes = query.selectAll('.card-pbl-son');
        nodes.fields({
          rect: true
        }, data => {
          console.log(data);
          if (data.length > 0) {
            let num = 0;
            for (let i = 0; i < data.length; i++) {
              if (data[i].top > 0) {
                num = i * 2;
                break;
              }
            }
            that.setData({
              changeIndex: num,
              feed: e.currentTarget.dataset.feed
            })
          }
        }).exec();
      }
    },
    // 推荐关注
    gorecommend() {
      wx.navigateTo({
        url: '/sfsns/pages/follow/recommend/recommend',
      })
    },
    // 点击购物车,显示购物车组件
    gocart(e) {
      let that = this;
      let vindex = e.currentTarget.dataset.index;
      if (vindex != undefined) {
        let goods = that.data.datalist[that.data.dataIndex].goodsInfo[vindex];
        if (goods != undefined) {
          app.$bus.emit('setSkuGoods', {
            goods: goods
          })
        }
      }
    },


    // 短视频
    sppublishBtn: function() {
      reqdata.uploadVideo();

    },
    publishBtn: function(e) {
      let atype = e.currentTarget.dataset.type;
      reqdata.uploadImage(atype, 9);

    },
    // 加购商品的展开
    pack() {
      let that = this;
      that.setData({
        packShow: !that.data.packShow
      })
    },
    pack2() {
      let that = this;
      that.setData({
        packShow2: !that.data.packShow2
      })
    },
    // 选择颜色
    colorClick(e) {
      let that = this;
      that.setData({
        coloract: e.currentTarget.dataset.index
      })
    },
    /*搜索*/
    onSearchTap: function() {
      wx.navigateTo({
        url: "/sfsns/pages/search/index",
      })
    },
    // 防止蒙版层穿透
    preventTouchMove() {},
    // 点击评论的爱心
    golove() {
      console.log('eeewq')
      let that = this;
      that.setData({
        love: !that.data.love
      })
      console.log(that.data.love)
    },



    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function() {},


    //滚动条下拉到底
    bindBottom: function() {
      this.getNewsDataList();
    },


    newstouchStart: function(e) { //资讯拖动开始
      startPoint = e.touches[0] //获取拖动开始点
    },

    swiperanimationfinish: function(e) {
      let that = this;
      if (e.detail.source == "touch") {
        let vindex = e.detail.current;
        if (vindex != undefined) {
          that.setData({
            playIndex: 0
          })
          let oldIndex = that.data.dataIndex;
          let oinfo = that.data.datalist[oldIndex];

          if (oldIndex == vindex) { //相等
            if (that.data.modalName3 == null) {
              if (vindex == 0) {
                wx.showToast({
                  title: '您划到宇宙尽头啦',
                  icon: "none",
                  duration: 400
                })
              } else if (that.data.currentpagesize != that.data.pagesize && vindex == that.data.datalist.length - 1) {
                wx.showToast({
                  title: '您划到宇宙尽头啦',
                  icon: "none",
                  duration: 400
                })
              }
            }
          } else {
            if (oinfo != undefined && oinfo.cn_videourl != undefined && oinfo.cn_videourl != '') { //停止视频播放

              this.playEnd()
            }
            if (vindex < oldIndex) { //往前翻
              if (vindex == 0) {
                that.setData({
                  dataIndex: vindex
                })
              } else {
                that.setData({
                  dataIndex: vindex
                })
              }
            } else { //往后翻
              if (vindex < that.data.datalist.length - 1) { //表示还有
                that.setData({
                  dataIndex: vindex
                })
                that.getStoreInfo();
              } else { //最后一页
                if (!that.data.submit) {
                  if (that.data.currentpagesize == that.data.pagesize && vindex == that.data.datalist.length - 1) {
                    that.setData({
                      dataIndex: vindex
                    })
                    that.getStoreInfo();
                    that.getNewsDataList();
                  } else {
                    that.setData({
                      dataIndex: vindex
                    })

                  }
                }
              }
            }
          }
        }
      }
    },

    newstouchEnd: function(e) { //资讯拖动结束
      // let that = this;
      // let endPoint = e.changedTouches[0];
      // if (startPoint != null && endPoint != undefined) {
      //     let changy = startPoint.pageY - endPoint.pageY;
      //     if (changy > 150) { //下一页
      //         if (that.data.dataIndex < that.data.datalist.length - 1) { //表示还有
      //             let dindex = that.data.dataIndex + 1;
      //             let newinfo = that.data.datalist[dindex];
      //             that.setData({
      //                 dataIndex: dindex,
      //                 newsinfo: newinfo
      //             })
      //             that.getStoreInfo();
      //         } else {
      //             if (!that.data.submit) {
      //                 if (that.data.currentpagesize == that.data.pagesize && that.data.dataIndex == that.data.datalist.length - 1) {
      //                     that.getNewsDataList();
      //                 } else {
      //                     wx.showToast({
      //                         title: '您划到宇宙尽头啦',
      //                         icon: "none",
      //                         duration: 400
      //                     })
      //                 }
      //             }
      //         }
      //     } else if (changy < -150) { //下一页
      //         if (that.data.dataIndex > 0) {
      //             let newinfo = that.data.datalist[that.data.dataIndex - 1];
      //             that.setData({
      //                 dataIndex: that.data.dataIndex - 1,
      //                 newsinfo: newinfo
      //             })
      //             that.getStoreInfo();
      //         } else {
      //             if (that.data.newsView != null && that.data.dataIndex == 0) {
      //                 that.setData({
      //                     dataIndex: -1,
      //                     newsinfo: that.data.newsView
      //                 })
      //                 that.getStoreInfo();
      //             } else {
      //                 wx.showToast({
      //                     title: '已经到头了',
      //                     icon: "none",
      //                     duration: 400
      //                 })
      //             }
      //         }
      //     }
      // }
    },

    //以下是按钮拖动事件
    buttonStart: function(e) {
      startPoint = e.touches[0] //获取拖动开始点
    },
    buttonMove: function(e) {
      let that = this;
      // this.setData({
      //   buttonRight: 0,
      // })
      var endPoint = e.touches[e.touches.length - 1] //获取拖动结束点
      //计算在X轴上拖动的距离和在Y轴上拖动的距离
      var translateX = endPoint.clientX - startPoint.clientX;
      if (translateX > 100) {}
      //var translateY = endPoint.clientY - startPoint.clientY
      //console.log(endPoint.clientX,startPoint.clientX,translateX);
      //startPoint = endPoint //重置开始位置
      // var buttonTop = this.data.buttonTop + translateY
      // var buttonLeft = this.data.buttonLeft + translateX
      // //判断是移动否超出屏幕
      // if (buttonLeft + 50 >= this.data.windowWidth) {
      //   buttonLeft = this.data.windowWidth - 50;
      // }
      // if (buttonLeft <= 0) {
      //   buttonLeft = 10;
      // }
      // if (buttonTop <= 0) {
      //   buttonTop = 0
      // }

      // if (buttonTop + 50 >= this.data.windowHeight) {
      //   buttonTop = this.data.windowHeight - 50;
      // }
      // if (buttonTop < 80) {
      //   buttonTop = 80
      // }
      // this.setData({
      //   buttonTop: buttonTop,
      //   buttonLeft: buttonLeft,
      //   classIsShow: false
      // })

      // app.globalData.buttonTop = buttonTop;
      // app.globalData.buttonLeft = buttonLeft;

    },
    buttonEnd: function(e) {
      let that = this;
      // this.setData({
      //   buttonRight: 0,
      // })
      var endPoint = e.changedTouches[0] //获取拖动结束点
      //计算在X轴上拖动的距离和在Y轴上拖动的距离
      var translateX = endPoint.clientX - startPoint.clientX;
      //console.log(translateX);
      if (translateX > 100) {
        let query = wx.createSelectorQuery();
        let nodes = query.selectAll('.card-pbl-son');
        nodes.fields({
          rect: true
        }, data => {
          console.log(data);
          if (data.length > 0) {
            let num = 0;
            for (let i = 0; i < data.length; i++) {
              if (data[i].top > 0) {
                num = i * 2;
                break;
              }
            }
            that.setData({
              changeIndex: num,
              feed: 1
            })
          }
        }).exec();
      }
      // let that = this;
      // let offsetLeft = e.currentTarget.offsetLeft;
      // let offsetTop = e.currentTarget.offsetTop;
      // let totle = that.data.windowWidth / 2;
      // if (totle > offsetLeft) {
      //   this.setData({
      //     buttonTop: offsetTop,
      //     buttonLeft: 10,
      //     // classIsShow: !that.data.classIsShow
      //   })
      //   app.globalData.buttonLeft = 10;
      //   app.globalData.buttonRight = null;

      // } else {
      //   this.setData({
      //     buttonRight: 10,
      //     // buttonLeft: '',
      //     buttonTop: offsetTop,
      //     // classIsShow: !that.data.classIsShow
      //   })
      //   app.globalData.buttonRight = 10;
      //   app.globalData.buttonLeft = null;

      // }
    },

    getStoreInfo: function(e) { //获取店铺信息
      let that = this;
      let ninfo = that.data.datalist[that.data.dataIndex];
      if (ninfo != null) {
        this.sendShare()
        reqdata.updateNewsHot(ninfo.id, ninfo.cn_like, 0); //更新热度值
        if (ninfo.u_token != null && ninfo.u_token != "" && ninfo.shopInfo == undefined) {
          app.reqGet("ms-sanfu-wechat-common/nearShop?shoId=" + ninfo.u_token, {}, dare => {
            ninfo.shopInfo = dare.data;
            if (that.data.latitude > 0) {
              ninfo.shopInfo.distance = that.getDistance(that.data.latitude, that.data.longitude, dare.data.latitude, dare.data.longitude);
            }
            that.setData({
              ['datalist[' + that.data.dataIndex + ']']: ninfo
            })
          })

        }
      }
    },

    addFavoriveProduct: function(e) { //添加收藏
      let that = this;
      let id = e.currentTarget.dataset.id;
      let vindex = e.currentTarget.dataset.index;

      if (id != undefined && vindex != undefined) {
        if (!that.data.submit) {
          that.setData({
            submit: true
          })
          wx.showLoading({
            title: '正在收藏',
          })

          let ddata = {
            sid: app.globalData.sanfu_sid,
            goods_id: id
          };
          app.reqPost('ms-sanfu-wap-goods/saveCollect', ddata, dare => {
            wx.hideLoading({
              success: (res) => {},
            })
            that.setData({
              submit: false
            })
            let n = that.data.datalist[that.data.dataIndex];
            n.goodsInfo[vindex].isCollect = 1;
            that.setData({
              ['datalist[' + that.data.dataIndex + ']']: n
            })
            wx.showToast({
              title: '收藏成功',
              duration: 800
            })
          })

        }
      }
    },
    cancleFavoriveProduct: function(e) { //取消收藏
      let that = this;
      let id = e.currentTarget.dataset.id;
      let vindex = e.currentTarget.dataset.index;

      if (id != undefined && vindex != undefined) {
        if (!that.data.submit) {
          that.setData({
            submit: true
          })
          wx.showLoading({
            title: '正在收藏',
          })

          let ddata = {
            sid: app.globalData.sanfu_sid,
            goods_id: id
          };
          app.reqPost("ms-sanfu-wap-goods/deleteCollect", ddata, dare => {
            wx.hideLoading({
              success: (res) => {},
            })
            that.setData({
              submit: false
            })
            let n = that.data.datalist[that.data.dataIndex];
            n.goodsInfo[vindex].isCollect = 0;
            that.setData({
              ['datalist[' + that.data.dataIndex + ']']: n
            })
            wx.showToast({
              title: '取消收藏成功',
              duration: 800
            })
          })

        }
      }
    },

    getDistance: function(latFrom, lngFrom, latTo, lngTo) { //计算距离
      var rad = function(d) { //计算角度
        return d * Math.PI / 180.0;
      }
      var EARTH_RADIUS = 6378136.49;
      var radLatFrom = rad(latFrom);
      var radLatTo = rad(latTo);
      var a = radLatFrom - radLatTo;
      var b = rad(lngFrom) - rad(lngTo);
      var distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLatFrom) * Math.cos(radLatTo) * Math.pow(Math.sin(b / 2), 2)));
      distance = distance * EARTH_RADIUS;
      distance = Math.round(distance * 10000) / 10000000;
      return parseFloat(distance.toFixed(1));
    },

    getNewsModel: function() { //获取帖子详情
      let that = this;
      if (that.data.newsid > 0) {
        let viewurl = "model/content_news_model";
        let viewdata = {
          id: that.data.newsid,
          sqla: app.globalData.userInfo.id
        };
        reqdata.POST(viewurl, viewdata, 15).then(() => {
          wx.hideLoading({
            success: (res) => {},
          })
          var user_model = app.netWorkData.result[15];
          if (user_model.code == 1000 && user_model.data != null) {
            let newsmodel = user_model.data;
            if (newsmodel.u_namebase64 != "" && newsmodel.u_namebase64 != "undefined") {
              newsmodel.u_namebase64 = base64.decode(newsmodel.u_namebase64);
            }
            if (newsmodel.cn_picproportion == undefined || newsmodel.cn_picproportion == "") {
              newsmodel.cn_picproportion = 0;
            }
            if (newsmodel.cn_piclist != "" && newsmodel.cn_piclist != null) {
              if (newsmodel.cn_piclist.indexOf(",") > -1) {
                newsmodel.cn_piclist = newsmodel.cn_piclist.split(",");
              } else {
                newsmodel.cn_piclist = ["" + newsmodel.cn_piclist + ""];
              }
            } else {
              newsmodel.cn_piclist = [];
            }
            if (newsmodel.cn_goodsinfo != "" && newsmodel.cn_goodsinfo != null) {
              newsmodel.cn_goodsinfo = JSON.parse(newsmodel.cn_goodsinfo);
            }
            let channid = newsmodel.cn_typelist;
            if (channid != undefined && channid != "") {
              channid = channid.replace(",", "");
              for (let i = 0; i < that.data.newsType.length; i++) {
                if (that.data.newsType[i].id == channid) {
                  that.setData({
                    active2: i
                  })
                  break;
                }
              }
            }

            let newslist = [];
            newslist.push(newsmodel);
            that.setData({
              datalist: newslist,
              newsView: newsmodel,
              dataIndex: 0,
              changeIndex: 0,
              feed: 1,
              active: 1,
              page: 1,
            });
            that.getNewsDataList();
          } else {
            wx.navigateBack({
              delta: 0,
            })
          }
        });
      }
    },

    // 监听页面滚动距离
    onPageScroll: function(e) {},

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function() {},

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function() {},
    BackPage: function(e) {
      if (app.globalData.reBackUrl != undefined && app.globalData.reBackUrl != "") {
        this.setData({
          newsid: 0
        })

        wx.navigateTo({
          url: '/' + app.globalData.reBackUrl,
        })
        app.globalData.reBackUrl = "";
      }
    },

    getshowadlist: function(e) { //显示广告
      var that = this;
      var iurl = "list/advert_list";
      var idata = {
        "a_loc": "3",
        "page": 1,
        "pagesize": 1
      }
      reqdata.POST(iurl, idata, 14).then(() => {
        //请求成功的操作
        var redata = app.netWorkData.result[14];
        if (redata.code == 1000 && redata.data.length > 0) {
          that.setData({
            showadlist: redata.data
          })
        }
      });
    },

    openShop: function(e) { //打开shop信息
      let that = this;
      let shopInfo = that.data.datalist[that.data.dataIndex].shopInfo;
      if (shopInfo != null) {
        wx.openLocation({
          latitude: parseFloat(shopInfo.latitude),
          longitude: parseFloat(shopInfo.longitude),
          name: shopInfo.shoName,
          address: shopInfo.address,
          scale: 15,
          fail: res => {
            wx.showToast({
              title: '位置打开失败',
              icon: 'none',
              duration: 2500
            })
          }
        })
      }
    },
    sendShare: function(e) { //分享
      let that = this;
      let ninfo = that.data.datalist[that.data.dataIndex];
      if (ninfo != undefined) {
        reqdata.updateNewsForward(ninfo.id, ninfo.cn_uid);
        ninfo.cn_forward = parseInt(ninfo.cn_forward + 1);
        that.setData({
          ['datalist[' + that.data.dataIndex + ']']: ninfo
        })
        var vurl = 'pages/find/share?id=' + ninfo.id;
        this.triggerEvent('share', {
          title: ninfo.cn_title,
          imageUrl: ninfo.cn_piclist[0],
          path: vurl
        })
      }
    },

    sys_gettoken: function() { //获取souyisitoken
      var that = this;
      var vtime = Date.now();
      var vkey = md5.hexMD5(app.globalData.appid + vtime + app.globalData.openid + app.globalData.appkey);
      vkey = vkey.substring(0, 20);
      var idata = {
        "unionid": app.globalData.unionid,
        "sanfuid": app.globalData.sanfu_id,
        "openid": app.globalData.openid,
        "appid": app.globalData.appid,
        "time": vtime,
        "appkey": vkey
      }
      wx.request({
        url: (app.globalData.sys_url + "accesstoken/accountnew"),
        header: {
          'content-type': 'application/x-www-form-urlencoded',
        },
        data: idata,
        method: 'POST',
        success: sysres => {
          if (sysres.data.code == 1000) {
            app.globalData.userInfo = sysres.data.token;
            that.getlevel();
            that.getTags();
            if (app.globalData.userInfo.u_namebase64 != null && app.globalData.userInfo.u_namebase64 != "undefined") {
              var dcode = base64.decode(app.globalData.userInfo.u_namebase64);
              if (dcode != "") {
                app.globalData.userInfo.u_namebase64 = dcode;
                app.globalData.userInfo.adddate = reqdata.getTimestamp();
              }
            } else {
              that.setData({ //表示没有授权
                authorization: true
              })
            }
            that.getUserAttr();
            that.updateUserInfo();
            that.sys_getaddress();
          } else {
            wx.showModal({
              title: '提示',
              content: sysres.data.msg,
              showCancel: false,
              success: function(res) {
                wx.switchTab({
                  url: '/pages/find/find',
                })
              }
            })
          }
        }
      })
    },

    getUserAttr: function(e) { //获取用户属性
      let that = this;
      if (app.globalData.userInfo != null && app.globalData.userInfo.id != undefined && app.globalData.userInfo.id > 0) {
        //获取等级权重
        wx.request({
          url: app.globalData.sys_url + "model/users_attribute_model",
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'authorization': app.globalData.userInfo.token
          },
          data: {
            uaUID: app.globalData.userInfo.id
          },
          method: 'POST',
          success: function(res) {
            let redata = res.data;
            if (redata.code == 1000) {
              let u = app.globalData.userInfo;
              u.u_whether_enable_posting = redata.data.uaOpenAddNews;
              u.u_token = redata.data.uaSopNumber;
              app.globalData.userInfo = u;
              that.setData({
                userinfo: u
              })
            }
          }
        })
      }
    },
    getUserInfo: function(e) { //获取用户信息

      this.setData({
        hasHeadImg: true,
        authorization: false
      })
      this.updateUserInfo();
      this.hiddenNewUser()
    },
    updateUserInfo: function() { //更新会员信息
      let that = this;
      if (app.globalData.userInfo != undefined && (app.globalData.userInfo.u_faceurl == null || app.globalData.userInfo.u_faceurl == "" || app.globalData.userInfo.u_namebase64 == "" || app.globalData.userInfo.u_namebase64 == "undefined") && app.globalData.sanfu_sid != '') {
        app.reqGet('ms-sanfu-wap-customer/index/baseInfo', {
          sid: wx.getStorageSync('sid')
        }, ree => {
          if (ree.success) {
            let u = app.globalData.userInfo;
            u.u_faceurl = ree.data.headImgUrl;
            if (ree.data.nickname != "") {
              u.u_namebase64 = ree.data.nickname;
              app.globalData.userInfo = u;
              let nname = ree.data.nickname;
              if (nname != undefined) {
                //if (nname.indexOf("\\u") > -1) {
                nname = base64.encode(nname);
                //}
              }
              let faceurl = ree.data.headImgUrl;
              var idata = {
                "id": app.globalData.userInfo.id,
                "u_namebase64": nname,
                "u_faceurl": faceurl,
                "u_sanfu_sid": u.u_sanfu_sid,
                "u_unionid": u.u_unionid,
                "u_openid": u.u_openid,
                "u_gender": u.u_gender,
                "u_channel_list": u.u_channel_list
              };

              wx.request({
                url: app.globalData.sys_url + "accesstoken/updateuser", //+ "update/users_update",
                header: {
                  'content-type': 'application/x-www-form-urlencoded',
                  'authorization': app.globalData.userInfo.token
                },
                data: idata,
                method: 'POST',
                success: (sysres) => {}
              });


            }
          }
        })

      }
    },

    getlevel: function() { //获取会员等级 
      let that = this;
      if (app.globalData.userInfo != null) {
        wx.request({
          url: app.globalData.sys_url + "model/users_level_model",
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'authorization': app.globalData.userInfo.token
          },
          data: {
            "ul_level": app.globalData.userInfo.u_level
          },
          method: 'POST',
          success: function(res) {
            app.globalData.sanfu_fubi = res.data.data;
          }
        })
        //获取等级权重
        wx.request({
          url: app.globalData.sys_url + "model/content_power_latest",
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'authorization': app.globalData.userInfo.token
          },
          data: {},
          method: 'POST',
          success: function(res) {
            app.globalData.newsPower = res.data.data;
            that.setData({
              newsPower: res.data.data
            })
          }
        })
      }
    },
    getTags: function() { //获取标签 
      let that = this;
      if (app.globalData.userInfo != null) {
        wx.request({
          url: app.globalData.sys_url + "list/content_tags_list",
          header: {
            'content-type': 'application/x-www-form-urlencoded',
            'authorization': app.globalData.userInfo.token
          },
          data: {
            "ul_level": app.globalData.userInfo.u_level
          },
          method: 'POST',
          success: function(res) {
            app.globalData.tags = res.data.data;
          }
        })
      }
    },

    getDateNow: function(adddays) { //获取日期
      var dd = new Date();
      dd.setDate(dd.getDate() - adddays); //获取AddDayCount天以前的日期 
      var y = dd.getFullYear();
      var m = dd.getMonth() + 1; //获取当前月份的日期 
      var d = dd.getDate();
      if (m < 10) {
        m = '0' + m;
      };
      if (d < 10) {
        d = '0' + d;
      };
      return y + "/" + m + "/" + d + " " + dd.getHours() + ":" + dd.getMinutes() + ":" + dd.getSeconds();
    },

    sys_getaddress: function() { //获取地址百度地图
      var that = this;
      wx.request({
        url: 'https://api.map.baidu.com/geocoder/v2/?callback=&location=' + app.globalData.latitude + ',' + app.globalData.longitude + '&output=json&pois=1&ak=pX97mLLlDoEe0gHS3v2me3B3XKORsUUw',
        success: function(res) {
          var vobj = res.data.result.addressComponent;
          app.globalData.province = vobj.province;
          app.globalData.city = vobj.city;
          app.globalData.address = res.data.result.formatted_address;
          if (app.globalData.userInfo.ut_uid == null) {
            var idata = {
              "id": app.globalData.userInfo.id,
              "ut_uid": app.globalData.userInfo.id,
              "u_longitude": app.globalData.longitude,
              "u_latitude": app.globalData.latitude,
              "u_citys": vobj.city
            }
            wx.request({
              url: app.globalData.sys_url + "add/users_total_add",
              header: {
                'content-type': 'application/x-www-form-urlencoded',
                'authorization': app.globalData.userInfo.token
              },
              data: idata,
              method: 'POST',
              success: dare => {
                if (dare.data.code == 1000) {
                  let uinfo = app.globalData.userInfo;
                  uinfo.ut_uid = uinfo.id;
                  uinfo.u_liked = 0;
                  uinfo.u_fans = 0;
                  uinfo.u_received_liked = 0;
                  uinfo.u_level = 0;
                  uinfo.u_tags = "";
                  uinfo.u_citys = vobj.city;
                  uinfo.u_longitude = app.globalData.longitude;
                  uinfo.u_latitude = app.globalData.latitude;
                  app.globalData.userInfo = uinfo;
                  that.pageload("");
                } else {
                  that.pageload("");
                }
              }
            })
          } else {
            that.pageload("");
          }
        },
        fail: function(res) {
          that.pageload("");
        }
      });
    },
    pictureView(e) { //图片预览
      wx.previewImage({
        current: e.currentTarget.dataset.src,
        urls: e.currentTarget.dataset.list
      })
    },
    openVideo(e) {
      // 打开视频
      if (this.data.playStatus == 0 || this.data.playStatus == 4) {
        let oldIndex = this.data.dataIndex;
        let context = wx.createVideoContext('video' + this.data.active2 + "-" + oldIndex, this)
        this.setData({
          playStatus: 1
        })
        context && context.play()
      } else if (this.data.playStatus == 1) {
        let oldIndex = this.data.dataIndex;
        let context = wx.createVideoContext('video' + this.data.active2 + "-" + oldIndex, this)
        context && context.requestFullScreen()
      }
    },
    playfullscreen(e) {
      this.setData({
        playStatus: e.detail.fullScreen ? 2 : 1
      })
    },
    playMute() {
      this.setData({
        videoSound: this.data.videoSound ? false : true
      })
    },
    playEnd(e) {
      let oldIndex = this.data.dataIndex;
      let context = wx.createVideoContext('video' + this.data.active2 + "-" + oldIndex, this)
      context && context.exitFullScreen()
      context.stop();
      this.setData({
        playStatus: 0,
        videoSound: false
      })
    },
  }
})
