<wxs src="../../../../utils/utils.wxs" module="utils"></wxs>
<navbar id="navbar" isBack showMenuBtn navStyle="z-index:998;" title="商品推荐">
  <view class="top">
    <view class="nav">
      <view class="{{current==0?'active':''}}" style="border-right: 2rpx solid #ddd;" bindtap="changeList" data-i="0">单品推荐</view>
      <view class="{{current==1?'active':''}}" bindtap="changeList" data-i="1">组合搭配推荐</view>
    </view>
  </view>
</navbar>
<view style="height: 120rpx;" ></view>



<view class="goo-container"  wx:if="{{current==0}}">
  <view class="goo-item" hover-class="hover" hover-start-time="20" hover-stay-time="100" wx:for="{{list[0].data}}" wx:key="index" bindtap="openGoods" data-id="{{item.goodsSn}}" wx:if="{{item.lImg}}">
    <image mode="aspectFill" class="goo-img" lazy-load  src="{{utils.jpg2jpeg(item.lImg)}}" />
    <view class="goo-name">{{utils.replaceg(item.goodsName,'三福', '')}}</view>
    <view class="comb-other">
      <text class="goo-price"><text style="font-weight: 400;">¥</text><text>{{item.salePrice}}</text></text>
      <view wx:if="{{!discard}}" class="iconDG-fenxiang f7" catchtap="showShare" data-index="{{index}}"></view>
    </view>
  </view>
</view>

<view class="comb-container" wx:if="{{current==1}}">
  <view class="comb-item" hover-class="hover" hover-start-time="20" hover-stay-time="100" wx:for="{{list[1].data}}" wx:key="index" bindtap="openComb" data-id="{{item.combId}}">
    <image mode="aspectFill" class="comb-img" lazy-load  src="{{utils.jpg2jpeg(item.coverUrl)}}" />
    <view class="comb-content">
      <view class="comb-title">{{item.combTitle}} </view>
      <view class="comb-subtitle">{{item.combDesc}}</view>
      <view class="comb-other">
        <view>查看详情<text class="uni-icon uni-icon-arrowright"></text></view>
        <view wx:if="{{!discard}}" class="iconDG-fenxiang f7" catchtap="showShare" data-index="{{index}}"></view>
      </view>
    </view>
  </view>
</view>


<!-- <block wx:if="{{list[current]}}"> -->
<view wx:if="{{list[current].has_more != 4}}" class="show_end" bindtap="getList">
  <view wx:if="{{list[current].has_more == 1}}" class="loadmore-icon"></view>
  <text>{{ loadingText[list[current].has_more] }}</text>
</view>
<!-- </block> -->


<share id="sanfu-share" />

<!-- 分享朋友圈二维码 -->
<canvas type="2d" id="shareCanvas" class="shareCanvas" style="width:{{canvasWidth}}px;height:{{canvasHeight}}px;position:fixed;top:-{{canvasHeight||9999}}px;right:-{{canvasHeight}}rpx;margin-top:-1000px;margin-right:-1000px;"></canvas>
<view hidden="{{showqrcode}}" class="qrcode">
  <image lazy-load mode="aspectFit" src="{{tmppath}}" style="height:{{canvasHeight}}px;width:630rpx;max-height:85%;border-radius: 20rpx;"></image>
  <view class="dis-top">
    <view style="background:white;width:40%;flex:none;" bindtap="closeQR">关闭</view>
    <view style="background:white;width:40%;flex:none;" bindtap="saveImage">保存</view>
  </view>
</view>

<sf-loading wx:if="{{showLoading}}" full/>