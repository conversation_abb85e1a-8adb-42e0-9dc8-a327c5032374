@import "/font/icon_f3.wxss";

.confirm {
  width: 100%;
  text-align: center;
  font-size: 30rpx;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 30rpx;
}

.selectAddress {
  background: #ffffff;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  font-size: 26rpx;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx 30rpx;
}

.serDes {
  width: 100%;
  color: #333333;
  height: 32rpx;
  line-height: 32rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
  font-weight: bold;
}

.description {
  flex: 1;
  position: relative;
}

.vip {
  font-size: 27rpx;
  color: #474747;
  margin-top: 20rpx;
  overflow-y: scroll;
}

.vipDiscount {
  text-decoration: underline;
  font-size: 20rpx;
  color: rgb(164, 164, 164);
  padding-left: 40rpx;
}
.closeIcon{
  vertical-align: middle;
  color: #999;
  font-size: 32rpx;
  float: right;
  font-weight: 400;
  padding: 10rpx;
  margin: -10rpx -10rpx 0 0;
}
.otherDes {
  font-size: 20rpx;
  color: rgb(164, 164, 164);
  padding-left: 40rpx;
}

.yd-cell-left {
  width: 90%;
  color: #565656;
}

.act-item {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  font-size: 32rpx;
  padding: 30rpx 0;
  width: 100%;
  color: #333333;
}

.act-item-0 {
  display: flex;
  font-size: 36rpx;
  color: #c9c9c9;
  align-items: center;
}

.act-item-1 {
  flex: 1;
  font-size: 28rpx;
  color: #555555;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.act-item-2 {
  font-size: 26rpx;
  color: #525252;
  display: flex;
  align-items: center;
}

.act-item-3 {
  display: flex;
  align-items: center;
  margin-left: 24rpx;
}
.act-item-3>view{
  font-size: 36rpx;
  color: #E60012;
}

.contacts {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.contactsName {
  font-weight: bold;
  margin-right: 16rpx;
  height: 32rpx;
  line-height: 32rpx;
  display: inline-block;
}

.mobile {
  font-size: 28rpx;
  color: #999999
}

.defaultAdr {
  font-size: 20rpx;
  background: #f00;
  color: #fff;
  padding: 0 10rpx;
  margin-right: 05rpx;
  border-radius: 05rpx
}

.scroll-view {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
}

.act-adress {
  width: 100%;
  overflow: hidden;
  color: #333333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  line-height: 1.3;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 超出几行省略 */
  overflow: hidden;
}

.location {
  vertical-align: middle;
  font-size: 34rpx;
}
.borderBottom{
  border-bottom:  1rpx solid #F2F2F2;
}
.addAddress{
  padding: 24rpx 0;
  flex: 1;
  border: 1px solid #E60012;
  border-radius: 50px;
  color: #E60012;
  font-weight: bold;
  font-size: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  box-sizing: content-box;
}
.manageAddress{
  padding: 24rpx 0;
  flex: 1;
  border-radius: 50px;
  color: #ffffff;
  font-weight: bold;
  font-size: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  box-sizing: content-box;
  background: linear-gradient(to right, #FF7956, #E60012);
  margin-left: 30rpx;
}
.transparent{
  color: transparent !important;
}