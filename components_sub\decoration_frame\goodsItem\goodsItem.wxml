<view class="goods-item {{config.goodsShadow ? 'shadow-' + config.goodsShadow : ''}} layout-{{config.layout}}-item" style="border-radius: {{config.goodsRadius}}rpx;background: {{config.goodsBg}};{{styles}}" data-item="{{item}}">
  <view class="goods-image" style="border-radius: {{config.goodsRadius}}rpx">
    <img lazy-load class="img" src="{{item.mImg||item.lImg}}" mode="aspectFill"></img>
    <img lazy-load wx:if="{{config.tag}}" class="goods-tag" src="{{config.tag}}"></img>
  </view>
  <view class="goods-info">
    <text class="goods-name {{config.goodsLine ? 'line-' + config.goodsLine : ''}}">
      <block wx:if="{{config.showPriceTag && (config.layout === '3' || config.layout === 'scroll')}}">
        <text wx:if="{{item.qiangId}}" class="price-tag">秒杀</text>
        <text wx:elif="{{item.memberPrice}}" class="price-tag">会员价</text>
      </block>
      <text>{{item.goodsName}}</text>
    </text>
    <view wx:if="{{config.showPromo&&!item.qiangPrice&&!item.memberPrice&&item.promoList&&item.promoList[0]&&item.promoList[0].promoName}}" class="goods-promo" style="{{config.layout==2&&config.showAdd?'max-width: calc(100% - 50rpx);':''}}">
      <text>{{item.promoList[0].promoName}}</text>
    </view>

    <view wx:if="{{config.showPrice}}" class="goods-price" style="color: {{config.priceColor}}">
      <block wx:if="{{config.showPriceTag && (config.layout === '1' || config.layout === '2')}}">
        <text wx:if="{{item.qiangId}}" class="price-tag">秒杀</text>
        <text wx:elif="{{item.memberPrice}}" class="price-tag">会员价</text>
      </block>
      <text class="current-price" style="color: {{config.goodsPriceColor}}">¥{{ item.groupBuyPrice>0?item.groupBuyPrice:item.qiangPrice>0?item.qiangPrice:item.memberPrice>0?item.memberPrice:item.salePrice>0?item.salePrice:item.scPrice}}</text>
      <block wx:if="{{config.showOriginalPrice}}">
        <text wx:if="{{item.groupBuyPrice>0&&item.groupBuyPrice<item.salePrice}}" class="original-price">¥{{item.salePrice}}</text>
        <text wx:elif="{{item.qiangPrice>0&&item.qiangPrice<item.salePrice}}" class="original-price">¥{{item.salePrice}}</text>
        <text wx:elif="{{item.memberPrice>0&&item.memberPrice<item.scPrice}}" class="original-price">¥{{item.scPrice}}</text>
        <text wx:elif="{{ item.scPrice>item.salePrice}}" class="original-price">¥{{item.scPrice}}</text>
      </block>
      <view wx:if="{{config.showAdd}}" class="plus sficon sf-a-close-circle-fill1" catchtap="openSku"></view>
    </view>
    <view wx:if="{{config.showSales && item.sales}}" class="goods-sales">已售{{item.sales}}</view>
    <view class="gooods-explanation" wx:if="{{item.message}}">{{item.message}}</view>
  </view>
</view>
