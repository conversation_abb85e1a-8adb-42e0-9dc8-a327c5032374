/* pages/distributershop/verifyImgDetail/verifyImgDetail.wxss */

.goodsDetailMiddle2 .goodMessage {
  padding: 20rpx;
  background: white;
  color: #353535;
  font-size: 30rpx;
  border-left: 4px solid red;
  border-bottom: 1px #efeff4 solid;
  text-align: center;
}

.goodsDetailMiddle2 .goodContent {
  padding: 0px 24rpx;
  background: white;
  font-size: 22rpx;
  color: #6c6b6a;
}

.goodsDetailMiddle2 .goodContent .content {
  padding: 15rpx 0px;
  float: left;
}

.goodsDetailMiddle2 .goodContent .cl {
  clear: both;
  width: 100%;
  border-bottom: 1px solid #f1f1f1;
}

.goodsDetailMiddle2 .morePull {
  font-size: 22rpx;
  color: #474747;
}

.footer {
  box-sizing: content-box;
  background: #fff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0 auto;
  width: 690rpx;
  padding: 0 30rpx;
  height: 170rpx;
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 0.5px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.footer .tip {
  width: 100%;
  text-align: center;
  color: #f00;
  padding: 10rpx 0;
  font-weight: 700;
}

.footer .btn {
  flex: 1;
  height: 70rpx;
  border-radius: 60rpx;
  text-align: center;
  background: #E60012;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.footer .btn.btn1 {
  background: #a0a0a0;
  border-radius: 60rpx 0 0 60rpx;
}

.footer .btn.btn2 {
  border-radius: 0 60rpx 60rpx 0;
}
