Component({
  properties: {
    full: {
      type: Boolean,
      value: false
    },
    zIndex: {
      type: Number,
      value: 9999
    },
    mask: {
      type: Boolean,
      value: false
    },
    auth: {
      type: Boolean,
      value: true
    },
    styles: {
      type: String,
      value: ''
    }
  },
  data: { // 这里是一些组件内部数据
    showLoading: true
  },
  attached: function() {
    //10秒后自动关闭loading 
    if (this.properties.full && this.properties.auth)
      this.data.timer = setTimeout(() => {
        this.setData({
          showLoading: false
        })
      }, 10000)
  },
  detached: function() {
    clearTimeout(this.data.timer)
  },
  methods: { // 这里是一个自定义方法
    preventD: function() {
      return
    }
  }
})
