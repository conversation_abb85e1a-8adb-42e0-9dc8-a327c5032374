const app = getApp()
Component({
  options: {
    addGlobalClass: true,
    virtualHost: true
  },
  properties: {
    styles: {
      type: String,
      value: ''
    },
    adGroupId: {
      type: String,
      value: ''
    },
    type: {
      type: Number,
      value: 1
    },
    track: {
      type: String,
      value: ''
    },
  },
  data: {
    list: [],
    height: 'auto'
  },
  attached() {
    this.getAd()
  },
  methods: {
    async imgload(e) {
      const rect = await app.util.getRect('#ad', false, this)
      let height = rect.width / e.detail.width * e.detail.height
      this.setData({
        height: height + 'px'
      })
    },
    /* 广告轮播传图点击跳转 */
    getAd() {
      if (!this.data.adGroupId) return
      app.reqGet('ms-sanfu-spi-common/listStaticAdDetail', {
        sid: wx.getStorageSync('sid'),
        shoId: wx.getStorageSync('dsho_id'),
        orgId: wx.getStorageSync('orgId'), // 主体标识
        adGroupId: this.data.adGroupId
      }, res => {
        if (res.success) {
          this.setData({
            adList: res.data
          })
        } else {
          // app.util.reqFail(res)
        }
      })
    },
    openAd(e) {
      if (!e.currentTarget.dataset.url) return
      if (e.currentTarget.dataset.url === 'https:/pages/fubishop/differentIndustry/differentIndustry') {
        wx.navigateTo({
          url: '/pages/fubishop/differentIndustry/differentIndustry',
        })
        return
      }
      app.toH5(e.currentTarget.dataset.url)
    },
  }
});
