let queue = [];
function getContext() {
  const pages = getCurrentPages();
  return pages[pages.length - 1];
}
const app = getApp()
const Share = options => {
  options = Object.assign({ isShowAlert: true, showFlag: true}, Share.currentOptions, options);
  return new Promise((resolve, reject) => {
    const context = options.context || getContext();
    const share = context.selectComponent(options.selector);
    delete options.selector;
    if (share) {
      share.setData(Object.assign({
        pengyouquan: function(){
          share.close()
          reject()
        }, weixin: function(){
          share.close()
          resolve()
        }
      }, options));
      // }
    }
    else {
      console.warn('未找到 share 节点，请确认 selector 及 context 是否正确');
    }
  });
};
Share.defaultOptions = {
  show: true,
  selector: '#sanfu-share'
};
Share.setDefaultOptions = options => {
  Object.assign(Share.currentOptions, options);
};
Share.resetDefaultOptions = () => {
  Share.currentOptions = Object.assign({}, Share.defaultOptions);
};
Share.resetDefaultOptions();
export default Share;
