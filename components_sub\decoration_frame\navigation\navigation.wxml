<wxs src="./expand.wxs" module="expand" />
<wxs src="./slide.wxs" module="slide" />
<!-- nav-item.wxml -->
<template name="nav-item">
  <view class="navigation-item menu-type-{{config.menuType}}" bindtap="handleItemClick" data-item="{{item}}">
    <view class="nav-image" style="padding: {{config.iconPadiding}}rpx;height: {{config.iW}}rpx;">
      <image src="{{item.imgUrl}}" style="width: 100%; height: {{config.imgW}}rpx;border-radius: {{config.iconRadius}}rpx;" mode="aspectFill" />
    </view>
    <view class="nav-title" style="font-size: {{config.titleSize}}rpx;">{{item.title}}</view>
  </view>
</template>
<view class="navigation-component" style="z-index:{{index+1}};padding: {{config.mTB}}rpx {{config.mLR}}rpx;">
  <view class="content" style="padding: {{config.pTB}}rpx {{config.pLR}}rpx;border-radius: {{config.bR}}rpx;background:{{config.bg}};">
    <!-- 平铺模式 -->
    <view wx:if="{{config.displayType === 'grid'}}" class="navigation-grid" style="grid-auto-flow: row;grid-template-columns: repeat({{config.itemsPerRow}}, minmax(0px, 1fr)); gap: {{config.rowGap}}rpx {{config.columnGap}}rpx;">
      <block wx:for="{{unitData.list}}" wx:key="index">
        <template is="nav-item" data="{{item, config}}" />
      </block>
    </view>
    <!-- 单行扩展模式 -->
    <view wx:if="{{config.displayType === 'expandSlide'}}" class="navigation-expand">
      <view class="expand-wrap" style="transform: translateX(0rpx);height: {{currentPage === 0 ? config.iH : config.iH * Math.ceil(unitData.list.length/config.itemsPerRow - 1) + Math.ceil(unitData.list.length/config.itemsPerRow - 2) * config.rowGap}}rpx; transition: all 0.3s ease-out;" bindtouchstart="{{expand.touchstart}}" bindtouchmove="{{expand.touchmove}}" bindtouchend="{{expand.touchend}}" config="{{config}}" dataLength="{{unitData.list.length}}" change:config="{{expand.configInit}}" change:dataLength="{{expand.dataLengthInit}}">

        <!-- 第一页 -->
        <view class="first-page" style="display: grid; grid-template-columns: repeat({{config.itemsPerRow}}, {{config.iW}}rpx); grid-column-gap: {{config.columnGap}}rpx; margin-right: {{config.showHalfFirst ? config.columnGap : 0}}rpx">
          <block wx:for="{{unitData.list}}" wx:key="index" wx:if="{{index < config.itemsPerRow}}">
            <template is="nav-item" data="{{item, config}}" />
          </block>
        </view>

        <!-- 后续页面 -->
        <view class="last-page" style="grid-template-columns: repeat({{config.itemsPerRow}}, {{config.iW}}rpx); grid-row-gap: {{config.rowGap}}rpx; min-width: {{750 - config.pLR * 2 - config.mLR * 2}}rpx">
          <block wx:for="{{unitData.list}}" wx:key="index" wx:if="{{index >= config.itemsPerRow}}">
            <template is="nav-item" data="{{item, config}}" />
          </block>
        </view>
      </view>

      <!-- 指示器 -->
      <view wx:if="{{config.showIndicator && unitData.list.length > config.itemsPerRow * 2}}" class="slider-indicator expandSlide">
        <block wx:for="{{2}}" wx:key="index">
          <view class="indicator-dot {{currentPage === index ? 'active' : ''}}" style="background: {{currentPage === index  ? config.barBtnColor : '' }}">
          </view>
        </block>
      </view>
    </view>

    <!-- 多行滑动模式 -->
    <block wx:if="{{config.displayType === 'multiRow'}}">
      <scroll-view class="navigation-multi-row" scroll-x enhanced show-scrollbar="{{false}}" bindscroll="{{slide.handleScroll}}" dataLength="{{unitData.list.length}}" change:dataLength="{{slide.dataLengthInit}}">
        <view class="navigation-multi-row-wrap">
          <view class="navigation-grid" style="grid-template-rows: repeat({{config.rowsPerPage}}, auto); gap: {{config.rowGap}}rpx {{config.columnGap}}rpx;  grid-auto-columns: {{config.iW}}rpx">
            <block wx:for="{{unitData.list}}" wx:key="index">
              <template is="nav-item" data="{{item, config}}" />
            </block>
          </view>
        </view>
      </scroll-view>

      <!-- 指示器 -->
      <view wx:if="{{config.showIndicator}}" class="scrollbar-track">
        <view class="scrollbar-thumb" style="background: {{config.barBtnColor}}">
        </view>
      </view>
    </block>
  </view>
</view>
