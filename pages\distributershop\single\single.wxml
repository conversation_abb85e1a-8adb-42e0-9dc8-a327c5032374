<!--pages/distributershop/single/single.wxml-->
<wxs src="../../../utils/utils.wxs" module="utils"></wxs>
<view style="position:fixed;top:0;left:0;width:100%;z-index:9; background:#fff;">
  <view class="bar-top">
    <view class="search-input">
      <text class="uni-icon uni-icon-search" style="font-size:36rpx;color:#999;margin-right:6rpx;" ></text>
      <input placeholder-style="color:#999;" bindfocus="inputsearch" bindinput="input" bindconfirm="search" type="text" data-name="search_keyword" value="{{search_keyword}}" placeholder="请输入商品关键词或6位货号" style="color: #404040;" confirm-type="search"/>
      <view style="height:100%;padding:0 12rpx;display:flex;align-items:center;background:#936eff;color:#fff;padding-right: 16rpx;" wx:if="{{searching}}" bindtap="closeinput">
        重置
      </view>
    </view>
    <view class="item" bindtap="search" style="font-weight: 700;">搜索</view>
  </view>
  <view class="top-nav">
    <view wx:for="{{bra_list}}" wx:key="index" style="{{bra_index==index?'color:#333;':''}}" data-index="{{index}}" bindtap="changeBar">{{item.name}}</view>
  </view>
  <order-by id="order" groups="{{groups}}" bind:orderBy="orderBy" />
</view>
<view style="height:220rpx"></view>
<sf-loading full wx:if="{{has_more == 4}}" />
<view class="single-items-list">
  <block wx:for="{{singles}}" wx:key="index" id="{{index}}">
    <view class="line-split" wx:if="{{index!=0}}"></view>
    <template data="{{item}}" id="{{index}}" is="single-item" />
  </block>
</view>
<view wx:if="{{has_more != 4}}" class="show_end" bindtap="onReachBottom">
  <view wx:if="{{has_more == 1}}" class="loadmore-icon"></view>
  <text>{{ loadingText[has_more] }}</text>
</view>

<!-- </view> -->

<!-- 分享组件 -->
<share id="sanfu-share" />
<uni-popup show="{{showDetail}}" closeName="单品推荐-查看全文弹框" zIndex="9999" type="bottom" bindchange="changeDetailStatus" styles="height:800rpx">
  <scroll-view class="show-detail-page" scroll-y>
    <view class="single-item">
      <view class="line2">
        <view class="top">适合群体</view>
        <text user-select="true" class="detail">{{showItem.fitGroup}}</text>
      </view>
      <view class="line3" style="margin-top:5px;">
        <view class="top" style="display: flex;align-items: center;">
          <text>推荐话术</text>
          <text style="margin-left: auto;color: #409EFF;padding: 20rpx 0 20rpx 80rpx;" bindtap="copy">一键复制</text>
         </view>
        <text user-select="true" class="detail">{{showItem.recommendTxt}}</text>
      </view>
      <view class="go-back" bindtap="changeDetailStatus">
        返回
      </view>
    </view>
  </scroll-view>
</uni-popup>


<!-- 单品推荐列表item -->
<template name='single-item'>
  <view class="single-item">
    <view class="line1">
      <image lazy-load  class="image" src='{{item.baseImg&&utils.jpg2jpeg(item.baseImg) || "https://img.sanfu.com/sf_access/uploads/yc1XJeFKUfFDQw2O0D3l6SFDOEM0IFEf.jpg"}}'>
        <text class="single-date">{{item.publishTime}}</text>
      </image>
      <view class="item-detail">
        <view class="title">{{item.goodsName}} {{item.gooId}}</view>
        <view class="price">
          <text style="font-weight:700">￥{{item.salePrice}}</text>
          <!-- <text class="sub-icon">立减</text> -->
        </view>
        <view class="viewer">
          <view class="viewer-stock">
            {{item.stockAmount!=-9999999?'门店库存：'+item.stockAmount:'非本店商品'}}
          </view>
          <view class="viewer-count">
            <text class="f7 iconDG-liulan"></text>
            <text>{{item.viewCnt || 0}}</text>
          </view>
          <view class="viewer-count" bindtap="showShare" data-shareItem="{{item}}">
            <text class="f7 iconDG-fenxiang" style="color:#F63979"></text>
            <text>{{item.shareCnt || 0}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="line2">
      <view class="top">适合群体</view>
      <text user-select="true" class="bottom">{{item.fitGroup}}</text>
    </view>
    <view class="line3">
      <view class="top">推荐话术</view>
      <text user-select="true" class="bottom">{{item.recommendTxt}}</text>
    </view>
    <view class="show-detail" bindtap="changeDetailStatus" data-showItem="{{item}}">
      查看全文>
    </view>
  </view>
</template>
