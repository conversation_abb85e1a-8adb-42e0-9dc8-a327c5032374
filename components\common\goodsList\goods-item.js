const app = getApp()
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  options: {
    addGlobalClass: true,
    multipleSlots: true,
    virtualHost: true
  },
  /**
   * 商品组件（单个)
   * @property {Number} theme   卡片展示类型
   * @value 1 横卡片
   * @value 2 竖卡片 
   * @property {Boolean} big   横卡片时有效  是否放大  1 / 2 / 3
   * @property {Number} rowNum 竖卡片时  每排展示个数  默认2  
   * @value 2-4
   * @property {Number} margin 竖卡片时  左间距  默认20
   * @property {String} title 商品标题 
   * @property {Number} titleLine 商品标题展示行数
   * @value 1 、 2 、3（不限制）
   * @property {String} img  图片地址
   * @property {Number} salePrice 售价 
   * 传入时判断全国平均价salePrice="{{item.shoId?item.salePrice:item.avgSalePrice}}"
   * @property {Number} memberPrice会员一口价（无促销和标签时展示-运营要求）
   * memberPrice="{{item.goodsLabelList.length == 0&&item.promoList.length==0&&item.memberPrice}}"
   * @property {Number} qiangPrice 秒杀价
   * @property {Number} groupBuyPrice 拼团价
   * @property {Number} scPrice 原价，标价
   * @property {String} styles 卡片样式
   * @property {String} imgStyle 图片样式
   * @property {String} priceStyle 图片样式
   * @property {showTag} 
   * @property {showTag} gooMainStyle  样式
   * 
   * @property {c} tag  商品标签
   * @slot {img} 图片内水印
   * @slot {afterTitle} 
   * @slot {beforeTitle} 
   * afterPrice
   * bottom
   */
  properties: {
    big: {
      type: Number,
      value: 2
    },
    theme: {
      type: Number,
      observer: function(value) {
        ['1', '2', '3'].indexOf(value + '') == -1 && this.setData({
          theme: 2
        })
      },
      value: 2
    },
    h: {
      type: Number,
      value: 1.33
    },
    rowNum: {
      type: Number,
      observer: (value) => {
        ['2', '3', '4'].indexOf(value + '') == -1 && this.setData({
          theme: 2
        })
      },
      value: 2
    },
    margin: {
      type: Number
    },
    img: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    titleLine: {
      type: Number,
      observer: (value) => {
        ['1', '2', '3'].indexOf(value + '') == -1 && this.setData({
          titleLine: 2
        })
      },
      value: 2
    },
    showTag: {
      type: Boolean,
      value: true
    },
    salePrice: {
      type: Number,
      value: ''
    },
    memberPrice: {
      type: Number,
      value: ''
    },
    qiangPrice: {
      type: Number,
      value: ''
    },
    groupBuyPrice: {
      type: Number,
      value: ''
    },
    scPrice: {
      type: Number,
      value: ''
    },
    tag: {
      type: String,
      value: ''
    },
    styles: {
      type: String,
      value: ''
    },
    gooMainStyle: {
      type: String,
      value: ''
    },
    imgStyle: {
      type: String,
      value: ''
    },
    priceStyle: {
      type: String,
      value: ''
    },
    isObserver: {
      type: Boolean,
      value: false
    },
    key: {
      type: String,
      value: ''
    },
    imgboxStyle: {
      type: String,
      value: ''
    }
  },
  data: {
    dsho_id: ''
  },
  attached() {
    this.setData({
      dsho_id: wx.getStorageSync('dsho_id'),
    })
  },
  watch: {
    'title': function() {
      this.ob && this.ob.disconnect && this.ob.reconnect()
    },
    'groupBuyPrice': function(newVal) {
      if (!this.data.dsho_id) {
        this.setData({
          dsho_id: wx.getStorageSync('dsho_id')
        })
      }
    },
    'memberPrice': function(newVal) {
      if (!this.data.dsho_id) {
        this.setData({
          dsho_id: wx.getStorageSync('dsho_id')
        })
      }
    },
    'salePrice': function(newVal) {
      if (!this.data.dsho_id) {
        this.setData({
          dsho_id: wx.getStorageSync('dsho_id')
        })
      }
    },
    'qiangPrice': function(newVal) {
      if (!this.data.dsho_id) {
        this.setData({
          dsho_id: wx.getStorageSync('dsho_id')
        })
      }
    }
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show: function() {
      if (!this.data.dsho_id) {
        this.setData({
          dsho_id: wx.getStorageSync('dsho_id')
        })
      }
    },
    hide: function() {},
    resize: function() {},
  },
  methods: {
    observer() {
      this.triggerEvent('observer', this.data.key);
    }
  }
});
